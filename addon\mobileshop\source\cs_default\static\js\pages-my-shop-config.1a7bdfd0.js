(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-shop-config"],{7188:function(t,e,a){"use strict";a.r(e);var i=a("a73d4"),o=a("e762");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("c0a0d");var r=a("828b"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"6fea54b0",null,!1,i["a"],void 0);e["default"]=s.exports},"7cb1":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5");var i=a("e5a3"),o={data:function(){return{shopInfo:{}}},onShow:function(){},onLoad:function(){this.$util.checkToken("/pages/my/shop/config")&&(this.shopInfo=uni.getStorageSync("shop_info")?JSON.parse(uni.getStorageSync("shop_info")):{})},computed:{formData:function(){return{logo:this.shopInfo.logo,logo_square:this.shopInfo.logo_square,avatar:this.shopInfo.avatar,banner:this.shopInfo.banner,seo_description:this.shopInfo.seo_description,seo_keywords:this.shopInfo.seo_keywords,site_name:this.shopInfo.site_name}}},methods:{save:function(){var t=this;(0,i.setShopConfig)(this.formData).then((function(e){t.$util.showToast({title:e.message}),e.code>=0&&(uni.setStorageSync("shop_info",JSON.stringify(t.shopInfo)),setTimeout((function(){uni.navigateBack({delta:1})}),1e3))}))},uplodImg:function(t){var e=this;this.$util.upload({number:1,path:"image"},(function(a){a&&(e.$util.showToast({title:"上传成功"}),"logo"==t?e.shopInfo.logo=a[0]:"logo_square"==t?e.shopInfo.logo_square=a[0]:"avatar"==t?e.shopInfo.avatar=a[0]:"banner"==t&&(e.shopInfo.banner=a[0]))}))},delImg:function(t){"logo"==t?this.shopInfo.logo="":"logo_square"==t?this.shopInfo.logo_square="":"avatar"==t?this.shopInfo.avatar="":"banner"==t&&(this.shopInfo.banner="")},previewMedia:function(t){var e=[this.$util.img(this.shopInfo[t])];uni.previewImage({current:0,urls:e})},imgError:function(t){this.shopInfo[t]=this.$util.img(this.$util.getDefaultImage().default_headimg),this.$forceUpdate()}}};e.default=o},"7cf8":function(t,e,a){var i=a("e536");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("84229132",i,!0,{sourceMap:!1,shadowMode:!1})},a73d4:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"iphone-safe-area"},[a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("网站名称")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入网站名称",maxlength:"100"},model:{value:t.shopInfo.site_name,callback:function(e){t.$set(t.shopInfo,"site_name",e)},expression:"shopInfo.site_name"}})],1),a("v-uni-view",{staticClass:"form-wrap goods-img"},[a("v-uni-text",{staticClass:"label"},[t._v("网站logo")]),a("v-uni-view",{staticClass:"img-list"},[a("v-uni-view",{staticClass:"add logo",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uplodImg("logo")}}},[t.shopInfo.logo?a("v-uni-image",{attrs:{src:t.$util.img(t.shopInfo.logo),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError("logo")},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.previewMedia("logo")}}}):a("v-uni-text",{staticClass:"iconfont iconadd1"}),t.shopInfo.logo?a("v-uni-view",{staticClass:"del-wrap iconfont iconclose",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.delImg("logo")}}}):t._e()],1),a("v-uni-view",{staticClass:"tips"},[t._v("建议图片尺寸：200*60")])],1)],1),a("v-uni-view",{staticClass:"form-wrap goods-img"},[a("v-uni-text",{staticClass:"label"},[t._v("方形logo")]),a("v-uni-view",{staticClass:"img-list"},[a("v-uni-view",{staticClass:"add logo",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uplodImg("logo_square")}}},[t.shopInfo.logo_square?a("v-uni-image",{attrs:{src:t.$util.img(t.shopInfo.logo_square),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError("logo_square")},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.previewMedia("logo_square")}}}):a("v-uni-text",{staticClass:"iconfont iconadd1"}),t.shopInfo.logo_square?a("v-uni-view",{staticClass:"del-wrap iconfont iconclose",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.delImg("logo_square")}}}):t._e()],1),a("v-uni-view",{staticClass:"tips"},[t._v("建议图片尺寸：80*80")])],1)],1),a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("网站描述")]),a("v-uni-textarea",{staticClass:"uni-input font-size-base",attrs:{placeholder:"请输入网站描述",maxlength:"300","auto-height":"true"},model:{value:t.shopInfo.seo_description,callback:function(e){t.$set(t.shopInfo,"seo_description",e)},expression:"shopInfo.seo_description"}})],1),a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label",staticStyle:{"align-self":"flex-start"}},[t._v("网站关键字")]),a("v-uni-view",{staticClass:"flex_1"},[a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入网站关键字,用逗号隔开",maxlength:"100"},model:{value:t.shopInfo.seo_keywords,callback:function(e){t.$set(t.shopInfo,"seo_keywords",e)},expression:"shopInfo.seo_keywords"}}),a("v-uni-view",{staticClass:"tips font-size-activity-tag color-tip padding-bottom"},[t._v('多个关键字之间请用英文","隔开')])],1)],1)],1),a("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save()}}},[t._v("保存")])],1)},o=[]},c0a0d:function(t,e,a){"use strict";var i=a("7cf8"),o=a.n(i);o.a},e536:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-6fea54b0]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-6fea54b0]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-6fea54b0]{position:fixed;left:0;right:0;z-index:998}.container-wrap[data-v-6fea54b0]{margin-bottom:%?60?%}.item-wrap[data-v-6fea54b0]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-6fea54b0]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;min-height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-6fea54b0]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-6fea54b0]{font-weight:700}.item-wrap .form-wrap .label[data-v-6fea54b0]{min-width:%?150?%;vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap .time-change[data-v-6fea54b0]{display:flex;align-items:center;flex:1;justify-content:flex-end}.item-wrap .form-wrap uni-textarea[data-v-6fea54b0],\r\n.item-wrap .form-wrap .picker[data-v-6fea54b0],\r\n.item-wrap .form-wrap uni-input[data-v-6fea54b0]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap .picker .iconfont[data-v-6fea54b0]{vertical-align:middle}.item-wrap .form-wrap uni-textarea[data-v-6fea54b0]{height:%?100?%;padding:%?20?%}.item-wrap .form-wrap .value[data-v-6fea54b0]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap.more-wrap .selected[data-v-6fea54b0]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-6fea54b0]{color:#303133}.item-wrap .form-wrap.more-wrap .flex_1[data-v-6fea54b0]{flex:1;text-align:right;padding-right:%?20?%}.item-wrap .form-wrap.more-wrap .flex_1 uni-input[data-v-6fea54b0]{height:%?100?%;display:block}.item-wrap .form-wrap.more-wrap .iconfont[data-v-6fea54b0]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.goods-img[data-v-6fea54b0]{display:flex}.item-wrap .form-wrap.goods-img .label[data-v-6fea54b0]{align-self:flex-start;margin-top:%?20?%}.item-wrap .form-wrap.goods-img .img-list[data-v-6fea54b0]{padding-top:%?40?%;padding-bottom:%?40?%;padding-left:%?40?%}.item-wrap .form-wrap.goods-img .img-list .add[data-v-6fea54b0]{position:relative;width:%?140?%;text-align:center;border:1px dashed #ccc;font-weight:700;color:#909399}.item-wrap .form-wrap.goods-img .img-list .add .iconfont[data-v-6fea54b0]{font-size:%?40?%}.item-wrap .form-wrap.goods-img .img-list .add.logo[data-v-6fea54b0]{height:%?84?%;line-height:%?84?%}.item-wrap .form-wrap.goods-img .img-list .add.avatar[data-v-6fea54b0]{height:%?140?%;line-height:%?140?%}.item-wrap .form-wrap.goods-img .img-list .add.banner[data-v-6fea54b0]{height:%?120?%;line-height:%?120?%}.item-wrap .form-wrap.goods-img .img-list .add uni-image[data-v-6fea54b0]{width:100%;height:100%}.item-wrap .form-wrap.goods-img .img-list .add .del-wrap[data-v-6fea54b0]{position:absolute;top:%?-16?%;right:%?-16?%;line-height:1;width:16px;height:16px;background-color:rgba(0,0,0,.5);border-radius:50%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;font-size:12px;color:#fff;font-weight:700}.item-wrap .form-wrap.goods-img .tips[data-v-6fea54b0]{color:#909399;font-size:%?20?%;margin-top:%?20?%;word-wrap:break-word;word-break:break-all}.footer-wrap[data-v-6fea54b0]{width:100%;padding:%?40?% 0;z-index:10;padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.value[data-v-6fea54b0]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}uni-button[data-v-6fea54b0]{margin-top:%?40?%}',""]),t.exports=e},e5a3:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getCaptchaConfig=function(){return o.default.get("/shopapi/config/captchaConfig")},e.getGoodsConfig=function(){return o.default.get("/shopapi/goods/config")},e.getOrderConfig=function(){return o.default.get("/shopapi/order/config")},e.setGoodsConfig=function(t){return o.default.post("/shopapi/goods/setconfig",{data:t})},e.setOrderConfig=function(t){return o.default.post("/shopapi/order/setconfig",{data:t})},e.setShopConfig=function(t){return o.default.post("/shopapi/shop/config",{data:t})};var o=i(a("9027"))},e762:function(t,e,a){"use strict";a.r(e);var i=a("7cb1"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a}}]);