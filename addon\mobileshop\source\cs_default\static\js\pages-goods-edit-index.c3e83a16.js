(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-edit-index"],{"0873":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getOrderFormList=function(){return i.default.get("/form/shopapi/form/lists")};var i=o(a("9027"))},"0e46":function(t,e,a){"use strict";var o=a("2249"),i=a.n(o);i.a},"1ec2":function(t,e,a){var o=a("2ec4");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=a("967d").default;i("13c3ea3a",o,!0,{sourceMap:!1,shadowMode:!1})},2249:function(t,e,a){var o=a("7dbd");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=a("967d").default;i("49fbfbec",o,!0,{sourceMap:!1,shadowMode:!1})},"2ec4":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-5d98d408]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-5d98d408]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-5d98d408]{position:fixed;left:0;right:0;z-index:998}.container[data-v-5d98d408]{padding-bottom:%?40?%}.safe-area[data-v-5d98d408]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.goods-edit-wrap[data-v-5d98d408]{margin-bottom:%?160?%}.form-title[data-v-5d98d408]{display:flex;justify-content:space-between;margin:%?20?% %?30?%;color:#909399}.item-wrap uni-radio .uni-radio-input[data-v-5d98d408]{width:%?30?%!important;height:%?30?%!important}.item-wrap[data-v-5d98d408]{background:#fff;margin-top:%?20?%}.item-wrap .goods-type[data-v-5d98d408]{display:flex;margin:0 %?40?% %?20?% %?40?%;flex-wrap:wrap}.item-wrap .goods-type uni-view[data-v-5d98d408]{flex:1;text-align:center;border:1px solid #ccc;color:#909399;margin-right:%?40?%;margin-top:%?30?%;position:relative;height:%?80?%;line-height:%?80?%;white-space:nowrap;min-width:calc((100% - %?100?%) / 3);max-width:calc((100% - %?100?%) / 3)}.item-wrap .goods-type uni-view[data-v-5d98d408]:nth-child(3n+3){margin-right:0}.item-wrap .goods-type uni-view .iconfont[data-v-5d98d408]{display:none}.item-wrap .goods-type uni-view.selected .iconfont[data-v-5d98d408]{display:block;position:absolute;bottom:%?-22?%;right:%?-22?%;font-size:%?80?%}.item-wrap .form-wrap[data-v-5d98d408]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-5d98d408]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-5d98d408]{font-weight:700}.item-wrap .form-wrap .label[data-v-5d98d408]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap uni-input[data-v-5d98d408]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-5d98d408]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-5d98d408]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-5d98d408]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.more-wrap .action[data-v-5d98d408]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.item-wrap .form-wrap.goods-img[data-v-5d98d408]{height:%?200?%;line-height:%?200?%;display:block;position:relative}.item-wrap .form-wrap.goods-img .label[data-v-5d98d408]{display:inline-block}.item-wrap .form-wrap.goods-img .img-list[data-v-5d98d408]{position:absolute;width:80%;top:0;left:%?100?%;margin-top:%?40?%;margin-left:%?40?%}.item-wrap .form-wrap.goods-img .tips[data-v-5d98d408]{color:#909399;font-size:%?20?%;margin-top:%?20?%}.item-wrap .form-wrap .unit[data-v-5d98d408]{margin-left:%?20?%;width:%?40?%}.item-wrap .form-wrap.join-member-discount .label[data-v-5d98d408]{flex:1}.item-wrap .form-wrap.validity-type[data-v-5d98d408]{border-bottom:1px solid #eee!important}.footer-wrap[data-v-5d98d408]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;background-color:#fff}.popup[data-v-5d98d408]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-5d98d408]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-5d98d408]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-5d98d408]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-5d98d408]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-5d98d408]{height:calc(100% - %?270?%)}.popup.category[data-v-5d98d408]{height:50vh}.popup.category .popup-header[data-v-5d98d408]{border-bottom:none}.popup.category .popup-body[data-v-5d98d408]{padding:0 %?30?%}.popup.category .popup-body .nav[data-v-5d98d408]{border-bottom:%?2?% solid #eee;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.popup.category .popup-body .nav uni-text[data-v-5d98d408]{padding:0 0 %?20?% 0;margin-right:%?20?%;display:inline-block}.popup.category .popup-body .nav uni-text[data-v-5d98d408]:last-child{padding-right:0}.popup.category .popup-body .nav uni-text.selected[data-v-5d98d408]{border-bottom:2px solid}.popup.category .popup-body .category[data-v-5d98d408]{height:100%}.popup.category .popup-body .category .item[data-v-5d98d408]{display:block;margin:%?20?% 0 0}.popup.category .popup-body .category .item uni-text[data-v-5d98d408]:first-child{overflow:hidden;text-overflow:ellipsis;white-space:pre;width:90%;display:inline-block;vertical-align:middle}.popup.category .popup-body .category .item .iconfont[data-v-5d98d408]{float:right}.popup.category .popup-body .category .child-item[data-v-5d98d408]{display:flex;justify-content:space-between;margin-left:%?40?%}.popup.choose-picture[data-v-5d98d408]{background-color:#f8f8f8}.popup.choose-picture .popup-header[data-v-5d98d408]{border-bottom:none;background-color:#fff}.popup.choose-picture .popup-body[data-v-5d98d408]{background-color:#f8f8f8;height:auto}.popup.choose-picture .popup-body .select-wrap[data-v-5d98d408]{background-color:#fff;padding:0 %?30?%}.popup.choose-picture .popup-body .item[data-v-5d98d408]{text-align:center;padding:%?20?%;background-color:#fff;border-bottom:1px solid #eee}.popup.choose-picture .popup-body .item[data-v-5d98d408]:last-child{border-bottom:none}.popup.choose-picture .popup-body .item.cancle[data-v-5d98d408]{margin-top:%?20?%}',""]),t.exports=e},"3dcf":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("5c47"),a("a1c1"),a("d4b5"),a("5ef2"),a("c9b5"),a("ab80"),a("e838"),a("aa9c"),a("e966"),a("0506");var i=o(a("2634")),s=o(a("2fdc")),r=a("95a1"),n=a("0873"),d=a("c998"),l={data:function(){return{repeatFlag:!1,isIphoneX:!1,goodsImgHeight:165,isAWait:!1,albumPage:"goodsEdit",shopCategoryNumber:1,shopCategoryData:{store_0:{}},shopCategoryIndex:"",categoryList:[],secondCategory:[],thirdCategory:[],categoryId:[0,0,0],categoryName:["","",""],currentLevel:1,lastLevel:1,showFisrt:!0,showSecond:!1,showThird:!1,goodsData:{goods_id:0,sku_id:0,goods_class:1,goods_name:"",introduction:"",category_id:0,category_name:"",goods_image:[],keywords:"",brand_id:0,brand_name:"",virtual_indate:"",goods_spec_format:[],goods_sku_data:[],spec_type_status:0,goods_shop_category_ids:"",price:"",market_price:"",cost_price:"",weight:"",volume:"",sku_no:"",goods_stock:"",goods_stock_alarm:"",goods_content:"",is_free_shipping:1,shipping_template:0,template_name:"",max_buy:"",min_buy:"",unit:"",goods_state:1,goods_attr_class:0,goods_attr_name:"",goods_attr_format:[],virtual_sale:0,is_consume_discount:0,recommend_way:0,is_need_verify:0,verify_validity_type:0,verify_num:1,is_limit:0,limit_type:1,sale_show:0,stock_show:0,market_price_show:0,barrage_show:0,virtual_deliver_type:"auto_deliver",virtual_receive_type:"auto_receive",goods_form_index:0,supply_index:0},currCategory:0,recommendArray:["无","新品","精品","热卖"],virtualDeliverArray:[{name:"自动发货",value:"auto_deliver"},{name:"手动发货",value:"artificial_deliver"},{name:"到店核销",value:"verify"}],virtualDeliverValue:{auto_deliver:"自动发货",artificial_deliver:"手动发货",verify:"到店核销"},virtualReceiveArray:[{name:"自动收货",value:"auto_receive"},{name:"买家确认收货",value:"artificial_receive"}],virtualReceiveValue:{auto_receive:"自动收货",artificial_receive:"买家确认收货"},validityTypeArray:["永久","购买后几日有效","指定过期日期"],virtualIndate:0,virtualTime:"",minDate:"",carmiLength:"添加卡密",limitLength:[{name:"单次限购",value:"1"},{name:"长期限购",value:"2"}],current:0,goodsFormArray:[],goodsForm:[],supplyFormArray:[],supplyForm:[]}},onLoad:function(t){var e=this;return(0,s.default)((0,i.default)().mark((function a(){var o;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.goodsData.goods_id=t.goods_id||0,e.$util.checkToken("/pages/goods/edit/index?goods_id="+e.goodsData.goods_id)){a.next=3;break}return a.abrupt("return");case 3:if(e.clearStoreage(),e.getCategoryTreeFn(),!e.goodsData.goods_id){a.next=12;break}return e.isAWait=!0,uni.setNavigationBarTitle({title:"编辑商品"}),a.next=10,e.editGetGoodsInfo();case 10:a.next=14;break;case 12:e.isAWait=!1,uni.setNavigationBarTitle({title:"添加商品"});case 14:o=new Date,e.minDate=o.getFullYear()+"-"+o.getMonth()+"-"+o.getUTCDate(),e.addonIsExit.form&&e.getGoodsForm(),e.addonIsExit.supply&&e.getSupplyList();case 18:case"end":return a.stop()}}),a)})))()},onShow:function(){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.refreshData()},methods:{editGetGoodsInfo:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var a,o;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,r.getGoodsInfoById)(t.goodsData.goods_id);case 2:a=e.sent,0==a.code&&a.data?(o=a.data,o.goods_category.forEach((function(e,a){t.shopCategoryData["store_"+a]={category_id:e.id,category_name:e.category_name}})),t.shopCategoryNumber=o.goods_category.length,o.category_id=o.goods_category[0].id,o.category_name=o.goods_category[0].category_name.replace(/\//g," / "),"string"==typeof o.category_id?(t.categoryId=o.category_id.split(","),t.categoryName=o.category_name.split(" / ")):(t.categoryId=o.category_id,t.categoryName=o.category_name),delete o.category_json,delete o.goods_category,o.goods_image=o.goods_image.split(","),o.goods_sku_data.forEach((function(t){t.sku_spec_format&&(t.sku_spec_format=JSON.parse(t.sku_spec_format))})),o.goods_spec_format?(uni.setStorageSync("editGoodsSpecFormat",o.goods_spec_format),uni.setStorageSync("editGoodsSkuData",JSON.stringify(o.goods_sku_data)),o.goods_spec_format=JSON.parse(o.goods_spec_format)):(o.sku_id=o.goods_sku_data[0].sku_id,o.price=o.goods_sku_data[0].price,o.market_price=o.goods_sku_data[0].market_price,o.cost_price=o.goods_sku_data[0].cost_price,o.weight=o.goods_sku_data[0].weight,o.volume=o.goods_sku_data[0].volume,o.sku_no=o.goods_sku_data[0].sku_no),1==o.goods_class?(delete o.virtual_indate,uni.setStorageSync("editGoodsShippingTemplateId",o.shipping_template),uni.setStorageSync("editGoodsShippingTemplateName",o.template_name?o.template_name:"")):(delete o.shipping_template,delete o.is_free_shipping),o.goods_attr_format&&(uni.setStorageSync("editGoodsAttrClass",o.goods_attr_class),uni.setStorageSync("editGoodsAttrName",o.goods_attr_name),uni.setStorageSync("editGoodsAttrFormat",o.goods_attr_format),o.goods_attr_format=JSON.parse(o.goods_attr_format)),uni.setStorageSync("editGoodsState",o.goods_state),uni.setStorageSync("editGoodsContent",o.goods_content),1==o.verify_validity_type?t.virtualIndate=o.virtual_indate:2==o.verify_validity_type&&(t.virtualTime=t.$util.timeStampTurnTime(o.virtual_indate,"Y-m-d")),o.verify_num=o.goods_sku_data[0].verify_num,t.goodsData=o,t.goodsData.goods_form_index=0,t.goodsData.supply_index=0,t.$forceUpdate()):(t.$util.showToast({title:"商品不存在"}),setTimeout((function(){t.$util.redirectTo("/pages/goods/list",{},"redirectTo")}),1e3));case 4:case"end":return e.stop()}}),e)})))()},openGoodsCategoryPop:function(t){var e=this;this.currCategory=t,this.shopCategoryData["store_"+t].category_id?(this.categoryId=this.shopCategoryData["store_"+t].category_id.split(","),this.categoryName=this.shopCategoryData["store_"+t].category_name.split(" / "),this.categoryList.forEach((function(t,a){t.selected=-1!=e.categoryId.indexOf(t.category_id.toString()),t.selected&&(e.secondCategory=t.child_list),t.child_list&&(t.selected&&(e.lastLevel=2),t.child_list.forEach((function(t){t.selected=-1!=e.categoryId.indexOf(t.category_id.toString()),t.selected&&(e.thirdCategory=t.child_list)})))})),this.changeShow(this.categoryId.length)):(this.categoryId=[0],this.categoryName=[""],this.changeShow(1)),this.$refs.categoryPopup.open()},closeGoodsCategoryPop:function(){this.$refs.categoryPopup.close()},openGoodsSpec:function(){this.$util.redirectTo("/pages/goods/edit/spec")},openGoodsSpecEdit:function(){this.$util.redirectTo("/pages/goods/edit/spec_edit",{goods_class:this.goodsData.goods_class,virtual_deliver_type:this.goodsData.virtual_deliver_type})},openCarmichaelEdit:function(){this.$util.redirectTo("/pages/goods/edit/carmichael_edit",{goods_class:this.goodsData.goods_class})},openGoodsState:function(){this.$util.redirectTo("/pages/goods/edit/state",{goods_state:this.goodsData.goods_state})},openExpressFreight:function(){this.$util.redirectTo("/pages/goods/edit/express_freight",{template_id:this.goodsData.shipping_template})},openGoodsContent:function(){this.$util.redirectTo("/pages/goods/edit/content")},openAttr:function(){this.$util.redirectTo("/pages/goods/edit/attr")},refreshGoodsImgHeight:function(t){var e=this;if(""!=t.height){var a=parseFloat(t.height.replace("px",""));this.goodsImgHeight=a+80,this.$forceUpdate(),t.isLoad&&this.$refs.loadingCover&&setTimeout((function(){e.$refs.loadingCover.hide()}),100),uni.removeStorageSync("selectedAlbumImg")}},getCategoryTreeFn:function(){var t=this;(0,r.getCategoryTree)().then((function(e){e.data&&(t.categoryList=e.data,t.categoryList.forEach((function(e,a){e.selected=-1!=t.categoryId.indexOf(e.category_id.toString()),e.selected&&(t.secondCategory=e.child_list,t.currentLevel=1),e.child_list&&(e.selected&&(t.lastLevel=2),e.child_list.forEach((function(e){e.selected=-1!=t.categoryId.indexOf(e.category_id.toString()),e.selected&&(t.thirdCategory=e.child_list,t.currentLevel=2),e.child_list&&(e.selected&&(t.lastLevel=3),e.child_list.forEach((function(e){e.selected=-1!=t.categoryId.indexOf(e.category_id.toString()),e.selected&&(t.currentLevel=3)})))})))})),t.changeShow(t.lastLevel),0==t.goodsData.goods_id&&t.$refs.loadingCover&&t.$refs.loadingCover&&t.$refs.loadingCover.hide())}))},changeShow:function(t){1==t?(this.showFisrt=!0,this.showSecond=!1,this.showThird=!1):2==t?(this.showFisrt=!1,this.showSecond=!0,this.showThird=!1):3==t&&(this.showFisrt=!1,this.showSecond=!1,this.showThird=!0),this.currentLevel=t,this.$forceUpdate()},selectCategory:function(t){var e=this;this.currentLevel=t.level,1==t.level&&this.categoryId[0]>0&&this.categoryId[0]!=t.captcha_id?(this.categoryId[1]=0,this.categoryName[1]="",this.categoryId[2]=0,this.categoryName[2]=""):2==t.level&&this.categoryId[1]>0&&this.categoryId[1]!=t.captcha_id&&(this.categoryId[2]=0,this.categoryName[2]=""),this.categoryId[t.level-1]=t.category_id,this.categoryName[t.level-1]=t.category_name,1==t.level?t.child_list?this.secondCategory=t.child_list:(this.categoryId[1]=0,this.categoryName[1]="",this.categoryId[2]=0,this.categoryName[2]=""):2==t.level&&(t.child_list?this.thirdCategory=t.child_list:(this.categoryId[2]=0,this.categoryName[2]="")),this.lastLevel=1,this.categoryList.forEach((function(t,a){t.selected=e.categoryId[0]==t.category_id,t.child_list&&(t.selected&&(e.lastLevel=2),t.child_list.forEach((function(t,a){t.selected=e.categoryId[1]==t.category_id,t.child_list&&t.selected&&(e.lastLevel=3)})))})),this.changeShow(this.lastLevel),this.goodsData.category_id=[],this.goodsData.category_name=[];for(var a=0;a<this.categoryId.length;a++)this.categoryId[a]&&this.goodsData.category_id.push(this.categoryId[a]);for(a=0;a<this.categoryName.length;a++)this.categoryName[a]&&this.goodsData.category_name.push(this.categoryName[a]);this.goodsData.category_id=this.goodsData.category_id.toString(),this.goodsData.category_name=this.goodsData.category_name.join(" / "),(3==this.lastLevel&&this.categoryId[2]||2==this.lastLevel&&this.categoryId[1]||1==this.lastLevel&&this.categoryId[0])&&(this.shopCategoryData["store_"+this.currCategory]={category_id:this.goodsData.category_id,category_name:this.goodsData.category_name},this.closeGoodsCategoryPop()),this.$forceUpdate()},addShopCategory:function(){10!=this.shopCategoryNumber?(this.shopCategoryData["store_"+this.shopCategoryNumber]={},++this.shopCategoryNumber):this.$util.showToast({title:"商品可以属于多个分类，最多10个"})},deleteShopCategory:function(t){delete this.shopCategoryData["store_"+t],--this.shopCategoryNumber;var e=0,a={};for(var o in this.shopCategoryData)a["store_"+e]=this.shopCategoryData[o],e++;this.shopCategoryData={},this.shopCategoryData=Object.assign(this.shopCategoryData,a)},refreshData:function(){var t=this,e=uni.getStorageSync("selectedAlbumImg");e&&(uni.setStorageSync("selectedAlbumImgTemp",e),e=JSON.parse(e),this.goodsData.goods_image=e.list.split(","),this.$refs.goodsShmilyDragImg.refresh()),this.goodsData.goods_spec_format=uni.getStorageSync("editGoodsSpecFormat")?JSON.parse(uni.getStorageSync("editGoodsSpecFormat")):[],this.goodsData.goods_spec_format.length<=0&&(this.goodsData.carmichael=uni.getStorageSync("editGoodsCarmichael")?JSON.parse(uni.getStorageSync("editGoodsCarmichael")):[],this.goodsData.carmichael.length>0&&(this.carmiLength="添加卡密【"+this.goodsData.carmichael.length+"】")),this.goodsData.goods_sku_data=uni.getStorageSync("editGoodsSkuData")?JSON.parse(uni.getStorageSync("editGoodsSkuData")):[],this.goodsData.goods_sku_data.length>0&&(this.goodsData.goods_stock=0,this.goodsData.goods_stock_alarm=0,this.goodsData.goods_sku_data.forEach((function(e){e.stock&&(t.goodsData.goods_stock+=parseInt(e.stock)),e.stock_alarm&&(t.goodsData.goods_stock_alarm+=parseInt(e.stock_alarm))}))),this.goodsData.shipping_template=uni.getStorageSync("editGoodsShippingTemplateId")||0,this.goodsData.is_free_shipping=this.goodsData.shipping_template>0?0:1,this.goodsData.template_name=uni.getStorageSync("editGoodsShippingTemplateName")||"",void 0!==uni.getStorageSync("editGoodsState")&&""!==uni.getStorageSync("editGoodsState")&&(this.goodsData.goods_state=uni.getStorageSync("editGoodsState")),void 0!=uni.getStorageSync("editGoodsContent")&&""!=uni.getStorageSync("editGoodsContent")&&(this.goodsData.goods_content=uni.getStorageSync("editGoodsContent")),this.goodsData.goods_attr_class=uni.getStorageSync("editGoodsAttrClass")||0,this.goodsData.goods_attr_name=uni.getStorageSync("editGoodsAttrName")||"",this.goodsData.goods_attr_format=uni.getStorageSync("editGoodsAttrFormat")?JSON.parse(uni.getStorageSync("editGoodsAttrFormat")):[],this.$forceUpdate()},verify:function(){if(0==this.goodsData.goods_name.length)return this.$util.showToast({title:"请输入商品名称"}),!1;if(this.goodsData.goods_name.length>100)return this.$util.showToast({title:"商品名称不能超过100个字符"}),!1;if(this.goodsData.introduction.length>100)return this.$util.showToast({title:"促销语不能超过100个字符"}),!1;if(0==this.goodsData.goods_image.length)return this.$util.showToast({title:"请上传商品图片"}),!1;if(!this.shopCategoryData.store_0.category_id)return this.$util.showToast({title:"请选择商品分类"}),!1;if(2==this.goodsData.goods_class&&"verify"==this.goodsData.virtual_deliver_type){if(1==this.goodsData.verify_validity_type){if(0==this.virtualIndate.length)return this.$util.showToast({title:"请输入有效期"}),!1;if(isNaN(this.virtualIndate)||!this.$util.data().regExp.number.test(this.virtualIndate))return this.$util.showToast({title:"[有效期]格式输入错误"}),!1;if(this.virtualIndate<1)return this.$util.showToast({title:"有效期不能小于1天"}),!1}if(2==this.goodsData.verify_validity_type&&0==this.virtualTime.length)return this.$util.showToast({title:"请设置有效期"}),!1}if(0==this.goodsData.goods_spec_format.length){if(0==this.goodsData.price.length)return this.$util.showToast({title:"请输入销售价"}),!1;if(isNaN(this.goodsData.price)||!this.$util.data().regExp.digit.test(this.goodsData.price))return this.$util.showToast({title:"[销售价]格式输入错误"}),!1;if(this.goodsData.market_price.length>0&&(isNaN(this.goodsData.market_price)||!this.$util.data().regExp.digit.test(this.goodsData.market_price)))return this.$util.showToast({title:"[划线价]格式输入错误"}),!1;if(this.goodsData.cost_price.length>0&&(isNaN(this.goodsData.cost_price)||!this.$util.data().regExp.digit.test(this.goodsData.cost_price)))return this.$util.showToast({title:"[成本价]格式输入错误"}),!1;if(2==this.goodsData.goods_class&&"verify"==this.goodsData.virtual_deliver_type){var t=this.goodsData.verify_num;if(0==t.length)return this.$util.showToast({title:"请输入核销次数"}),!1;if(isNaN(t)||!this.$util.data().regExp.number.test(t))return this.$util.showToast({title:"[核销次数]格式输入错误"}),!1;if(parseInt(t)<1)return this.$util.showToast({title:"核销次数不能小于1"}),!1}if(1==this.goodsData.goods_class&&this.goodsData.weight.length>0&&(isNaN(this.goodsData.weight)||!this.$util.data().regExp.float3.test(this.goodsData.weight)))return this.$util.showToast({title:"[重量(kg)]格式输入错误，最多三位小数"}),!1;if(1==this.goodsData.goods_class&&this.goodsData.volume.length>0&&(isNaN(this.goodsData.volume)||!this.$util.data().regExp.float3.test(this.goodsData.volume)))return this.$util.showToast({title:"[体积(m³)]格式输入错误，最多三位小数"}),!1}else{if(0==this.goodsData.goods_sku_data.length)return this.$util.showToast({title:"请编辑规格信息"}),!1;for(var e=!1,a=0;a<this.goodsData.goods_sku_data.length;a++)if(""==this.goodsData.goods_sku_data[a].price){e=!0;break}if(e)return this.$util.showToast({title:"请编辑规格信息"}),!1}if(0==this.goodsData.goods_stock.length)return this.$util.showToast({title:"请输入库存"}),!1;if(isNaN(this.goodsData.goods_stock)||!this.$util.data().regExp.number.test(this.goodsData.goods_stock))return this.$util.showToast({title:"[库存]格式输入错误"}),!1;if(this.goodsData.goods_stock_alarm.length>0){if(isNaN(this.goodsData.goods_stock_alarm)||!this.$util.data().regExp.number.test(this.goodsData.goods_stock_alarm))return this.$util.showToast({title:"[库存预警]格式输入错误"}),!1;if(0!=parseInt(this.goodsData.goods_stock_alarm)&&parseInt(this.goodsData.goods_stock_alarm)==parseInt(this.goodsData.goods_stock))return this.$util.showToast({title:"[库存预警]不能等于库存数量"}),!1;if(parseInt(this.goodsData.goods_stock_alarm)>parseInt(this.goodsData.goods_stock))return this.$util.showToast({title:"[库存预警]不能超过库存数量"}),!1}if(1==this.goodsData.goods_class&&0==this.goodsData.is_free_shipping&&""==this.goodsData.shipping_template)return this.$util.showToast({title:"请选择运费模板"}),!1;if(0==this.goodsData.goods_content.length)return this.$util.showToast({title:"请填写商品详情"}),!1;if(this.goodsData.max_buy.length>0){if(isNaN(this.goodsData.max_buy)||!this.$util.data().regExp.number.test(this.goodsData.max_buy))return this.$util.showToast({title:"[限购]格式输入错误"}),!1;if(this.goodsData.max_buy<0)return this.$util.showToast({title:"限购数量不能小于"}),!1}if(this.goodsData.min_buy.length>0){if(isNaN(this.goodsData.min_buy)||!this.$util.data().regExp.number.test(this.goodsData.min_buy))return this.$util.showToast({title:"[起售]格式输入错误"}),!1;if(this.goodsData.min_buy<0)return this.$util.showToast({title:"起售数量不能小于"}),!1;if(this.goodsData.max_buy>0&&parseInt(this.goodsData.min_buy)>parseInt(this.goodsData.max_buy))return this.$util.showToast({title:"起售数量不能大于限购数量"}),!1}return!0},clearStoreage:function(){uni.removeStorageSync("selectedAlbumImg"),uni.removeStorageSync("selectedAlbumImgTemp"),uni.removeStorageSync("editGoodsSpecFormat"),uni.removeStorageSync("editGoodsSkuData"),uni.removeStorageSync("editGoodsCarmichael"),uni.removeStorageSync("specName"),uni.removeStorageSync("editGoodsShippingTemplateId"),uni.removeStorageSync("editGoodsShippingTemplateName"),uni.removeStorageSync("editGoodsState"),uni.removeStorageSync("editGoodsContent"),uni.removeStorageSync("editGoodsAttrClass"),uni.removeStorageSync("editGoodsAttrName"),uni.removeStorageSync("editGoodsAttrFormat")},save:function(){var t=this;if(this.verify()){for(var e=0;e<this.goodsData.goods_sku_data.length;e++)0==this.goodsData.goods_sku_data[e].sku_images.length&&(this.goodsData.goods_sku_data[e].sku_image="");var a=JSON.parse(JSON.stringify(this.goodsData));for(var o in delete a.category_name,a.category_json=[],a.category_id="",this.shopCategoryData)this.shopCategoryData[o].category_id&&(a.category_id+=","+this.shopCategoryData[o].category_id,a.category_json.push(this.shopCategoryData[o].category_id));if(a.category_id+=",",a.category_json=JSON.stringify(a.category_json),0==a.goods_spec_format.length)var i={sku_id:a.goods_id?a.sku_id:0,sku_name:a.goods_name,spec_name:"",sku_no:a.sku_no,sku_spec_format:"",price:a.price,market_price:a.market_price,cost_price:a.cost_price,stock:a.goods_stock,stock_alarm:a.goods_stock_alarm,weight:a.weight,volume:this.goodsData.volume,sku_image:a.goods_image[0],sku_images:a.goods_image.toString(),verify_num:a.verify_num},s=JSON.stringify([i]);a.goods_image=a.goods_image.toString(),a.goods_spec_format=a.goods_spec_format.length>0?JSON.stringify(a.goods_spec_format):"",a.goods_sku_data=a.goods_spec_format.length>0?JSON.stringify(a.goods_sku_data):s,a.goods_attr_format=a.goods_attr_format.length>0?JSON.stringify(a.goods_attr_format):"",a.spec_type_status=a.goods_spec_format.length>0?1:0,1==this.goodsData.verify_validity_type?a.virtual_indate=this.virtualIndate:2==this.goodsData.verify_validity_type&&(a.virtual_indate=this.virtualTime);var n=null;3==a.goods_class?(n=r.addVirtualCardGoods,a.goods_id&&(n=r.editVirtualCardGoods)):2==a.goods_class?(n=r.addVirtualGoods,a.goods_id&&(n=r.editVirtualGoods)):(n=r.addGoods,a.goods_id&&(n=r.editGoods)),a.goods_form=this.goodsData.goods_form_index?this.goodsForm[this.goodsData.goods_form_index-1].id:0,a.supplier_id=this.goodsData.supply_index?this.supplyForm[this.goodsData.supply_index-1].supplier_id:0,this.repeatFlag||(this.repeatFlag=!0,n(a).then((function(e){t.$util.showToast({title:e.message}),0==e.code?(t.clearStoreage(),setTimeout((function(){t.$util.redirectTo("/pages/goods/list",{},"tabbar")}),1e3)):t.repeatFlag=!1})))}},onLimit:function(){this.goodsData.is_limit=1==this.goodsData.is_limit?0:1},limitChange:function(t){this.goodsData.limit_type=t},joinMemberDiscount:function(){this.goodsData.is_consume_discount=1==this.goodsData.is_consume_discount?0:1},switchBtn:function(t){this.goodsData[t]=1==this.goodsData[t]?0:1},recommendWayChange:function(t){this.goodsData.recommend_way=t.detail.value},virtualDeliverTypeChange:function(t){this.goodsData.virtual_deliver_type=this.virtualDeliverArray[t.detail.value]["value"]},virtualReceiveTypeChange:function(t){this.goodsData.virtual_receive_type=this.virtualReceiveArray[t.detail.value]["value"]},validityTypeChange:function(t){this.goodsData.verify_validity_type=t.detail.value},virtualTimeChange:function(t){this.virtualTime=t.detail.value},isNeedVerify:function(){this.goodsData.is_need_verify=1==this.goodsData.is_need_verify?0:1},getGoodsForm:function(){var t=this;(0,n.getOrderFormList)().then((function(e){if(e.data){var a=["请选择商品表单"];e.data.forEach((function(e,o){a.push(e.form_name),t.goodsData.form_id&&t.goodsData.form_id==e.id&&(t.goodsData.goods_form_index=o+1)})),t.goodsForm=e.data,t.goodsFormArray=a,t.$forceUpdate()}}))},goodsFormChange:function(t){this.goodsData.goods_form_index=t.detail.value,this.$forceUpdate()},getSupplyList:function(){var t=this;(0,d.getSupplyList)().then((function(e){if(e.data){var a=["请选择供应商"];e.data.forEach((function(e,o){a.push(e.title),t.goodsData.supplier_id&&t.goodsData.supplier_id==e.supplier_id&&(t.goodsData.supply_index=o+1)})),t.supplyForm=e.data,t.supplyFormArray=a,t.$forceUpdate()}}))},supplyChange:function(t){this.goodsData.supply_index=t.detail.value,this.$forceUpdate()}}};e.default=l},"780d":function(t,e,a){"use strict";var o=a("1ec2"),i=a.n(o);i.a},"7dbd":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,".img-list[data-v-5d98d408] .con .area{\r\n\t/* height: 170rpx; */}",""]),t.exports=e},8491:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return o}));var o={shmilyDragImage:a("beeb").default,nsSwitch:a("e1f1").default,uniPopup:a("26da").default,loadingCover:a("59c1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container",class:{"safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"goods-edit-wrap"},[0==t.goodsData.goods_id?a("v-uni-view",{staticClass:"item-wrap padding"},[a("v-uni-view",{staticClass:"form-title color-title"},[t._v("商品类型")]),a("v-uni-view",{staticClass:"goods-type"},[a("v-uni-view",{class:{"selected color-base-text color-base-border":1==t.goodsData.goods_class},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsData.goods_class=1}}},[a("v-uni-text",[t._v("实物商品")]),a("v-uni-text",{staticClass:"iconfont iconduigou1"})],1),a("v-uni-view",{class:{"selected color-base-text color-base-border":2==t.goodsData.goods_class},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsData.goods_class=2}}},[a("v-uni-text",[t._v("虚拟商品")]),a("v-uni-text",{staticClass:"iconfont iconduigou1"})],1),t.addonIsExit.virtualcard?a("v-uni-view",{class:{"selected color-base-text color-base-border":3==t.goodsData.goods_class},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsData.goods_class=3}}},[a("v-uni-text",[t._v("电子卡密")]),a("v-uni-text",{staticClass:"iconfont iconduigou1"})],1):t._e(),t.addonIsExit.cardservice?[a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.showToast({title:"请到PC端管理端进行添加"})}}},[a("v-uni-text",[t._v("服务项目")]),a("v-uni-text",{staticClass:"iconfont iconduigou1"})],1),a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.showToast({title:"请到PC端管理端进行添加"})}}},[a("v-uni-text",[t._v("卡项套餐")]),a("v-uni-text",{staticClass:"iconfont iconduigou1"})],1)]:t._e()],2)],1):t._e(),a("v-uni-view",{staticClass:"form-title"},[t._v("基础信息")]),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("商品名称")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入商品名称,不能超过100个字符",maxlength:"100"},model:{value:t.goodsData.goods_name,callback:function(e){t.$set(t.goodsData,"goods_name",e)},expression:"goodsData.goods_name"}})],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("促销语")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入促销语,不能超过100个字符",maxlength:"100"},model:{value:t.goodsData.introduction,callback:function(e){t.$set(t.goodsData,"introduction",e)},expression:"goodsData.introduction"}})],1),a("v-uni-view",{staticClass:"form-wrap goods-img",style:{height:t.goodsImgHeight+"px"}},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("商品图片")])],1),a("v-uni-view",{staticClass:"img-list"},[a("shmily-drag-image",{ref:"goodsShmilyDragImg",attrs:{list:t.goodsData.goods_image,imageWidth:170,imageHeight:170,number:10,uploadMethod:"album",openSelectMode:!0,isAWait:t.isAWait},on:{"update:list":function(e){arguments[0]=e=t.$handleEvent(e),t.$set(t.goodsData,"goods_image",e)},callback:function(e){arguments[0]=e=t.$handleEvent(e),t.refreshGoodsImgHeight.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"tips"},[t._v("建议尺寸：800*800，长按图片可拖拽排序，最多上传10张")])],1)],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("关键词")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"商品关键词搜索",maxlength:"100"},model:{value:t.goodsData.keywords,callback:function(e){t.$set(t.goodsData,"keywords",e)},expression:"goodsData.keywords"}})],1)],1),a("v-uni-view",{staticClass:"form-title"},[a("v-uni-text",[t._v("商品分类")]),a("v-uni-text",{staticClass:"color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addShopCategory.apply(void 0,arguments)}}},[t._v("添加")])],1),a("v-uni-view",{staticClass:"item-wrap"},t._l(t.shopCategoryNumber,(function(e,o){return a("v-uni-view",{key:o,staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openGoodsCategoryPop(o)}}},[o>0?a("v-uni-text",{staticClass:"action iconfont iconjian",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteShopCategory(o)}}}):t._e(),a("v-uni-text",{staticClass:"label"},[t._v("商品分类")]),a("v-uni-text",{staticClass:"selected",class:{have:t.shopCategoryData["store_"+o]&&t.shopCategoryData["store_"+o].category_name}},[t._v(t._s(t.shopCategoryData["store_"+o]&&t.shopCategoryData["store_"+o].category_name?t.shopCategoryData["store_"+o].category_name:"请选择"))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)})),1),2==t.goodsData.goods_class?[a("v-uni-view",{staticClass:"form-title"},[t._v("收发货设置")]),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-picker",{attrs:{value:t.goodsData.virtual_deliver_type,range:t.virtualDeliverArray,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.virtualDeliverTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("发货设置")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.goodsData.virtual_deliver_type?t.virtualDeliverValue[t.goodsData.virtual_deliver_type]:""))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1)],1),"auto_deliver"==t.goodsData.virtual_deliver_type||"artificial_deliver"==t.goodsData.virtual_deliver_type?a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-picker",{attrs:{value:t.goodsData.virtual_receive_type,range:t.virtualReceiveArray,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.virtualReceiveTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("收货设置")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.goodsData.virtual_receive_type?t.virtualReceiveValue[t.goodsData.virtual_receive_type]:""))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1)],1):t._e(),"verify"==t.goodsData.virtual_deliver_type?[a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-picker",{attrs:{value:t.goodsData.verify_validity_type,range:t.validityTypeArray},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.validityTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap validity-type"},[a("v-uni-text",{staticClass:"label"},[t._v("核销有效期")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.validityTypeArray[t.goodsData.verify_validity_type]))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),1==t.goodsData.verify_validity_type?a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("有效期")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:t.virtualIndate,callback:function(e){t.virtualIndate=e},expression:"virtualIndate"}}),a("v-uni-text",{staticClass:"unit"},[t._v("天")])],1):2==t.goodsData.verify_validity_type?a("v-uni-picker",{attrs:{mode:"date",start:t.minDate,value:t.virtualTime},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.virtualTimeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap validity-type"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("有效期")])],1),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(""==t.virtualTime?"请设置过期时间":t.virtualTime))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1):t._e()],1)]:t._e()]:t._e(),a("v-uni-view",{staticClass:"form-title"},[t._v("规格、价格及库存")]),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openGoodsSpec()}}},[a("v-uni-text",{staticClass:"label"},[t._v("规格类型")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.goodsData.goods_spec_format.length?"多规格":"单规格"))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1),3==t.goodsData.goods_class&&t.goodsData.goods_spec_format.length<=0?a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openCarmichaelEdit()}}},[a("v-uni-text",{staticClass:"label"},[t._v("卡密管理")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",placeholder:t.carmiLength,disabled:""}}),a("v-uni-text",{staticClass:"iconfont iconright"})],1):t._e(),t.goodsData.goods_spec_format.length?a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openGoodsSpecEdit()}}},[a("v-uni-text",{staticClass:"label"},[t._v("规格详情")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v("价格、库存")]),a("v-uni-text",{staticClass:"iconfont iconright"})],1):[a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("销售价")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:t.goodsData.price,callback:function(e){t.$set(t.goodsData,"price",e)},expression:"goodsData.price"}}),a("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("划线价")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:t.goodsData.market_price,callback:function(e){t.$set(t.goodsData,"market_price",e)},expression:"goodsData.market_price"}}),a("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("成本价")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:t.goodsData.cost_price,callback:function(e){t.$set(t.goodsData,"cost_price",e)},expression:"goodsData.cost_price"}}),a("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),3!=t.goodsData.goods_class?a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("库存")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:t.goodsData.goods_stock,callback:function(e){t.$set(t.goodsData,"goods_stock",e)},expression:"goodsData.goods_stock"}}),a("v-uni-text",{staticClass:"unit"},[t._v("件")])],1):t._e(),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("库存预警")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:t.goodsData.goods_stock_alarm,callback:function(e){t.$set(t.goodsData,"goods_stock_alarm",e)},expression:"goodsData.goods_stock_alarm"}}),a("v-uni-text",{staticClass:"unit"},[t._v("件")])],1),2==t.goodsData.goods_class&&"verify"==t.goodsData.virtual_deliver_type?a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("核销次数")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:t.goodsData.verify_num,callback:function(e){t.$set(t.goodsData,"verify_num",e)},expression:"goodsData.verify_num"}}),a("v-uni-text",{staticClass:"unit"},[t._v("次")])],1):t._e(),1==t.goodsData.goods_class?a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("重量")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:t.goodsData.weight,callback:function(e){t.$set(t.goodsData,"weight",e)},expression:"goodsData.weight"}}),a("v-uni-text",{staticClass:"unit"},[t._v("kg")])],1):t._e(),1==t.goodsData.goods_class?a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("体积")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:t.goodsData.volume,callback:function(e){t.$set(t.goodsData,"volume",e)},expression:"goodsData.volume"}}),a("v-uni-text",{staticClass:"unit"},[t._v("m³")])],1):t._e(),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("商品编码")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入商品编码"},model:{value:t.goodsData.sku_no,callback:function(e){t.$set(t.goodsData,"sku_no",e)},expression:"goodsData.sku_no"}})],1)]],2),a("v-uni-view",{staticClass:"form-title"},[t._v(t._s(1==t.goodsData.goods_class?"配送及其他信息":"其他信息"))]),a("v-uni-view",{staticClass:"item-wrap"},[1==t.goodsData.goods_class?a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openExpressFreight()}}},[a("v-uni-text",{staticClass:"label"},[t._v("快递运费")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.goodsData.template_name?t.goodsData.template_name:"免邮"))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1):t._e(),a("v-uni-view",{staticClass:"form-wrap join-member-discount"},[a("v-uni-text",{staticClass:"label"},[t._v("是否开启限购")]),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.goodsData.is_limit},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onLimit.apply(void 0,arguments)}}})],1),1==t.goodsData.is_limit?a("v-uni-view",{staticClass:"form-wrap",staticStyle:{border:"none"}},[a("v-uni-view",{staticClass:"form-wrap",staticStyle:{border:"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.limitChange(1)}}},[a("v-uni-view",{staticClass:"iconfont",class:1==t.goodsData.limit_type?"iconyuan_checked color-base-text":"iconyuan_checkbox",staticStyle:{"margin-right":"10rpx"}}),a("v-uni-text",{staticClass:"label"},[t._v("单次限购")])],1),a("v-uni-view",{staticClass:"form-wrap",staticStyle:{border:"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.limitChange(2)}}},[a("v-uni-view",{staticClass:"iconfont",class:2==t.goodsData.limit_type?"iconyuan_checked color-base-text":"iconyuan_checkbox",staticStyle:{"margin-right":"10rpx"}}),a("v-uni-text",{staticClass:"label"},[t._v("长期限购")])],1)],1):t._e(),1==t.goodsData.is_limit?a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("限购数量")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:t.goodsData.max_buy,callback:function(e){t.$set(t.goodsData,"max_buy",e)},expression:"goodsData.max_buy"}}),a("v-uni-text",{staticClass:"unit"},[t._v("件")])],1):t._e(),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("起售数量")]),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:t.goodsData.min_buy,callback:function(e){t.$set(t.goodsData,"min_buy",e)},expression:"goodsData.min_buy"}}),a("v-uni-text",{staticClass:"unit"},[t._v("件")])],1),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("单位")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入单位"},model:{value:t.goodsData.unit,callback:function(e){t.$set(t.goodsData,"unit",e)},expression:"goodsData.unit"}})],1),a("v-uni-view",{staticClass:"form-wrap price"},[a("v-uni-text",{staticClass:"label"},[t._v("虚拟销量")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请设置虚拟销量"},model:{value:t.goodsData.virtual_sale,callback:function(e){t.$set(t.goodsData,"virtual_sale",e)},expression:"goodsData.virtual_sale"}})],1),a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openGoodsState()}}},[a("v-uni-text",{staticClass:"label"},[t._v("状态")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(1==t.goodsData.goods_state?"立刻上架":"放入仓库"))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1),a("v-uni-view",{staticClass:"form-wrap join-member-discount"},[a("v-uni-text",{staticClass:"label"},[t._v("是否参与会员折扣")]),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.goodsData.is_consume_discount},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.joinMemberDiscount.apply(void 0,arguments)}}})],1),a("v-uni-picker",{attrs:{value:t.goodsData.recommend_way,range:t.recommendArray},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.recommendWayChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("推荐方式")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.recommendArray[t.goodsData.recommend_way]))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),a("v-uni-view",{staticClass:"form-wrap join-member-discount"},[a("v-uni-text",{staticClass:"label"},[t._v("商品详情显示库存")]),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.goodsData.stock_show},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.switchBtn("stock_show")}}})],1),a("v-uni-view",{staticClass:"form-wrap join-member-discount"},[a("v-uni-text",{staticClass:"label"},[t._v("商品详情显示销量")]),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.goodsData.sale_show},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.switchBtn("sale_show")}}})],1),a("v-uni-view",{staticClass:"form-wrap join-member-discount"},[a("v-uni-text",{staticClass:"label"},[t._v("商品详情显示弹幕")]),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.goodsData.barrage_show},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.switchBtn("barrage_show")}}})],1),a("v-uni-view",{staticClass:"form-wrap join-member-discount"},[a("v-uni-text",{staticClass:"label"},[t._v("划线价显示")]),a("ns-switch",{staticClass:"balance-switch",attrs:{checked:1==t.goodsData.market_price_show},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.switchBtn("market_price_show")}}})],1),t.addonIsExit.form?a("v-uni-picker",{attrs:{value:t.goodsData.goods_form_index,range:t.goodsFormArray},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsFormChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("商品表单")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.goodsFormArray[t.goodsData.goods_form_index]))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1):t._e(),t.addonIsExit.supply?a("v-uni-picker",{attrs:{value:t.goodsData.supply_index,range:t.supplyFormArray},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.supplyChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("供应商")]),a("v-uni-text",{staticClass:"selected color-title"},[t._v(t._s(t.supplyFormArray[t.goodsData.supply_index]))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1):t._e()],1),a("v-uni-view",{staticClass:"form-title"},[t._v("商品详情")]),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openGoodsContent()}}},[a("v-uni-text",{staticClass:"label"},[t._v("商品详情")]),a("v-uni-text",{staticClass:"selected have"},[t._v("查看")]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),a("v-uni-view",{staticClass:"form-title"},[t._v("商品参数")]),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openAttr()}}},[a("v-uni-text",{staticClass:"label"},[t._v("商品参数")]),a("v-uni-text",{staticClass:"selected have"},[t._v("查看")]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1)],2),a("uni-popup",{ref:"categoryPopup",attrs:{type:"bottom"}},[a("v-uni-view",{staticClass:"popup category",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("选择商品分类")]),a("v-uni-text",{staticClass:"iconfont iconclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeGoodsCategoryPop()}}})],1),a("v-uni-view",{staticClass:"popup-body",class:{"safe-area":t.isIphoneX}},[a("v-uni-view",{staticClass:"nav color-base-text"},t._l(t.categoryName,(function(e,o){return t.currentLevel>=o+1?a("v-uni-text",{key:o,class:{"selected color-base-text":e},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow(o+1)}}},[t._v(t._s(e||"请选择"))]):t._e()})),1),a("v-uni-scroll-view",{staticClass:"category",attrs:{"scroll-y":"true"}},[t._l(t.categoryList,(function(e,o){return t.showFisrt?a("v-uni-view",{key:o,staticClass:"item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectCategory(e)}}},[a("v-uni-text",{class:{"color-base-text":t.categoryId[0]==e.category_id}},[t._v(t._s(e.category_name))]),a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:t.categoryId[0]==e.category_id,expression:"categoryId[0] == item.category_id"}],staticClass:"iconfont iconqueding_queren color-base-text"})],1):t._e()})),t._l(t.secondCategory,(function(e,o){return t.showSecond?a("v-uni-view",{key:o,staticClass:"item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectCategory(e)}}},[a("v-uni-text",{class:{"color-base-text":t.categoryId[1]==e.category_id}},[t._v(t._s(e.category_name))]),a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:t.categoryId[1]==e.category_id,expression:"categoryId[1] == item.category_id"}],staticClass:"iconfont iconqueding_queren color-base-text"})],1):t._e()})),t._l(t.thirdCategory,(function(e,o){return t.showThird?a("v-uni-view",{key:o,staticClass:"item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectCategory(e)}}},[a("v-uni-text",{class:{"color-base-text":t.categoryId[2]==e.category_id}},[t._v(t._s(e.category_name))]),a("v-uni-text",{directives:[{name:"show",rawName:"v-show",value:t.categoryId[2]==e.category_id,expression:"categoryId[2] == item.category_id"}],staticClass:"iconfont iconqueding_queren color-base-text"})],1):t._e()}))],2)],1)],1)],1),a("v-uni-view",{staticClass:"footer-wrap",class:{"safe-area":t.isIphoneX}},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save()}}},[t._v("保存")])],1),a("loading-cover",{ref:"loadingCover"})],1)},s=[]},"95a1":function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addGoods=function(t){return i.default.post("/shopapi/goods/addGoods",{data:t})},e.addVirtualCardGoods=function(t){return i.default.post("/virtualcard/shopapi/virtualgoods/addGoods",{data:t})},e.addVirtualGoods=function(t){return i.default.post("/shopapi/virtualgoods/addGoods",{data:t})},e.copyGoods=function(t){return i.default.post("/shopapi/goods/copyGoods",{data:{goods_id:t}})},e.deleteGoods=function(t){return i.default.post("/shopapi/goods/deleteGoods",{data:{goods_ids:t}})},e.editGoods=function(t){return i.default.post("/shopapi/goods/editGoods",{data:t})},e.editOutputList=function(t){return i.default.post("/shopapi/goods/editGoodsStock",{data:t})},e.editVirtualCardGoods=function(t){return i.default.post("/virtualcard/shopapi/virtualgoods/editGoods",{data:t})},e.editVirtualGoods=function(t){return i.default.post("/shopapi/virtualgoods/editGoods",{data:t})},e.getAttrClassList=function(){return i.default.get("/shopapi/goods/getAttrClassList")},e.getAttributeListById=function(t){return i.default.post("/shopapi/goods/getAttributeList",{data:{attr_class_id:t}})},e.getCategoryTree=function(t){return i.default.post("/shopapi/goods/getCategoryTree",{data:t})},e.getCondition=function(){return i.default.get("/shopapi/goods/condition")},e.getGoodsInfoById=function(t){return i.default.post("/shopapi/goods/editGetGoodsInfo",{data:{goods_id:t}})},e.getGoodsLists=function(t){return i.default.post("/shopapi/goods/lists",{data:t})},e.getOutputListById=function(t){return i.default.post("/shopapi/goods/getOutputList",{data:{goods_id:t}})},e.getVerifyStateRemark=function(t){return i.default.post("/shopapi/goods/getVerifyStateRemark",{data:{goods_id:t}})},e.offGoods=function(t){return i.default.post("/shopapi/goods/offGoods",{data:t})},e.onGoods=function(t){return i.default.post("/shopapi/goods/onGoods",{data:t})};var i=o(a("9027"))},aab1:function(t,e,a){"use strict";a.r(e);var o=a("b9d3"),i=a.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},b9d3:function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=o(a("beeb")),s=o(a("e1f1")),r=o(a("3dcf")),n={components:{shmilyDragImage:i.default,nsSwitch:s.default},mixins:[r.default]};e.default=n},c998:function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getSupplyList=function(){return i.default.get("/supply/shopapi/supply/lists")};var i=o(a("9027"))},cddc:function(t,e,a){"use strict";a.r(e);var o=a("8491"),i=a("aab1");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("780d"),a("0e46");var r=a("828b"),n=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"5d98d408",null,!1,o["a"],void 0);e["default"]=n.exports}}]);