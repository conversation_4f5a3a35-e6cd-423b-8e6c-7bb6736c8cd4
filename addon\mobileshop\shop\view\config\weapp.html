<div class="layui-form">
	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">小程序开发者设置</span>
		</div>
		<div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">小程序名称：</label>
                <div class="layui-input-inline ">
                    <input type="text" name="weapp_name" autocomplete="off" class="layui-input len-long" value="{$config_info.weapp_name ?? ''}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">小程序原始ID：</label>
                <div class="layui-input-block ">
                    <input type="text" name="weapp_original" autocomplete="off" class="layui-input len-long" value="{$config_info.weapp_original ?? ''}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">小程序二维码：</label>
                <div class="layui-input-block">
                    <div class="upload-img-block img-upload">
						<div class="upload-img-box {if condition="$config_info && $config_info['qrcode']"}hover{/if}">
							<div class="upload-default" id="img">
								{if condition="$config_info && $config_info.qrcode"}
								<div id="preview_img" class="preview_img">
									<img layer-src src="{:img($config_info.qrcode)}" class="img_prev"/>
								</div>
								{else/}
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
								{/if}
							</div>
							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="qrcode" value="{$config_info.qrcode ?? ''}">
						</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
	
	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">开发者ID设置</span>
		</div>
		<div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">APPID：</label>
                <div class="layui-input-inline ">
                    <input type="text" name="appid" autocomplete="off" class="layui-input len-long" value="{$config_info.appid ?? ''}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">APP密钥：</label>
                <div class="layui-input-block ">
                    <input type="text" name="appsecret" autocomplete="off" class="layui-input len-long" value="{$config_info.appsecret ?? ''}">
                </div>
                <div class="word-aux">AppID(小程序ID)和AppSecret(小程序密钥)来自于您申请的小程序账号，使用小程序账号密码登录公众平台，在开发->开发设置中可以找到</div>
            </div>
        </div>
    </div>
	
	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">服务器配置信息</span>
		</div>
		<div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">request合法域名：</label>
                <div class="layui-input-inline ">
                    <input type="text" autocomplete="off" readonly id="url_request" class="layui-input len-long" value="{$url}">
                </div>
                <button class="layui-btn layui-btn-primary" onclick="ns.copy('url_request')">复制</button>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">socket合法域名：</label>
                <div class="layui-input-inline ">
                    <input type="text" autocomplete="off" readonly id="url_socket" class="layui-input len-long" value="{$url}">
                </div>
                <button class="layui-btn layui-btn-primary" onclick="ns.copy('url_socket')">复制</button>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">uploadFile合法域名：</label>
                <div class="layui-input-inline ">
                    <input type="text" autocomplete="off" readonly id="url_upload" class="layui-input len-long" value="{$url}">
                </div>
                <button class="layui-btn layui-btn-primary" onclick="ns.copy('url_upload')">复制</button>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">downloadFile合法域：</label>
                <div class="layui-input-inline ">
                    <input type="text" autocomplete="off" id="url_download" readonly class="layui-input len-long" value="{$url}">
                </div>
                <button class="layui-btn layui-btn-primary" onclick="ns.copy('url_download')">复制</button>
            </div>
			<div class="form-row">
				<button class="layui-btn" lay-submit lay-filter="save">保存</button>
			</div>
        </div>
    </div>
</div>

<script type="text/javascript">
    layui.use(['form'], function () {
        var form = layui.form,
            repeat_flag = false; //防重复标识
        form.render();

        form.on('submit(save)', function (data) {
            if (repeat_flag) return;
            repeat_flag = true;
			
			//删除图片
			if(!data.field.qrcode) qrcode_upload.delete();
			
            $.ajax({
                type: "post",
                url: "{:addon_url('mobileshop://shop/config/weapp')}",
                dataType: "JSON",
                data: data.field,
                success: function (data) {
                    repeat_flag = false;
                    layer.msg(data.message);
                }
            });
        });

        // 图片上传
        var qrcode_upload = new Upload({
            elem: '#img',
			url: ns.url("shop/upload/upload"),
        });

    });
</script>
