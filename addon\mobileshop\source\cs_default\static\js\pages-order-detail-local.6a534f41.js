(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-detail-local"],{"13ae":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("64aa");var a={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var i=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){i.seconds--,i.seconds<0?i.timeUp():i.countDown()}),1e3)},watch:{day:function(t){var i=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){i.seconds--,i.seconds<0?i.timeUp():i.countDown()}),1e3)},hour:function(t){var i=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){i.seconds--,i.seconds<0?i.timeUp():i.countDown()}),1e3)},minute:function(t){var i=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){i.seconds--,i.seconds<0?i.timeUp():i.countDown()}),1e3)},second:function(t){var i=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){i.seconds--,i.seconds<0?i.timeUp():i.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,i,e,a){return t=Number(t),i=Number(i),e=Number(e),a=Number(a),60*t*60*24+60*i*60+60*e+a},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,i=0,e=0,a=0,s=0;t>0?(i=Math.floor(t/86400),e=Math.floor(t/3600)-24*i,a=Math.floor(t/60)-24*i*60-60*e,s=Math.floor(t)-24*i*60*60-60*e*60-60*a):this.timeUp(),i<10&&(i="0"+i),e<10&&(e="0"+e),a<10&&(a="0"+a),s<10&&(s="0"+s),this.d=i,this.h=e,this.i=a,this.s=s}}};i.default=a},"1a48":function(t,i,e){"use strict";e.d(i,"b",(function(){return s})),e.d(i,"c",(function(){return o})),e.d(i,"a",(function(){return a}));var a={nsOrderRemark:e("4003").default,loadingCover:e("59c1").default},s=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"order-detail-wrap"},[e("v-uni-view",{staticClass:"status-wrap color-base-bg",style:{backgroundImage:"url("+t.$util.img("public/uniapp/order/status-wrap-bg.png")+")"}},[e("v-uni-view",{staticClass:"container"},[e("v-uni-image",{attrs:{src:t.$util.img(t.orderDetail.order_status_action.icon)}}),e("v-uni-view",{staticClass:"status-name"},[e("v-uni-view",[t._v(t._s(t.orderDetail.order_status_name)),t.orderDetail.promotion_status_name?[t._v("("+t._s(t.orderDetail.promotion_status_name)+")")]:t._e()],2)],1)],1)],1),e("v-uni-view",{staticClass:"address-wrap"},[e("v-uni-view",{staticClass:"icon"},[e("v-uni-view",{staticClass:"iconfont iconlocation"})],1),e("v-uni-view",{staticClass:"address-info"},[e("v-uni-view",{staticClass:"info"},[t._v(t._s(t.orderDetail.name)+" "+t._s(t.orderDetail.mobile))]),e("v-uni-view",{staticClass:"detail"},[t._v("收货地址："+t._s(t.orderDetail.full_address)+" "+t._s(t.orderDetail.address))])],1)],1),e("v-uni-view",{staticClass:"block-wrap"},t._l(t.orderDetail.order_goods,(function(i,a){return e("v-uni-view",{key:a,staticClass:"goods-item-wrap"},[e("v-uni-view",{staticClass:"goods-item"},[e("v-uni-view",{staticClass:"goods-img"},[e("v-uni-image",{attrs:{src:t.$util.img(i.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(i){arguments[0]=i=t.$handleEvent(i),t.imgError(a)}}})],1),e("v-uni-view",{staticClass:"info-wrap"},[e("v-uni-view",{staticClass:"name-wrap"},[t._v(t._s(i.goods_name))]),i.sku_spec_format?e("v-uni-view",{staticClass:"spec-wrap"},[t._l(i.sku_spec_format,(function(e,a){return[t._v(t._s(e.spec_value_name)+" "+t._s(a<i.sku_spec_format.length-1?"; ":""))]}))],2):t._e(),e("v-uni-view",{staticClass:"more-wrap"},[e("v-uni-view",{staticClass:"goods-class"},[t._v(t._s(i.goods_class_name))]),1==i.is_present?e("v-uni-text",{staticClass:"present-label color-base-bg"},[t._v("赠品")]):t._e(),e("v-uni-view",{staticClass:"price-wrap"},[e("v-uni-view",{staticClass:"price"},[e("v-uni-text",{staticClass:"unit"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(i.price))])],1),e("v-uni-view",{staticClass:"num"},[t._v("x"+t._s(i.num))])],1)],1)],1),0!=i.refund_status?e("v-uni-view",{staticClass:"action-wrap",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.goRefund(i.order_goods_id)}}},[e("v-uni-button",{attrs:{type:"primary",size:"mini"}},[t._v(t._s(i.refund_status_name))])],1):1==t.orderDetail.is_enable_refund&&"blindbox"!=t.orderDetail.promotion_type&&0==i.shop_active_refund?e("v-uni-view",{staticClass:"action-wrap"},[e("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.shopActiveRefund(i.order_goods_id)}}},[t._v("主动退款")])],1):t._e()],1),i.form?e("v-uni-view",{staticClass:"goods-form"},t._l(i.form,(function(i,a){return e("v-uni-view",{key:a,staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v(t._s(i.value.title)+"：")]),"Img"==i.controller?e("v-uni-view",{staticClass:"box img-box"},t._l(i.img_lists,(function(i,a){return e("v-uni-view",{key:a,staticClass:"img"},[e("v-uni-image",{attrs:{src:t.$util.img(i),mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia(t.$util.img(i))}}})],1)})),1):e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"color-title"},[t._v(t._s(i.val))]),e("v-uni-text",{staticClass:"copy color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(i.val)}}},[t._v("复制")])],1)],1)})),1):t._e()],1)})),1),t.orderDetail.form?e("v-uni-view",{staticClass:"block-wrap order-form"},t._l(t.orderDetail.form,(function(i,a){return e("v-uni-view",{key:a,staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v(t._s(i.value.title)+"：")]),"Img"==i.controller?e("v-uni-view",{staticClass:"box img-box"},t._l(i.img_lists,(function(i,a){return e("v-uni-view",{key:a,staticClass:"img"},[e("v-uni-image",{attrs:{src:t.$util.img(i),mode:"widthFix"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewMedia(t.$util.img(i))}}})],1)})),1):e("v-uni-view",{staticClass:"box"},[e("v-uni-text",{staticClass:"color-title"},[t._v(t._s(i.val))]),e("v-uni-text",{staticClass:"copy color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(i.val)}}},[t._v("复制")])],1)],1)})),1):t._e(),e("v-uni-view",{staticClass:"block-wrap"},[e("v-uni-view",{staticClass:"title"},[t._v("订单信息")]),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("订单编号：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.order_no))]),e("v-uni-view",{staticClass:"copy color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.copy(t.orderDetail.order_no)}}},[t._v("复制")])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("订单类型：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.order_type_name))])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("订单来源：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.order_from_name))])],1),t.orderDetail.store_id?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("来源门店：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.store_name||"--"))])],1):t._e(),t.orderDetail.pay_status>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("付款方式：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.pay_type_name))])],1):t._e(),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("买家：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.member_id?t.orderDetail.nickname:"散客"))]),t.orderDetail.member_id?e("v-uni-view",{staticClass:"copy color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.redirectTo("/pages/member/detail",{member_id:t.orderDetail.member_id})}}},[t._v("查看")]):t._e()],1),t.orderDetail.delivery_type_name?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("配送方式：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.delivery_type_name))])],1):t._e(),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("配送时间：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.buyer_ask_delivery_time_str))])],1),""!=t.orderDetail.buyer_message?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("买家留言：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.buyer_message))])],1):t._e(),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("创建时间：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.create_time)))])],1),t.orderDetail.close_time>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("关闭时间：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.close_time)))])],1):t._e(),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("支付时间：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.pay_time)))])],1),t.orderDetail.remark?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("卖家备注：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.remark))])],1):t._e()],1),t.orderDetail.package_list?e("v-uni-view",{staticClass:"block-wrap tit-auto"},[e("v-uni-view",{staticClass:"title"},[t._v("配送信息")]),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("配送员：")]),e("v-uni-view",{staticClass:"box align-right"},[t._v(t._s(t.orderDetail.package_list.deliverer))])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("联系方式：")]),e("v-uni-view",{staticClass:"box align-right"},[t._v(t._s(t.orderDetail.package_list.deliverer_mobile))])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("配送时间：")]),e("v-uni-view",{staticClass:"box align-right"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.package_list.delivery_time)))])],1)],1):t._e(),1==t.orderDetail.is_invoice?[e("v-uni-view",{staticClass:"block-wrap"},[e("v-uni-view",{staticClass:"title"},[t._v("发票信息")]),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("发票类型：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(1==t.orderDetail["invoice_type"]?"纸质":"电子")+t._s(1==t.orderDetail["is_tax_invoice"]?"专票":"普票"))])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("发票抬头：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_title))])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("抬头类型：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(1==t.orderDetail.invoice_title_type?"个人":"企业"))])],1),2==t.orderDetail.invoice_title_type?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("纳税人识别号：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.taxpayer_number))])],1):t._e(),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("发票内容：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_content))])],1),1==t.orderDetail.invoice_title_type?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("邮寄地址：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_full_address))])],1):e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("接收邮件：")]),e("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_email))])],1)],1),e("v-uni-view",{staticClass:"block-wrap"},[e("v-uni-view",{staticClass:"title"},[t._v("发票费用")]),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("发票费用：")]),e("v-uni-view",{staticClass:"box align-right money"},[t._v("￥"+t._s(t.orderDetail.invoice_money))])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("发票税率：")]),e("v-uni-view",{staticClass:"box align-right money"},[t._v(t._s(t.orderDetail.invoice_rate)+"%")])],1),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("邮寄费用：")]),e("v-uni-view",{staticClass:"box align-right money"},[t._v("￥"+t._s(t.orderDetail.invoice_delivery_money))])],1)],1)]:t._e(),e("v-uni-view",{staticClass:"block-wrap"},[e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("商品金额")]),e("v-uni-view",{staticClass:"box align-right money bold"},[e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderDetail.goods_money))])],1)],1),t.orderDetail.delivery_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("运费")]),e("v-uni-view",{staticClass:"box align-right money bold"},[e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.delivery_money))])],1)],1):t._e(),t.orderDetail.member_card_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("会员卡")]),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"color-base-text"},[e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.member_card_money))])],1)],1)],1):t._e(),t.orderDetail.invoice_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("税费"),e("v-uni-text",{staticClass:"color-base-text"},[t._v("("+t._s(t.orderDetail.invoice_rate)+"%)")])],1),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"color-base-text"},[e("v-uni-text",{staticClass:"operator"},[t._v("+")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.invoice_money))])],1)],1)],1):t._e(),t.orderDetail.invoice_delivery_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("发票邮寄费")]),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"color-base-text"},[e("v-uni-text",{staticClass:"operator"},[t._v("+")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.invoice_delivery_money))])],1)],1)],1):t._e(),t.orderDetail.coupon_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),e("v-uni-view",{staticClass:"box align-right money bold"},[e("v-uni-text",{staticClass:"operator"},[t._v("-")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.coupon_money))])],1)],1):t._e(),t.orderDetail.promotion_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("优惠")]),e("v-uni-view",{staticClass:"box align-right money bold"},[e("v-uni-text",{staticClass:"operator"},[t._v("-")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.promotion_money))])],1)],1):t._e(),0!=t.orderDetail.adjust_money?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("订单调价")]),e("v-uni-view",{staticClass:"box align-right money bold"},[t.orderDetail.adjust_money<0?e("v-uni-text",{staticClass:"operator"},[t._v("-")]):e("v-uni-text",{staticClass:"operator"},[t._v("+")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t._f("abs")(t.orderDetail.adjust_money)))])],1)],1):t._e(),t.orderDetail.balance_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("使用余额")]),e("v-uni-view",{staticClass:"box align-right money bold"},[e("v-uni-text",{staticClass:"operator"},[t._v("-")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.balance_money))])],1)],1):t._e(),t.orderDetail.point_money>0?e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-text",{staticClass:"tit"},[t._v("积分抵扣")]),e("v-uni-view",{staticClass:"box align-right"},[e("v-uni-text",{staticClass:"color-base-text"},[e("v-uni-text",{staticClass:"operator"},[t._v("-")]),e("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),e("v-uni-text",[t._v(t._s(t.orderDetail.point_money))])],1)],1)],1):t._e(),e("v-uni-view",{staticClass:"order-cell"},[e("v-uni-view",{staticClass:"box align-right money bold"},[e("v-uni-text",[t._v("共"+t._s(t.orderDetail.goods_num)+"件商品，实付金额：")]),e("v-uni-text",{staticClass:"font-size-goods-tag color-base-text"},[t._v("￥")]),e("v-uni-text",{staticClass:"font-size-base color-base-text"},[t._v(t._s(t.orderDetail.pay_money))])],1)],1)],1),t.log.length?e("v-uni-view",{staticClass:"block-wrap log"},[e("v-uni-view",{staticClass:"title color-base-text"},[t._v("订单日志")]),t._l(t.log,(function(i,a){return e("v-uni-view",{key:a,staticClass:"item"},[e("v-uni-view",{staticClass:"action"},[e("v-uni-view",{staticClass:"title"},[e("v-uni-text",{staticClass:"font-size-base"},[t._v("操作："+t._s(i.action))]),e("v-uni-text",{staticClass:"color-sub time"},[t._v(t._s(t.$util.timeStampTurnTime(i.action_time,"Y-m-d")))])],1),e("v-uni-view",[e("v-uni-text",{staticClass:"color-tip"},[t._v("操作人："+t._s(i.nick_name))]),e("br"),e("v-uni-text",{staticClass:" color-tip"},[t._v("操作时间："+t._s(t.$util.timeStampTurnTime(i.action_time)))]),e("br"),e("v-uni-text",{staticClass:" color-tip"},[t._v("订单状态："+t._s(i.order_status_name))])],1)],1)],1)}))],2):t._e(),e("v-uni-view",{staticClass:"block-wrap tips"},[e("v-uni-view",{staticClass:"title color-base-text"},[t._v("提醒")]),e("v-uni-text",[t._v("请及时关注你发出的包裹状态，确保能配送至买家手中")]),e("v-uni-text",[t._v("如果买家表示未收到货或者货物有问题，请及时联系买家积极处理，友好协商")])],1),e("v-uni-view",{staticClass:"placeholder-height"}),e("v-uni-view",{staticClass:"footer-wrap"},[e("v-uni-view",{staticClass:"container"},[e("v-uni-button",{attrs:{type:"primary",size:"mini",plain:!0},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderRemark()}}},[t._v("备注")]),t._l(t.orderDetail.order_status_action.action,(function(i,a){return e("v-uni-button",{key:a,attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.orderAction(i.action,t.orderId)}}},[t._v(t._s(i.title))])})),0==t.orderDetail.order_status?e("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.offlinePay(t.orderId)}}},[t._v("线下支付")]):t._e()],2)],1),e("ns-order-remark",{ref:"orderRemark",attrs:{order:t.tempOrder}}),e("loading-cover",{ref:"loadingCover"})],2)},o=[]},3897:function(t,i,e){"use strict";e.r(i);var a=e("8a25"),s=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);i["default"]=s.a},4123:function(t,i,e){var a=e("d3cb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=e("967d").default;s("b30e5984",a,!0,{sourceMap:!1,shadowMode:!1})},"4cea":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){}));var a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?e("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),e("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():e("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},s=[]},"4d89":function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-e9e34d7a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-e9e34d7a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-e9e34d7a]{position:fixed;left:0;right:0;z-index:998}.uni-countdown[data-v-e9e34d7a]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-e9e34d7a]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-e9e34d7a]{line-height:%?50?%}.uni-countdown__number[data-v-e9e34d7a]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=i},5695:function(t,i,e){"use strict";e.r(i);var a=e("4cea"),s=e("df68");for(var o in s)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(o);e("c4f9");var r=e("828b"),n=Object(r["a"])(s["default"],a["b"],a["c"],!1,null,"e9e34d7a",null,!1,a["a"],void 0);i["default"]=n.exports},"5b30":function(t,i,e){"use strict";var a=e("4123"),s=e.n(a);s.a},7299:function(t,i,e){"use strict";e.r(i);var a=e("1a48"),s=e("3897");for(var o in s)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(o);e("5b30");var r=e("828b"),n=Object(r["a"])(s["default"],a["b"],a["c"],!1,null,"67146a7c",null,!1,a["a"],void 0);i["default"]=n.exports},"75f6":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("bf0f"),e("2797"),e("d4b5"),e("e838");var a=e("6638"),s={data:function(){return{isIphoneX:!1,orderId:0,template:"",orderDetail:{order_status_action:{icon:""},virtual_goods:{}},tempOrder:{remark:""},log:[]}},onLoad:function(t){this.orderId=t.order_id||0,this.template=t.template||"basis"},onShow:function(){var t=this,i="/pages/order/detail/"+this.template+"?order_id="+this.orderId+"&template="+this.template;this.$util.checkToken(i)&&(this.getOrderDetail(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.actionCallback=function(){t.getOrderDetail()},this.getLogList())},methods:{getOrderDetail:function(){var t=this;(0,a.getOrderDetailInfoById)(this.orderId).then((function(i){0==i.code?(t.orderDetail=i.data,t.orderDetail.order_status_action=JSON.parse(t.orderDetail.order_status_action),t.orderDetail.order_goods.forEach((function(t){t.sku_spec_format=t.sku_spec_format?JSON.parse(t.sku_spec_format):[]})),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:i.message}),setTimeout((function(){t.$util.redirectTo("/pages/order/list",{},"redirectTo")}),1e3))}))},getLogList:function(){var t=this;(0,a.getOrderLog)(this.orderId).then((function(i){i.code>=0&&i.data&&(t.log=i.data)}))},imgError:function(t){this.orderDetail.order_goods[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},orderRemark:function(){var t=this;this.tempOrder=JSON.parse(JSON.stringify(this.orderDetail)),this.$refs.orderRemark.show((function(){t.getOrderDetail()}))},orderAction:function(t,i){try{this[t](i)}catch(e){console.log("orderAction error：",e)}},trace:function(){this.$util.redirectTo("/pages/order/logistics",{order_id:this.orderId})}},filters:{abs:function(t){return Math.abs(parseFloat(t)).toFixed(2)}}};i.default=s},"8a25":function(t,i,e){"use strict";e("6a54");var a=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var s=a(e("5695")),o=a(e("4003")),r=a(e("514b")),n=a(e("75f6")),l={components:{uniCountDown:s.default,nsOrderRemark:o.default},mixins:[r.default,n.default]};i.default=l},b65d:function(t,i,e){var a=e("4d89");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=e("967d").default;s("431f734f",a,!0,{sourceMap:!1,shadowMode:!1})},c4f9:function(t,i,e){"use strict";var a=e("b65d"),s=e.n(a);s.a},d3cb:function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-67146a7c]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-67146a7c]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-67146a7c]{position:fixed;left:0;right:0;z-index:998}.order-detail-wrap[data-v-67146a7c]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?30?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?30?%)}.align-right[data-v-67146a7c]{text-align:right}.status-wrap[data-v-67146a7c]{background-size:100% 100%;padding:%?40?%;height:%?180?%}.status-wrap uni-image[data-v-67146a7c]{width:%?104?%;height:%?86?%;margin-right:%?20?%;margin-top:%?20?%}.status-wrap .container[data-v-67146a7c]{display:flex;align-items:center}.status-wrap .container .status-name uni-view[data-v-67146a7c]{font-size:%?32?%;color:#fff;line-height:1;margin-top:%?20?%;text-align:left}.status-wrap .container .status-name uni-view.time[data-v-67146a7c]{margin-top:%?20?%;font-size:%?28?%}.address-wrap[data-v-67146a7c]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative;min-height:%?100?%;margin-top:%?-69?%}.address-wrap .icon[data-v-67146a7c]{position:absolute;top:%?10?%;margin-right:%?20?%}.address-wrap .icon .iconfont[data-v-67146a7c]{line-height:%?50?%;font-size:%?28?%}.address-wrap .address-info[data-v-67146a7c]{padding-left:%?40?%}.address-wrap .address-info .info[data-v-67146a7c]{display:flex;line-height:1}.address-wrap .address-info .detail[data-v-67146a7c]{line-height:1.3;margin-top:%?20?%}.block-wrap[data-v-67146a7c]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative}.block-wrap .goods-item-wrap[data-v-67146a7c]{border-bottom:%?2?% solid #f5f5f5;padding-bottom:%?20?%}.block-wrap .goods-item-wrap[data-v-67146a7c]:last-child{border-bottom:0}.block-wrap .goods-item[data-v-67146a7c]{background:#fff;margin-top:%?20?%;display:flex;position:relative;flex-flow:row wrap}.block-wrap .goods-item .goods-img[data-v-67146a7c]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.block-wrap .goods-item .goods-img uni-image[data-v-67146a7c]{width:100%;height:100%}.block-wrap .goods-item .info-wrap[data-v-67146a7c]{flex:1;display:flex;flex-direction:column;width:50%}.block-wrap .goods-item .info-wrap .name-wrap[data-v-67146a7c]{line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.block-wrap .goods-item .info-wrap .spec-wrap[data-v-67146a7c]{line-height:1;margin-top:%?10?%;margin-bottom:%?10?%;color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.block-wrap .goods-item .info-wrap .more-wrap[data-v-67146a7c]{display:flex}.block-wrap .goods-item .info-wrap .more-wrap .goods-class[data-v-67146a7c]{font-size:%?20?%;color:#909399}.block-wrap .goods-item .info-wrap .more-wrap .goods-class .delivery-status[data-v-67146a7c]{padding-left:%?6?%}.block-wrap .goods-item .info-wrap .more-wrap .present-label[data-v-67146a7c]{padding:0 %?6?%;display:inline-block;color:#fff;font-size:%?24?%;border-radius:%?10?%;width:-webkit-fit-content;width:fit-content;position:absolute;margin-left:%?110?%}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap[data-v-67146a7c]{flex:1;text-align:right}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .unit[data-v-67146a7c]{font-size:%?20?%}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .price[data-v-67146a7c]{display:inline-block;line-height:1;flex:1}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .num[data-v-67146a7c]{color:#909399;font-size:%?20?%;line-height:1}.block-wrap .goods-item .info-wrap .delivery-status-name[data-v-67146a7c]{font-weight:700;text-align:right;line-height:1;margin-top:%?10?%}.block-wrap .goods-item .action-wrap[data-v-67146a7c]{width:100%;text-align:right;margin-top:%?10?%}.block-wrap .goods-item .action-wrap uni-button[data-v-67146a7c]{margin-right:%?20?%!important}.block-wrap .goods-item .action-wrap uni-button[data-v-67146a7c]:last-child{margin-right:0!important}.block-wrap .title[data-v-67146a7c]{font-size:%?32?%}.block-wrap .order-cell[data-v-67146a7c]{display:flex;margin:%?20?% 0;align-items:center;background:#fff;line-height:%?40?%}.block-wrap .order-cell[data-v-67146a7c]:first-child{margin-top:0}.block-wrap .order-cell .tit[data-v-67146a7c]{width:%?200?%;text-align:left}.block-wrap .order-cell .box[data-v-67146a7c]{flex:1;padding:0 %?20?%;line-height:inherit}.block-wrap .order-cell .box.money[data-v-67146a7c]{padding:0}.block-wrap .order-cell .box.bold[data-v-67146a7c]{font-weight:700}.block-wrap .order-cell .box .operator[data-v-67146a7c]{font-size:%?24?%;margin-right:%?6?%}.block-wrap .order-cell .img-box[data-v-67146a7c]{display:flex;flex-wrap:wrap}.block-wrap .order-cell .img-box .img[data-v-67146a7c]{width:%?100?%;height:%?100?%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:%?30?%;margin-bottom:%?30?%;position:relative;border-radius:%?10?%;line-height:1;overflow:hidden}.block-wrap .order-cell .img-box .img uni-image[data-v-67146a7c]{width:100%}.block-wrap.tit-auto .tit[data-v-67146a7c]{width:auto}.block-wrap.tips .title[data-v-67146a7c]{font-size:%?28?%}.block-wrap.tips uni-text[data-v-67146a7c]{font-size:%?24?%;display:block;margin-top:%?10?%}.block-wrap.log .title[data-v-67146a7c]{font-size:%?28?%}.block-wrap.log .item[data-v-67146a7c]{display:flex;align-items:center;margin-top:%?20?%}.block-wrap.log .item[data-v-67146a7c]:last-child{margin-bottom:%?20?%}.block-wrap.log .item .tag[data-v-67146a7c]{color:#fff;border-radius:50%;margin-right:%?20?%;padding:%?10?%}.block-wrap.log .item .action[data-v-67146a7c]{flex:1}.block-wrap.log .item .action .title[data-v-67146a7c]{display:flex;justify-content:space-between}.block-wrap.log .item uni-text[data-v-67146a7c]{font-size:%?24?%}.placeholder-height[data-v-67146a7c]{height:%?120?%}.footer-wrap[data-v-67146a7c]{position:fixed;bottom:0;padding:%?20?% 0 %?10?%;z-index:10;width:100%;text-align:right;background-color:#fff;border-top:1px solid #eee;padding-bottom:calc(constant(safe-area-inset-bottom) + %?30?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?30?%)}.footer-wrap .container[data-v-67146a7c]{margin:0 %?30?%}.footer-wrap .container uni-button[data-v-67146a7c]{margin-left:%?20?%!important}.footer-wrap .container uni-button[data-v-67146a7c]:first-child{margin-left:0!important}.goods-form .order-cell .box[data-v-67146a7c]{padding-right:0}.goods-form .order-cell .copy[data-v-67146a7c]{margin-left:%?10?%;float:right}.order-form .order-cell .box[data-v-67146a7c]{padding-right:0}.order-form .order-cell .copy[data-v-67146a7c]{margin-left:%?10?%;float:right}',""]),t.exports=i},df68:function(t,i,e){"use strict";e.r(i);var a=e("13ae"),s=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(o);i["default"]=s.a}}]);