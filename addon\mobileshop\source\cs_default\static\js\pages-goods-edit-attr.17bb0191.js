(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-edit-attr"],{"1ef3":function(t,a,e){"use strict";e.r(a);var o=e("68dd"),i=e("a9cb");for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(r);e("dcfa");var n=e("828b"),d=Object(n["a"])(i["default"],o["b"],o["c"],!1,null,"7f211cf6",null,!1,o["a"],void 0);a["default"]=d.exports},"323b":function(t,a,e){var o=e("608a");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=e("967d").default;i("316b2136",o,!0,{sourceMap:!1,shadowMode:!1})},"608a":function(t,a,e){var o=e("c86c");a=o(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-7f211cf6]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-7f211cf6]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-7f211cf6]{position:fixed;left:0;right:0;z-index:998}.container[data-v-7f211cf6]{padding-bottom:%?40?%}.safe-area[data-v-7f211cf6]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.goods-edit-wrap[data-v-7f211cf6]{margin-bottom:%?160?%}.form-title[data-v-7f211cf6]{display:flex;justify-content:space-between;margin:%?20?% %?30?%;color:#909399}.item-wrap uni-radio .uni-radio-input[data-v-7f211cf6]{width:%?30?%!important;height:%?30?%!important}.item-wrap[data-v-7f211cf6]{background:#fff;margin-top:%?20?%}.item-wrap .goods-type[data-v-7f211cf6]{display:flex;margin:0 %?40?% %?20?% %?40?%;flex-wrap:wrap}.item-wrap .goods-type uni-view[data-v-7f211cf6]{flex:1;text-align:center;border:1px solid #ccc;color:#909399;margin-right:%?40?%;margin-top:%?30?%;position:relative;height:%?80?%;line-height:%?80?%;white-space:nowrap;min-width:calc((100% - %?100?%) / 3);max-width:calc((100% - %?100?%) / 3)}.item-wrap .goods-type uni-view[data-v-7f211cf6]:nth-child(3n+3){margin-right:0}.item-wrap .goods-type uni-view .iconfont[data-v-7f211cf6]{display:none}.item-wrap .goods-type uni-view.selected .iconfont[data-v-7f211cf6]{display:block;position:absolute;bottom:%?-22?%;right:%?-22?%;font-size:%?80?%}.item-wrap .form-wrap[data-v-7f211cf6]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-7f211cf6]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-7f211cf6]{font-weight:700}.item-wrap .form-wrap .label[data-v-7f211cf6]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap uni-input[data-v-7f211cf6]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-7f211cf6]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-7f211cf6]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-7f211cf6]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.more-wrap .action[data-v-7f211cf6]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.item-wrap .form-wrap.goods-img[data-v-7f211cf6]{height:%?200?%;line-height:%?200?%;display:block;position:relative}.item-wrap .form-wrap.goods-img .label[data-v-7f211cf6]{display:inline-block}.item-wrap .form-wrap.goods-img .img-list[data-v-7f211cf6]{position:absolute;width:80%;top:0;left:%?100?%;margin-top:%?40?%;margin-left:%?40?%}.item-wrap .form-wrap.goods-img .tips[data-v-7f211cf6]{color:#909399;font-size:%?20?%;margin-top:%?20?%}.item-wrap .form-wrap .unit[data-v-7f211cf6]{margin-left:%?20?%;width:%?40?%}.item-wrap .form-wrap.join-member-discount .label[data-v-7f211cf6]{flex:1}.item-wrap .form-wrap.validity-type[data-v-7f211cf6]{border-bottom:1px solid #eee!important}.footer-wrap[data-v-7f211cf6]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;background-color:#fff}.popup[data-v-7f211cf6]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-7f211cf6]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-7f211cf6]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-7f211cf6]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-7f211cf6]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-7f211cf6]{height:calc(100% - %?270?%)}.popup.category[data-v-7f211cf6]{height:50vh}.popup.category .popup-header[data-v-7f211cf6]{border-bottom:none}.popup.category .popup-body[data-v-7f211cf6]{padding:0 %?30?%}.popup.category .popup-body .nav[data-v-7f211cf6]{border-bottom:%?2?% solid #eee;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.popup.category .popup-body .nav uni-text[data-v-7f211cf6]{padding:0 0 %?20?% 0;margin-right:%?20?%;display:inline-block}.popup.category .popup-body .nav uni-text[data-v-7f211cf6]:last-child{padding-right:0}.popup.category .popup-body .nav uni-text.selected[data-v-7f211cf6]{border-bottom:2px solid}.popup.category .popup-body .category[data-v-7f211cf6]{height:100%}.popup.category .popup-body .category .item[data-v-7f211cf6]{display:block;margin:%?20?% 0 0}.popup.category .popup-body .category .item uni-text[data-v-7f211cf6]:first-child{overflow:hidden;text-overflow:ellipsis;white-space:pre;width:90%;display:inline-block;vertical-align:middle}.popup.category .popup-body .category .item .iconfont[data-v-7f211cf6]{float:right}.popup.category .popup-body .category .child-item[data-v-7f211cf6]{display:flex;justify-content:space-between;margin-left:%?40?%}.popup.choose-picture[data-v-7f211cf6]{background-color:#f8f8f8}.popup.choose-picture .popup-header[data-v-7f211cf6]{border-bottom:none;background-color:#fff}.popup.choose-picture .popup-body[data-v-7f211cf6]{background-color:#f8f8f8;height:auto}.popup.choose-picture .popup-body .select-wrap[data-v-7f211cf6]{background-color:#fff;padding:0 %?30?%}.popup.choose-picture .popup-body .item[data-v-7f211cf6]{text-align:center;padding:%?20?%;background-color:#fff;border-bottom:1px solid #eee}.popup.choose-picture .popup-body .item[data-v-7f211cf6]:last-child{border-bottom:none}.popup.choose-picture .popup-body .item.cancle[data-v-7f211cf6]{margin-top:%?20?%}.item-wrap .action[data-v-7f211cf6]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%;vertical-align:middle}.item-wrap .form-wrap.attr-value[data-v-7f211cf6]{height:auto;display:block;padding-bottom:%?20?%}.item-wrap .form-wrap .select-box[data-v-7f211cf6]{vertical-align:middle;flex:1;text-align:right;color:#909399;font-size:%?40?%;display:flex;flex-wrap:wrap}.item-wrap .form-wrap .select-box > uni-view[data-v-7f211cf6]{margin-right:%?30?%}.item-wrap .form-wrap .select-box > uni-view[data-v-7f211cf6]:last-child{margin-right:0}.item-wrap .form-wrap .select-box > uni-view .value[data-v-7f211cf6]{margin-left:%?10?%;max-width:%?400?%;display:inline-block;vertical-align:middle;white-space:pre;overflow:hidden;text-overflow:ellipsis}.item-wrap .form-wrap .radio[data-v-7f211cf6],\r\n.item-wrap .form-wrap .checkbox[data-v-7f211cf6]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;font-size:%?40?%}.item-wrap .form-wrap .diy-attr-input[data-v-7f211cf6]{text-align:left;margin-right:%?30?%}.add-attr[data-v-7f211cf6]{text-align:center;background-color:#fff;height:%?100?%;line-height:%?100?%;border-radius:%?10?%;margin:%?20?% %?30?% 0}.footer-wrap[data-v-7f211cf6]{position:static;background-color:initial}',""]),t.exports=a},"68dd":function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return r})),e.d(a,"a",(function(){return o}));var o={loadingCover:e("59c1").default},i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",[e("v-uni-view",{staticClass:"item-wrap"},[e("v-uni-view",{staticClass:"form-wrap more-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("参数模板")]),e("v-uni-picker",{staticClass:"selected",attrs:{value:t.index,range:t.pickedArr},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.bindPickerChange.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.goodsAttrName?t.goodsAttrName:"请选择参数模板"))])],1),e("v-uni-text",{staticClass:"iconfont iconright"})],1),t._l(t.attrValueList,(function(a,o){return e("v-uni-view",{key:o,staticClass:"form-wrap",class:{"attr-value":3!=a.attr_type}},[e("v-uni-text",{staticClass:"action iconfont iconjian",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.deleteAttr(o)}}}),a.attr_class_id>0?[e("v-uni-text",{staticClass:"label"},[t._v(t._s(a.attr_name))]),3==a.attr_type?e("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入"+a.attr_name,maxlength:"50"},model:{value:a.attr_value_name,callback:function(e){t.$set(a,"attr_value_name",e)},expression:"item.attr_value_name"}}):t._e()]:[e("v-uni-input",{staticClass:"uni-input diy-attr-input",attrs:{placeholder:"请输入参数名",maxlength:"50"},model:{value:a.attr_name,callback:function(e){t.$set(a,"attr_name",e)},expression:"item.attr_name"}}),e("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入参数值",maxlength:"50"},model:{value:a.attr_value_name,callback:function(e){t.$set(a,"attr_value_name",e)},expression:"item.attr_value_name"}})],3!=a.attr_type?e("v-uni-view",{staticClass:"select-box"},t._l(a.attr_value_format,(function(i,r){return e("v-uni-view",{key:r,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectBox(a.attr_type,o,r)}}},[1==a.attr_type?e("v-uni-text",{staticClass:"radio iconfont",class:i.checked?"iconyuan_checked color-base-text":"iconyuan_checkbox"}):2==a.attr_type?e("v-uni-text",{staticClass:"checkbox iconfont",class:i.checked?"iconfuxuankuang1 color-base-text":"iconfuxuankuang2"}):t._e(),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.attr_value_name))])],1)})),1):t._e()],2)}))],2),e("v-uni-view",{staticClass:"color-base-text add-attr",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.addAttr()}}},[t._v("+添加参数")]),e("v-uni-view",{staticClass:"footer-wrap"},[e("v-uni-button",{attrs:{type:"primary"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.save()}}},[t._v("保存")])],1),e("loading-cover",{ref:"loadingCover"})],1)},r=[]},"95a1":function(t,a,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.addGoods=function(t){return i.default.post("/shopapi/goods/addGoods",{data:t})},a.addVirtualCardGoods=function(t){return i.default.post("/virtualcard/shopapi/virtualgoods/addGoods",{data:t})},a.addVirtualGoods=function(t){return i.default.post("/shopapi/virtualgoods/addGoods",{data:t})},a.copyGoods=function(t){return i.default.post("/shopapi/goods/copyGoods",{data:{goods_id:t}})},a.deleteGoods=function(t){return i.default.post("/shopapi/goods/deleteGoods",{data:{goods_ids:t}})},a.editGoods=function(t){return i.default.post("/shopapi/goods/editGoods",{data:t})},a.editOutputList=function(t){return i.default.post("/shopapi/goods/editGoodsStock",{data:t})},a.editVirtualCardGoods=function(t){return i.default.post("/virtualcard/shopapi/virtualgoods/editGoods",{data:t})},a.editVirtualGoods=function(t){return i.default.post("/shopapi/virtualgoods/editGoods",{data:t})},a.getAttrClassList=function(){return i.default.get("/shopapi/goods/getAttrClassList")},a.getAttributeListById=function(t){return i.default.post("/shopapi/goods/getAttributeList",{data:{attr_class_id:t}})},a.getCategoryTree=function(t){return i.default.post("/shopapi/goods/getCategoryTree",{data:t})},a.getCondition=function(){return i.default.get("/shopapi/goods/condition")},a.getGoodsInfoById=function(t){return i.default.post("/shopapi/goods/editGetGoodsInfo",{data:{goods_id:t}})},a.getGoodsLists=function(t){return i.default.post("/shopapi/goods/lists",{data:t})},a.getOutputListById=function(t){return i.default.post("/shopapi/goods/getOutputList",{data:{goods_id:t}})},a.getVerifyStateRemark=function(t){return i.default.post("/shopapi/goods/getVerifyStateRemark",{data:{goods_id:t}})},a.offGoods=function(t){return i.default.post("/shopapi/goods/offGoods",{data:t})},a.onGoods=function(t){return i.default.post("/shopapi/goods/onGoods",{data:t})};var i=o(e("9027"))},a9cb:function(t,a,e){"use strict";e.r(a);var o=e("f5a6"),i=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(r);a["default"]=i.a},dcfa:function(t,a,e){"use strict";var o=e("323b"),i=e.n(o);i.a},f5a6:function(t,a,e){"use strict";e("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("bf0f"),e("2797"),e("aa9c"),e("dd2b"),e("4100"),e("d4b5");var o=e("95a1"),i={data:function(){return{goodsAttrClass:0,goodsAttrName:"",goodsAttrFormat:[],attrClassList:[],attrValueList:[],index:0,pickedArr:[]}},onLoad:function(t){this.goodsAttrClass=uni.getStorageSync("editGoodsAttrClass")||0,this.goodsAttrName=uni.getStorageSync("editGoodsAttrName")||"",this.goodsAttrFormat=uni.getStorageSync("editGoodsAttrFormat")?JSON.parse(uni.getStorageSync("editGoodsAttrFormat")):[],this.getAttrClassListFn()},onShow:function(){},methods:{getAttrClassListFn:function(){var t=this;(0,o.getAttrClassList)().then((function(a){a.data&&(t.attrClassList=a.data,t.attrClassList.forEach((function(a,e){t.pickedArr.push(a.class_name),t.goodsAttrClass&&t.goodsAttrClass==a.class_id&&(t.index=e)})),t.goodsAttrClass?(t.goodsAttrName=t.attrClassList[t.index].class_name,t.getAttributeList()):t.$refs.loadingCover&&t.$refs.loadingCover.hide())}))},getAttributeList:function(){var t=this;(0,o.getAttributeListById)(this.goodsAttrClass).then((function(a){a.data&&(t.attrValueList=a.data,t.goodsAttrFormat.forEach((function(a){a.attr_id<0&&(a.attr_type=3,a.attr_value_format="",t.attrValueList.splice(0,0,a))})),t.attrValueList.forEach((function(a,e){switch(a.sort=e,a.attr_type){case 1:case 2:a.attr_value_format.forEach((function(a){a.checked=!1,t.goodsAttrFormat.forEach((function(t){a.attr_value_id==t.attr_value_id&&(a.checked=!0)}))}));break;case 3:a.attr_id>0&&(a.attr_value_name=""),t.goodsAttrFormat.forEach((function(t){a.attr_id==t.attr_id&&(a.attr_value_name=t.attr_value_name)}));break}})),t.$refs.loadingCover&&t.$refs.loadingCover.hide())}))},bindPickerChange:function(t){if(0!=this.attrClassList.length){this.index=t.target.value,this.goodsAttrClass=this.attrClassList[this.index].class_id,this.goodsAttrName=this.attrClassList[this.index].class_name;for(var a=0;a<this.goodsAttrFormat.length;a++)this.goodsAttrFormat[a].attr_id>0&&(this.goodsAttrFormat.splice(a,1),a=0);this.getAttributeList()}},selectBox:function(t,a,e){1==t&&this.attrValueList[a].attr_value_format.forEach((function(t,a){e!=a&&(t.checked=!1)})),this.attrValueList[a].attr_value_format[e].checked=!this.attrValueList[a].attr_value_format[e].checked,this.$forceUpdate()},addAttr:function(){var t=this.attrValueList.length,a={attr_name:"",attr_value_name:"",attr_type:3,attr_value_format:"",sort:0};a.attr_class_id=-(t+Math.floor((new Date).getSeconds())+Math.floor((new Date).getMilliseconds())),a.attr_id=a.attr_class_id+-(t+Math.floor((new Date).getSeconds())+Math.floor((new Date).getMilliseconds())),a.attr_value_id=a.attr_id+-(t+Math.floor((new Date).getSeconds())+Math.floor((new Date).getMilliseconds())),this.attrValueList.push(a)},deleteAttr:function(t){var a=this;uni.showModal({title:"操作提示",content:"确定要删除此参数吗？",success:function(e){e.confirm&&(a.attrValueList.splice(t,1),a.goodsAttrFormat.splice(t,1))}})},save:function(){var t=this;this.goodsAttrFormat=[],this.attrValueList.forEach((function(a,e){var o={attr_class_id:a.attr_class_id,attr_id:a.attr_id,attr_name:a.attr_name,attr_value_id:"",attr_value_name:"",sort:e};switch(a.attr_type){case 1:case 2:a.attr_value_format.forEach((function(i){i.checked&&(o={attr_class_id:a.attr_class_id,attr_id:a.attr_id,attr_name:a.attr_name,attr_value_id:i.attr_value_id,attr_value_name:i.attr_value_name,sort:e},t.goodsAttrFormat.push(o))}));break;case 3:a.attr_value_name&&(o.attr_value_name=a.attr_value_name,t.goodsAttrFormat.push(o));break}})),uni.setStorageSync("editGoodsAttrClass",this.goodsAttrClass),uni.setStorageSync("editGoodsAttrName",this.goodsAttrName),uni.setStorageSync("editGoodsAttrFormat",JSON.stringify(this.goodsAttrFormat)),uni.navigateBack({delta:1})}}};a.default=i}}]);