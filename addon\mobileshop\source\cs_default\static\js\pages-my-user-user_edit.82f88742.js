(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-user-user_edit"],{"01ec":function(e,r,t){"use strict";t.d(r,"b",(function(){return i})),t.d(r,"c",(function(){return n})),t.d(r,"a",(function(){return a}));var a={loadingCover:t("59c1").default},i=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("v-uni-view",[t("v-uni-view",{staticClass:"item-wrap"},[t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-text",{staticClass:"label"},[e._v("用户名")]),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:e.uid>0?e.user_info.username:"请输入用户名",maxlength:"100",disabled:e.uid>0},model:{value:e.user_info.username,callback:function(r){e.$set(e.user_info,"username",r)},expression:"user_info.username"}})],1),e.uid<=0?[t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-text",{staticClass:"label"},[e._v("密码")]),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入密码",password:"true",maxlength:"100"},model:{value:e.password,callback:function(r){e.password=r},expression:"password"}})],1),t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-text",{staticClass:"label"},[e._v("再次输入密码")]),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请再次输入密码",password:"true",maxlength:"100"},model:{value:e.againPassword,callback:function(r){e.againPassword=r},expression:"againPassword"}})],1)]:e._e(),t("v-uni-view",{staticClass:"form-wrap more-wrap"},[t("v-uni-text",{staticClass:"label"},[e._v("用户组")]),t("v-uni-picker",{staticClass:"picker",attrs:{value:e.pickerCurr,range:e.groupList,"range-key":"group_name"},on:{change:function(r){arguments[0]=r=e.$handleEvent(r),e.pickerChange.apply(void 0,arguments)}}},[t("v-uni-text",{staticClass:"uni-input",class:{"color-tip":!e.user_info.group_id}},[e._v(e._s(e.user_info.group_id?e.groupList[e.pickerCurr].group_name:"请选择用户组"))]),t("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),e.uid>0?t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-text",{staticClass:"label"},[e._v("用户状态")]),t("v-uni-view",{staticClass:"picker"},[t("v-uni-switch",{staticStyle:{transform:"scale(0.7)"},attrs:{checked:1==e.user_info.status},on:{change:function(r){arguments[0]=r=e.$handleEvent(r),e.switchChange.apply(void 0,arguments)}}})],1)],1):e._e()],2),t("v-uni-button",{attrs:{type:"primary"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.save()}}},[e._v("保存")]),e.uid>0?t("loading-cover",{ref:"loadingCover"}):e._e()],1)},n=[]},"25ba":function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i=a(t("2634")),n=a(t("2fdc")),s=a(t("61e2")),o=t("9aa2"),u={data:function(){return{uid:0,user_info:{username:"",status:1,group_id:""},password:"",againPassword:"",groupList:[{group_name:""}],pickerCurr:0,repeatFlag:!1}},onLoad:function(e){var r=this;return(0,n.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r.$util.checkToken("/pages/my/user/user_edit?uid="+r.uid)){t.next=2;break}return t.abrupt("return");case 2:return r.uid=e.uid||0,t.next=5,r.getgroupList();case 5:r.uid&&r.getDetails();case 6:case"end":return t.stop()}}),t)})))()},onShow:function(){},methods:{getDetails:function(){var e=this;(0,o.getUserInfoById)(this.uid).then((function(r){var t=r.data;0==r.code&&t?(e.user_info.username=r.data.username,e.user_info.status=r.data.status,e.user_info.group_id=r.data.group_id,e.findCurrValue(e.groupList)):e.$util.redirectTo("/pages/my/user/user"),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}))},getgroupList:function(){var e=this;return(0,n.default)((0,i.default)().mark((function r(){var t;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,(0,o.getUserGroupList)();case 2:t=r.sent,0==t.code&&(e.groupList=t.data);case 4:case"end":return r.stop()}}),r)})))()},findCurrValue:function(e){for(var r in e)if(e[r].group_id==this.user_info.group_id){this.pickerCurr=r;break}},pickerChange:function(e){0!=this.groupList.length&&(this.pickerCurr=e.target.value,this.user_info.group_id=this.groupList[this.pickerCurr].group_id)},switchChange:function(e){this.user_info.status=e.target.value?1:0},save:function(){var e=this,r=this.user_info;if(this.uid){t=o.editUser;r.uid=this.uid}else{var t=o.addUser;if(r.password=this.password,r.group_id=this.groupList[this.pickerCurr].group_id,!this.verify())return}this.repeatFlag||(this.repeatFlag=!0,t(r).then((function(r){e.$util.showToast({title:r.message}),0==r.code?setTimeout((function(){uni.navigateBack({delta:1})}),1e3):e.repeatFlag=!1})))},verify:function(){var e={username:this.user_info.username,password:this.password,againPassword:this.againPassword},r=s.default.check(e,[{name:"username",checkType:"required",errorMsg:"用户名不能为空"},{name:"password",checkType:"required",errorMsg:"请输入密码"},{name:"againPassword",checkType:"required",errorMsg:"请再次输入密码"}]);return r?this.password==this.againPassword||(this.$util.showToast({title:"两次密码不一致"}),!1):(this.$util.showToast({title:s.default.error}),!1)}}};r.default=u},"501d":function(e,r,t){var a=t("835d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=t("967d").default;i("4697cecf",a,!0,{sourceMap:!1,shadowMode:!1})},"61e2":function(e,r,t){t("23f4"),t("7d2f"),t("5c47"),t("9c4e"),t("ab80"),t("0506"),t("64aa"),t("5ef2"),e.exports={error:"",check:function(e,r){for(var t=0;t<r.length;t++){if(!r[t].checkType)return!0;if(!r[t].name)return!0;if(!r[t].errorMsg)return!0;if(!e[r[t].name])return this.error=r[t].errorMsg,!1;switch(r[t].checkType){case"custom":if("function"==typeof r[t].validate&&!r[t].validate(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"required":var a=new RegExp("/[S]+/");if(a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"string":a=new RegExp("^.{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"digit":a=new RegExp("^(d{0,10}(.?d{0,2}){"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"between":if(!this.isNumber(e[r[t].name]))return this.error=r[t].errorMsg,!1;var i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"same":if(e[r[t].name]!=r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"notsame":if(e[r[t].name]==r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"email":a=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"phoneno":a=/^\d{11}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"reg":a=new RegExp(r[t].checkRule);if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"in":if(-1==r[t].checkRule.indexOf(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"notnull":if(0==e[r[t].name]||void 0==e[r[t].name]||null==e[r[t].name]||e[r[t].name].length<1)return this.error=r[t].errorMsg,!1;break;case"lengthMin":if(e[r[t].name].length<r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"lengthMax":if(e[r[t].name].length>r[t].checkRule)return this.error=r[t].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"835d":function(e,r,t){var a=t("c86c");r=a(!1),r.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-76f5f8fb]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-76f5f8fb]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-76f5f8fb]{position:fixed;left:0;right:0;z-index:998}.container-wrap[data-v-76f5f8fb]{margin-bottom:%?60?%}.item-wrap[data-v-76f5f8fb]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-76f5f8fb]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;min-height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-76f5f8fb]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-76f5f8fb]{font-weight:700}.item-wrap .form-wrap .label[data-v-76f5f8fb]{min-width:%?150?%;vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap .time-change[data-v-76f5f8fb]{display:flex;align-items:center;flex:1;justify-content:flex-end}.item-wrap .form-wrap uni-textarea[data-v-76f5f8fb],\r\n.item-wrap .form-wrap .picker[data-v-76f5f8fb],\r\n.item-wrap .form-wrap uni-input[data-v-76f5f8fb]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap .picker .iconfont[data-v-76f5f8fb]{vertical-align:middle}.item-wrap .form-wrap uni-textarea[data-v-76f5f8fb]{height:%?100?%;padding:%?20?%}.item-wrap .form-wrap .value[data-v-76f5f8fb]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap.more-wrap .selected[data-v-76f5f8fb]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-76f5f8fb]{color:#303133}.item-wrap .form-wrap.more-wrap .flex_1[data-v-76f5f8fb]{flex:1;text-align:right;padding-right:%?20?%}.item-wrap .form-wrap.more-wrap .flex_1 uni-input[data-v-76f5f8fb]{height:%?100?%;display:block}.item-wrap .form-wrap.more-wrap .iconfont[data-v-76f5f8fb]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.goods-img[data-v-76f5f8fb]{display:flex}.item-wrap .form-wrap.goods-img .label[data-v-76f5f8fb]{align-self:flex-start;margin-top:%?20?%}.item-wrap .form-wrap.goods-img .img-list[data-v-76f5f8fb]{padding-top:%?40?%;padding-bottom:%?40?%;padding-left:%?40?%}.item-wrap .form-wrap.goods-img .img-list .add[data-v-76f5f8fb]{position:relative;width:%?140?%;text-align:center;border:1px dashed #ccc;font-weight:700;color:#909399}.item-wrap .form-wrap.goods-img .img-list .add .iconfont[data-v-76f5f8fb]{font-size:%?40?%}.item-wrap .form-wrap.goods-img .img-list .add.logo[data-v-76f5f8fb]{height:%?84?%;line-height:%?84?%}.item-wrap .form-wrap.goods-img .img-list .add.avatar[data-v-76f5f8fb]{height:%?140?%;line-height:%?140?%}.item-wrap .form-wrap.goods-img .img-list .add.banner[data-v-76f5f8fb]{height:%?120?%;line-height:%?120?%}.item-wrap .form-wrap.goods-img .img-list .add uni-image[data-v-76f5f8fb]{width:100%;height:100%}.item-wrap .form-wrap.goods-img .img-list .add .del-wrap[data-v-76f5f8fb]{position:absolute;top:%?-16?%;right:%?-16?%;line-height:1;width:16px;height:16px;background-color:rgba(0,0,0,.5);border-radius:50%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;font-size:12px;color:#fff;font-weight:700}.item-wrap .form-wrap.goods-img .tips[data-v-76f5f8fb]{color:#909399;font-size:%?20?%;margin-top:%?20?%;word-wrap:break-word;word-break:break-all}.footer-wrap[data-v-76f5f8fb]{width:100%;padding:%?40?% 0;z-index:10;padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}uni-button[data-v-76f5f8fb]{margin-top:%?40?%}',""]),e.exports=r},"9aa2":function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.addUser=function(e){return i.default.post("/shopapi/user/addUser",{data:e})},r.deleteUser=function(e){return i.default.post("/shopapi/user/deleteUser",{data:{uid:e}})},r.editUser=function(e){return i.default.post("/shopapi/user/editUser",{data:e})},r.editUserPassword=function(e){return i.default.post("/shopapi/user/modifyPassword",{data:e})},r.getUserGroupList=function(){return i.default.get("/shopapi/user/groupList")},r.getUserInfoById=function(e){return i.default.post("/shopapi/user/info",{data:{uid:e}})},r.getUserList=function(e){return i.default.post("/shopapi/user/user",{data:e})},r.getUserPermission=function(){return i.default.get("/shopapi/user/permission")};var i=a(t("9027"))},"9b82":function(e,r,t){"use strict";t.r(r);var a=t("25ba"),i=t.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return a[e]}))}(n);r["default"]=i.a},c309:function(e,r,t){"use strict";t.r(r);var a=t("01ec"),i=t("9b82");for(var n in i)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return i[e]}))}(n);t("dcfa5");var s=t("828b"),o=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"76f5f8fb",null,!1,a["a"],void 0);r["default"]=o.exports},dcfa5:function(e,r,t){"use strict";var a=t("501d"),i=t.n(a);i.a}}]);