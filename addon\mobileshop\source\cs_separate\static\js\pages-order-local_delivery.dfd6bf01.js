(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-local_delivery"],{"3dae":function(e,t,r){"use strict";var i=r("e4aa"),a=r.n(i);a.a},"4be0":function(e,t,r){"use strict";r.r(t);var i=r("831a"),a=r.n(i);for(var d in i)["default"].indexOf(d)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(d);t["default"]=a.a},5810:function(e,t,r){var i=r("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-3b263b26]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-3b263b26]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-3b263b26]{position:fixed;left:0;right:0;z-index:998}.item-wrap[data-v-3b263b26]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-3b263b26]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-3b263b26]:last-child{border-bottom:none}.item-wrap .form-wrap .label[data-v-3b263b26]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap .value[data-v-3b263b26]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap uni-input[data-v-3b263b26]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap .selected[data-v-3b263b26]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.footer-wrap[data-v-3b263b26]{width:100%;padding:%?40?% 0}',""]),e.exports=t},6638:function(e,t,r){"use strict";r("6a54");var i=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.adjustOrderPrice=function(e){return a.default.post("/shopapi/order/adjustPrice",{data:e})},t.closeOrder=function(e){return a.default.post("/shopapi/order/close",{data:{order_id:e}})},t.deliveryOrder=function(e){return a.default.post("/shopapi/order/delivery",{data:e})},t.editOrderDelivery=function(e){return a.default.post("/shopapi/order/editOrderDelivery",{data:e})},t.editOrderInvoicelist=function(e){return a.default.post("/shopapi/order/invoiceEdit",{data:e})},t.getOrderCondition=function(){return a.default.get("/shopapi/order/condition")},t.getOrderDetailById=function(e){return a.default.post("/shopapi/order/getOrderDetail",{data:{order_id:e}})},t.getOrderDetailInfoById=function(e){return a.default.post("/shopapi/order/detail",{data:{order_id:e}})},t.getOrderGoodsList=function(e){return a.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:e}})},t.getOrderInfoById=function(e){return a.default.post("/shopapi/order/getOrderInfo",{data:{order_id:e}})},t.getOrderInvoicelist=function(e){return a.default.post("/shopapi/order/invoicelist",{data:e})},t.getOrderList=function(e){return a.default.post("/shopapi/order/lists",{data:e})},t.getOrderLog=function(e){return a.default.post("/shopapi/order/log",{data:{order_id:e}})},t.getOrderPackageList=function(e){return a.default.post("/shopapi/order/package",{data:{order_id:e}})},t.ordErtakeDelivery=function(e){return a.default.post("/shopapi/order/takeDelivery",{data:{order_id:e}})},t.orderExtendTakeDelivery=function(e){return a.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:e}})},t.orderLocalorderDelivery=function(e){return a.default.post("/shopapi/localorder/delivery",{data:e})},t.orderOfflinePay=function(e){return a.default.post("/shopapi/order/offlinePay",{data:{order_id:e}})},t.orderVirtualDelivery=function(e){return a.default.post("/shopapi/virtualorder/delivery",{data:{order_id:e}})},t.storeOrderTakeDelivery=function(e){return a.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:e}})};var a=i(r("9027"))},"831a":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("bf0f"),r("2797"),r("aa9c");var i=r("6638"),a={data:function(){return{order:{},repeatFlag:!1,data:{order_id:0,deliver_id:0,deliverer:"请选择",deliverer_mobile:""},deliverArray:[],deliver_index:0}},onLoad:function(e){this.data.order_id=e.order_id||0,this.getOrderInfo()},onShow:function(){},methods:{deliverChange:function(e){0!=this.deliverArray.length&&(this.deliver_index=e.target.value,this.data.deliver_id=this.order["deliver_list"][this.deliver_index].deliver_id,this.data.deliverer=this.order["deliver_list"][this.deliver_index].deliver_name,this.data.deliverer_mobile=this.order["deliver_list"][this.deliver_index].deliver_mobile)},getOrderInfo:function(){var e=this;(0,i.getOrderInfoById)(this.data.order_id).then((function(t){0==t.code?(e.order=t.data,e.order.deliver_list&&e.order.deliver_list.forEach((function(t,r){e.deliverArray.push(t.deliver_name)})),e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.$util.showToast({title:t.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3))}))},save:function(){var e=this;0!=this.data.deliver_id?0!=this.data.deliverer_mobile?this.repeatFlag||(this.repeatFlag=!0,(0,i.orderLocalorderDelivery)(this.data).then((function(t){0==t.code?setTimeout((function(){uni.navigateBack({delta:1})}),1e3):e.repeatFlag=!1,e.$util.showToast({title:t.message})}))):this.$util.showToast({title:"请输入手机号"}):this.$util.showToast({title:"请选择配送员"})}}};t.default=a},bf7d:function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return d})),r.d(t,"a",(function(){return i}));var i={loadingCover:r("59c1").default},a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",[r("v-uni-view",{staticClass:"item-wrap"},[r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("收货地址")]),r("v-uni-text",{staticClass:"value"},[e._v(e._s(e.order.full_address)+" "+e._s(e.order.address))])],1),r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("发货方式")]),r("v-uni-text",{staticClass:"value"},[e._v("商家自配送")])],1),r("v-uni-view",{staticClass:"form-wrap more-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("配送员")]),r("v-uni-picker",{staticClass:"selected",attrs:{value:e.deliver_index,range:e.deliverArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.deliverChange.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.data["deliverer"]))])],1),r("v-uni-text",{staticClass:"iconfont iconright"})],1),r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-view",{staticClass:"label"},[r("v-uni-text",{staticClass:"required color-base-text"},[e._v("*")]),r("v-uni-text",[e._v("手机号")])],1),r("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"请输入手机号"},model:{value:e.data.deliverer_mobile,callback:function(t){e.$set(e.data,"deliverer_mobile",t)},expression:"data.deliverer_mobile"}})],1)],1),r("v-uni-view",{staticClass:"footer-wrap"},[r("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save()}}},[e._v("确定")])],1),r("loading-cover",{ref:"loadingCover"})],1)},d=[]},cb06:function(e,t,r){"use strict";r.r(t);var i=r("bf7d"),a=r("4be0");for(var d in a)["default"].indexOf(d)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(d);r("3dae");var o=r("828b"),n=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"3b263b26",null,!1,i["a"],void 0);t["default"]=n.exports},e4aa:function(e,t,r){var i=r("5810");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=r("967d").default;a("0c51bc68",i,!0,{sourceMap:!1,shadowMode:!1})}}]);