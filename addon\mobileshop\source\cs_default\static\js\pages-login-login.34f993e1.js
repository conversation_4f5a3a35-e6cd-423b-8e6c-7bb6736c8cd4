(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login"],{"13e6":function(e,t,r){"use strict";r("6a54");var i=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getCaptcha=function(e){return n.default.post("/shopapi/captcha/captcha",{data:{captcha_id:e}})},t.login=function(e){return n.default.post("/shopapi/login/login",{data:e})},t.modifyPassword=function(e){return n.default.post("/shopapi/login/modifyPassword",{data:e})};var n=i(r("9027"))},"17d8":function(e,t,r){"use strict";var i=r("2242"),n=r.n(i);n.a},2242:function(e,t,r){var i=r("42b2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=r("967d").default;n("7c08041c",i,!0,{sourceMap:!1,shadowMode:!1})},"42b2":function(e,t,r){var i=r("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-0b245273]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-0b245273]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-0b245273]{position:fixed;left:0;right:0;z-index:998}uni-page-body[data-v-0b245273]{background-color:#fff}body.?%PAGE?%[data-v-0b245273]{background-color:#fff}.login[data-v-0b245273]{margin:0 %?60?% 0;padding-top:%?180?%}.login .login-title[data-v-0b245273]{font-size:%?60?%;font-weight:500;display:inline-block;margin-bottom:%?50?%}.login .login-input[data-v-0b245273]{display:flex;height:%?70?%;margin:%?50?% auto 0;align-items:center;justify-content:center;border-bottom:1px solid #eee;border-radius:%?10?%;padding:%?6?% %?10?%}.login .login-input .iconfont[data-v-0b245273]{font-size:%?40?%}.login .login-input .uni-input[data-v-0b245273]{flex:1;margin-left:%?20?%}.login .login-input .code[data-v-0b245273]{width:%?150?%;height:%?70?%}.login uni-button[data-v-0b245273]{margin:%?180?% auto 0}.login .login-text[data-v-0b245273]{text-align:center;margin:%?50?% auto 0;color:#909399}',""]),e.exports=t},"5ecf":function(e,t,r){"use strict";r.r(t);var i=r("dbd6"),n=r.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(a);t["default"]=n.a},"61e2":function(e,t,r){r("23f4"),r("7d2f"),r("5c47"),r("9c4e"),r("ab80"),r("0506"),r("64aa"),r("5ef2"),e.exports={error:"",check:function(e,t){for(var r=0;r<t.length;r++){if(!t[r].checkType)return!0;if(!t[r].name)return!0;if(!t[r].errorMsg)return!0;if(!e[t[r].name])return this.error=t[r].errorMsg,!1;switch(t[r].checkType){case"custom":if("function"==typeof t[r].validate&&!t[r].validate(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"required":var i=new RegExp("/[S]+/");if(i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"string":i=new RegExp("^.{"+t[r].checkRule+"}$");if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"int":i=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[r].checkRule+"}$");if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"digit":i=new RegExp("^(d{0,10}(.?d{0,2}){"+t[r].checkRule+"}$");if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[r].name]))return this.error=t[r].errorMsg,!1;var n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"betweenD":i=/^-?[1-9][0-9]?$/;if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"betweenF":i=/^-?[0-9][0-9]?.+[0-9]+$/;if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;n=t[r].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[t[r].name]>n[1]||e[t[r].name]<n[0])return this.error=t[r].errorMsg,!1;break;case"same":if(e[t[r].name]!=t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"notsame":if(e[t[r].name]==t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"email":i=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"phoneno":i=/^\d{11}$/;if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"zipcode":i=/^[0-9]{6}$/;if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"reg":i=new RegExp(t[r].checkRule);if(!i.test(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"in":if(-1==t[r].checkRule.indexOf(e[t[r].name]))return this.error=t[r].errorMsg,!1;break;case"notnull":if(0==e[t[r].name]||void 0==e[t[r].name]||null==e[t[r].name]||e[t[r].name].length<1)return this.error=t[r].errorMsg,!1;break;case"lengthMin":if(e[t[r].name].length<t[r].checkRule)return this.error=t[r].errorMsg,!1;break;case"lengthMax":if(e[t[r].name].length>t[r].checkRule)return this.error=t[r].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"8e0b":function(e,t,r){"use strict";r.r(t);var i=r("b204"),n=r("5ecf");for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(a);r("17d8");var o=r("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"0b245273",null,!1,i["a"],void 0);t["default"]=s.exports},b204:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return a})),r.d(t,"a",(function(){return i}));var i={loadingCover:r("59c1").default},n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"login"},[r("v-uni-view",{staticClass:"login-title"},[e._v("商家登录")]),r("v-uni-view",{staticClass:"login-input"},[r("v-uni-view",{staticClass:"iconfont icon06_huiyuanguanli color-base-text"}),r("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入用户名"},model:{value:e.loginList.username,callback:function(t){e.$set(e.loginList,"username",t)},expression:"loginList.username"}})],1),r("v-uni-view",{staticClass:"login-input"},[r("v-uni-view",{staticClass:"iconfont iconmima color-base-text"}),r("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入密码",password:"true"},model:{value:e.loginList.password,callback:function(t){e.$set(e.loginList,"password",t)},expression:"loginList.password"}})],1),1==e.captchaConfig?r("v-uni-view",{staticClass:"login-input"},[r("v-uni-view",{staticClass:"iconfont iconyanzhengma1 color-base-text"}),r("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入验证码",type:"number"},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.loginFn()}},model:{value:e.loginList.vcode,callback:function(t){e.$set(e.loginList,"vcode",t)},expression:"loginList.vcode"}}),r("v-uni-image",{staticClass:"code",attrs:{src:e.captcha.img,mode:"aspectFit"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getImg.apply(void 0,arguments)}}})],1):e._e(),r("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.loginFn()}}},[e._v("登录")]),r("loading-cover",{ref:"loadingCover"})],1)},a=[]},dbd6:function(e,t,r){"use strict";r("6a54");var i=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("5c47"),r("a1c1"),r("aa9c");var n=i(r("61e2")),a=r("e5a3"),o=r("13e6"),s=r("e08b"),c={data:function(){return{loginList:{username:"",password:"",vcode:""},captchaConfig:1,captcha:{id:"",img:""},back:"",redirect:"redirectTo"}},onLoad:function(e){uni.getStorageSync("token")&&uni.removeStorageSync("token"),uni.getStorageSync("site_id")&&uni.removeStorageSync("site_id"),this.back=e.back||"",this.getCaptchaConfigFn()},methods:{getImg:function(){this.getCodeImg()},getCaptchaConfigFn:function(){var e=this;(0,a.getCaptchaConfig)().then((function(t){t.code>=0&&t.data&&(e.captchaConfig=t.data.shop_login,1==e.captchaConfig?e.getCodeImg():e.$refs.loadingCover&&e.$refs.loadingCover.hide())}))},getCodeImg:function(){var e=this;(0,o.getCaptcha)(this.captcha.id).then((function(t){t.code>=0&&t.data&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,"")),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}))},loginFn:function(){var e=this;this.verify()&&(0,o.login)({captcha_id:this.captcha.id,username:this.loginList.username,password:this.loginList.password,captcha_code:this.loginList.vcode}).then((function(t){t.code>=0?(e.$util.showToast({title:"登录成功"}),uni.setStorageSync("token",t.data.token),uni.setStorageSync("site_id",t.data.site_id),t.data.site_id>0?""!=e.back?e.$util.redirectTo(decodeURIComponent(e.back),{},e.redirect):e.$util.redirectTo("/pages/index/index",{},e.redirect):e.getShopStatus()):(e.getImg(),e.$util.showToast({title:t.message}))}))},getShopStatus:function(){var e=this;(0,s.getApplyIndex)().then((function(t){var r=t.data;0==t.code&&r&&(1==t.data.procedure?e.$util.redirectTo("/pages/apply/mode",{},"reLaunch"):e.$util.redirectTo("/pages/apply/audit",{},"reLaunch"))}))},verify:function(){var e=[];e=[{name:"username",checkType:"required",errorMsg:"请输入用户名"},{name:"password",checkType:"required",errorMsg:"请输入密码"}],1==this.captchaConfig&&""!=this.captcha.id&&e.push({name:"vcode",checkType:"required",errorMsg:"请输入验证码"});var t=n.default.check(this.loginList,e);return!!t||(this.$util.showToast({title:n.default.error}),!1)}}};t.default=c},e08b:function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.getApplyIndex=function(){return request.get("/shopapi/apply/index")}},e5a3:function(e,t,r){"use strict";r("6a54");var i=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getCaptchaConfig=function(){return n.default.get("/shopapi/config/captchaConfig")},t.getGoodsConfig=function(){return n.default.get("/shopapi/goods/config")},t.getOrderConfig=function(){return n.default.get("/shopapi/order/config")},t.setGoodsConfig=function(e){return n.default.post("/shopapi/goods/setconfig",{data:e})},t.setOrderConfig=function(e){return n.default.post("/shopapi/order/setconfig",{data:e})},t.setShopConfig=function(e){return n.default.post("/shopapi/shop/config",{data:e})};var n=i(r("9027"))}}]);