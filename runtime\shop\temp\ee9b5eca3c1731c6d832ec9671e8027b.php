<?php /*a:2:{s:45:"E:\aicode\shop\app\shop\view\login\login.html";i:1741057066;s:45:"E:\aicode\shop\app\shop\view\layout\base.html";i:1741057066;}*/ ?>
<!DOCTYPE html>
<html>
<head>
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
	<title><?php echo htmlentities((isset($shop_info['site_name']) && ($shop_info['site_name'] !== '')?$shop_info['site_name']:"")); ?></title>
	<meta name="keywords" content="<?php echo isset($shop_info['seo_keywords']) ? htmlentities($shop_info['seo_keywords']) : ''; ?>">
	<meta name="description" content="<?php echo isset($shop_info['seo_description']) ? htmlentities($shop_info['seo_description']) : ''; ?>">
	<link rel="icon" type="image/x-icon" href="http://**********/public/static/img/shop_bitbug_favicon.ico" />
	<?php if(!(empty($load_diy_icon_url) || (($load_diy_icon_url instanceof \think\Collection || $load_diy_icon_url instanceof \think\Paginator ) && $load_diy_icon_url->isEmpty()))): ?>
		<!-- 加载自定义图标库 -->
		<?php echo implode('',$load_diy_icon_url); ?>
	<?php endif; ?>
	<link rel="stylesheet" type="text/css" href="http://**********/public/static/css/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="http://**********/public/static/ext/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="http://**********/app/shop/view/public/css/template/<?php echo htmlentities($theme_config['url']); ?>" />
	<link rel="stylesheet" type="text/css" href="http://**********/app/shop/view/public/css/common.css?time=20240822" />
	<script src="http://**********/public/static/js/jquery-3.1.1.js"></script>
	<script src="http://**********/public/static/js/jquery.cookie.js"></script>
	<script src="http://**********/public/static/ext/layui/layui.js"></script>
	<script>
		layui.use(['layer', 'upload', 'element'], function() {});
		//全局定义一次, 加载formSelects
		layui.extend({
			formSelects: 'http://**********/public/static/ext/layui/extend/formSelects-v4',
			layCascader: 'http://**********/public/static/ext/layui/extend/cascader/cascader',
			dropdown: 'http://**********/public/static/ext/layui/extend/dropdown/dropdown'
		});
		window.ns_url = {
			baseUrl: "http://**********/",
			route: ['<?php echo request()->module(); ?>', '<?php echo request()->controller(); ?>', '<?php echo request()->action(); ?>'],
			appModule: '<?php echo isset($app_module) ? htmlentities($app_module) : ""; ?>',
			siteId: '<?php echo request()->siteid(); ?>',
			shopImg: 'http://**********/app/shop/view/public/img',
			staticImg: 'http://**********/public/static/img',
			staticExt: 'http://**********/public/static/ext',
			uploadMaxFileSize: '<?php echo isset($upload_max_filesize) ? htmlentities($upload_max_filesize) : 0; ?>',
			siteName : "<?php echo htmlentities($shop_info['site_name']); ?>",
		};
		window.regexp_config = <?php echo json_encode(config('regexp')); ?>;
	</script>
	<script src="http://**********/public/static/js/common.js?v=20241114"></script>
	<script src="http://**********/app/shop/view/public/js/common.js?time=20241114"></script>
</head>
<body>
<link rel="stylesheet" href="http://**********/app/shop/view/public/css/login.css?time=20240614">
<style type="text/css"></style>
<div class="container">
	<div class="head-wrap">
		<div class="main-wrap">
			<div class="login-wrap">
				<img src="<?php if(!empty($copyright['logo'])): ?> <?php echo img($copyright['logo']); else: ?>http://**********/public/static/img/copyright_logo.png<?php endif; ?>" />
			</div>

			<?php if(!(empty($port_data) || (($port_data instanceof \think\Collection || $port_data instanceof \think\Paginator ) && $port_data->isEmpty()))): ?>
			<div class="login-iph">
				<div class="show">
					<i class="iconfont iconico text-color"></i>
					<span>手机管理端</span>
					<div class="log-type">
						<div class="type-wrap">
							<?php if(is_array($port_data) || $port_data instanceof \think\Collection || $port_data instanceof \think\Paginator): $i = 0; $__LIST__ = $port_data;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
							<div class="type-item">
								<div class="item-img">
									<img src="<?php echo img($vo['img']); ?>"  style="width: 100%">
								</div>
								<span><?php echo htmlentities($vo['message']); ?></span>
							</div>
							<?php endforeach; endif; else: echo "" ;endif; ?>
						</div>
					</div>
				</div>
			</div>
			<?php endif; ?>
		</div>
	</div>

	<div class="body-wrap">
		<div class="main-wrap">
			<img src="http://**********/app/shop/view/public/img/login-left.png" alt="" class="login-leftbg">

			<div class="form-wrap layui-form">
				<h2 class="login-title">智慧门店·管理中心</h2>

				<div class="input-wrap">
					<div class="icon">
						<i class="iconfont iconhuiyuan1"></i>
					</div>
					<input type="text" name="username" lay-verify="userName" placeholder="请输入用户名" autocomplete="off" class="layui-input input">
				</div>

				<div class="input-wrap">
					<div class="icon">
						<i class="iconfont iconmima"></i>
					</div>
					<input type="password" name="password" lay-verify="password" placeholder="请输入密码" autocomplete="off" class="layui-input input">
				</div>

				<?php if($shop_login == 1): ?>
				<div class="input-wrap">
					<div class="icon">
						<i class="iconfont iconyanzhengma"></i>
					</div>
					<input type="text" name="captcha" lay-verify="verificationCode" placeholder="请输入验证码" autocomplete="off" class="layui-input input">
					<img id='verify_img' src="<?php echo htmlentities($captcha['img']); ?>" alt='captcha' onclick="verificationCode()" class="captcha"/>
					<input type="hidden" name="captcha_id" value="<?php echo htmlentities($captcha['id']); ?>">
				</div>
				<?php endif; ?>

				<button type="button" class="layui-btn bg-color login-btn" lay-submit lay-filter="login">登录</button>
			</div>
		</div>
	</div>

	<div class="footer-wrap"></div>

</div>
<script type="text/javascript">

	// 设置网站标题
	document.title = '登录 - ' + window.ns_url.siteName;

	// 二维码
	var tip_index = 0;
	$(document).on('mouseover', '#goodTitleMsg', function(data){
		var details = data.currentTarget.lastChild.defaultValue;
		if(details!=""){
			tip_index =  layer.tips("<span style='font-size:13px;line-height:20px;'>"+details+"</span>", ($(this)),{ tips: [3, '5CBA59'],time:0,time:0,area: ['200px']});
		}
	}).on('mouseleave', '#goodTitleMsg', function(){
		layer.close(tip_index);
	});

	var form, login_repeat_flag = false,carousel;
	/**
	 * 验证码
	 */
	function verificationCode(){
		$.ajax({
			type: "get",
			url: "<?php echo url('shop/login/captcha'); ?>",
			dataType: "JSON",
			async: false,
			success: function (res) {
				var data = res.data;
				$("#verify_img").attr("src",data.img);
				$("input[name='captcha_id']").val(data.id);
			}
		});
	}

	layui.use(['form','carousel'], function(){
		form = layui.form;
		carousel = layui.carousel;
		form.render();

		/* 登录 */
		form.on('submit(login)', function(data) {
			if (login_repeat_flag) return;
			login_repeat_flag = true;

			$.ajax({
				type: "POST",
				dataType: "JSON",
				url: '<?php echo url("shop/login/login"); ?>',
				data: data.field,
				success: function(res) {
					if (res.code == 0) {
						layer.msg('登录成功',{anim: 5,time: 500},function () {
							window.location =  ns.href();
						});
					} else {
						layer.msg(res.message);
						login_repeat_flag = false;
						verificationCode();
					}

				}
			})
		});

		/*
		* 轮播
		* */
		carousel.render({
			elem: '#logCarousel'
			,width: '100%' //设置容器宽度
			,height: '100%'
			,arrow: 'none' //始终显示箭头
			,anim: 'fade'
			,indicator: 'none'
		});

		/**
		 * 表单验证
		 */
		form.verify({
			userName: function(value) {
				if (!value.trim()) {
					return "账号不能为空";
				}
			},
			password: function(value) {
				if (!value.trim()) {
					return "密码不能为空";
				}
			},
			verificationCode: function(value) {
				if (!value.trim()) {
					return "验证码不能为空";
				}
			}

		});
	});
	
	$("body").off("blur",".login-content .login-input").on("blur",".login-content .login-input",function(){
		$(this).removeClass("login-input-select");
	});
	$("body").off("focus",".login-content .login-input").on("focus",".login-content .login-input",function(){
		$(this).addClass("login-input-select");
	});

	$(document).keydown(function (event) {
		if (event.keyCode == 13) {
			$(".login-btn").trigger("click");
		}
	});
</script>
<script type="text/html" id="reset_pass">
	<!-- 重置密码弹框html -->
	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>原密码</label>
			<div class="layui-input-block">
				<input type="password" id="old_pass" name="old_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="new_pass" name="new_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>确认新密码</label>
			<div class="layui-input-block">
				<input type="password" id="repeat_pass" name="repeat_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="form-row mid">
			<button class="layui-btn" onclick="repass()">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="layer.closeAll()">返回</button>
		</div>
	</div>
</script>
<script type="text/html" id="patch_alert">
	<table class="layui-table">
		<colgroup>
			<col width="30%">
			<col width="70%">
		</colgroup>
		<thead>
		<tr>
			<th>补丁名称</th>
			<th>补丁说明</th>
		</tr>
		</thead>
		<tbody>
			{{# d.forEach((item)=>{ }}
			<tr>
				<td>{{item.patch_name}}</td>
				<td>{{item.patch_desc}}</td>
			</tr>
			{{# }) }}
		</tbody>
	</table>
</script>
</body>
</html>