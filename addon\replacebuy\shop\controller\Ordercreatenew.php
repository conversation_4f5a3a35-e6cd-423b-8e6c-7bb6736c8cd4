<?php

/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\replacebuy\shop\controller;

use addon\replacebuy\model\ReplacebuyOrderCreate as OrderCreateModel;
use Ash\RandomNickname;
use app\model\member\Member as MemberModel;
use think\facade\Db;

/**
 * 订单
 * Class Order
 * @package app\shop\controller
 */
class Ordercreatenew
{
    /**
     * 创建订单2
     */
    // public function create()
    // {
    //     $order_create = new OrderCreateModel();
    //     // 获取订单号
    //     $out_trade_no = input('out_trade_no', '');
    //     if (empty($out_trade_no)) {
    //         return $order_create->error([], "订单号不能为空");
    //     }
    //     $order = Db::name('order')->where('out_trade_no', $out_trade_no)->find();
    //     if ($order) {
    //         return $order_create->error([], "订单号已存在,请勿重复提交");
    //     }

    //     // 获取价格
    //     $price = input('price', 0);
    //     if ($price <= 0) {
    //         return $order_create->error([], "价格不能为0");
    //     }

    //     if ($price <= 50) {
    //         $goods = Db::name('goods')
    //             ->where('price', 1)
    //             ->where('is_delete', 0)
    //             ->where('is_virtual', 1)
    //             ->orderRaw('rand()')
    //             ->find();
    //         if (!$goods) {
    //             return $order_create->error([], "没有找到商品");
    //         }
    //         // 计算优惠金额
    //         $num = ceil($price);
    //         $coupon_money = $num - $price;
    //     } else {
    //         // 根据$price,从googs表中匹配金额大于$price
    //         $goods = Db::name('goods')
    //             ->where('price', '>', $price)
    //             ->where('is_delete', 0)
    //             ->where('is_virtual', 1)
    //             ->order('price', 'asc')
    //             ->find();

    //         // 如果获取到就返回错误
    //         if (!$goods) {
    //             return $order_create->error([], "没有找到商品");
    //         }
    //         // 计算优惠金额
    //         $coupon_money = $goods['price'] - $price;
    //         $num = 1;
    //     }
    //     $coupon_money = round($coupon_money, 2);

    //     // 模拟购物车
    //     $cart = '[{"sku_id":"' . $goods['goods_id'] . '","sku_name":"' . $goods['goods_name'] . '","stock":"","sku_price":"' . $goods['price'] . '","sku_image":"' . $goods['goods_image'] . '","spec_name":"","num":' . $num . ',"LAY_TABLE_INDEX":0}]';
    //     $cart_data_result = $this->tranSkuData($cart);
    //     if ($cart_data_result["code"] < 0) {
    //         return $cart_data_result;
    //     }
    //     $cart_data = $cart_data_result["data"];
    //     $sku_ids = $cart_data["sku_ids"];
    //     $nums = $cart_data["nums"];

    //     // 随机获取一个未删除的会员ID
    //     $buyer_member = Db::name('member')
    //         ->where('is_delete', 0)
    //         ->orderRaw('rand()')
    //         ->find();
    //     $buyer_member_id = $buyer_member['member_id'];
    //     if ($buyer_member_id <= 0)
    //         return $order_create->error([], "当前没有登录会员");


    //     // 订单数据初始化
    //     $data = [
    //         'sku_ids' => $sku_ids,
    //         'nums' => $nums,
    //         'site_id' => 1,
    //         'member_id' => $buyer_member_id,
    //         'is_balance' => 0, //是否使用余额
    //         'is_point' => 0, //是否使用积分
    //         'is_open_card' => 0,
    //         'order_from' => "h5",
    //         'order_from_name' => "H5",
    //         'coupon_money' => $coupon_money,
    //         'address_id' => 0, //收货地址ID

    //     ];
    //     $res = $order_create->setParam($data)->orderPayment();

    //     // 创建订单
    //     $data = [
    //         'site_id' => 1,
    //         'order_key' => $res['order_key'], //订单缓存
    //         'member_id' => $buyer_member_id,
    //         'is_balance' => 0, //是否使用余额
    //         'order_from' => "h5",
    //         'order_from_name' => "H5",
    //         "buyer_message" => '',
    //         'out_trade_no' => $out_trade_no,
    //         'member_address' => [
    //             'mobile' => $buyer_member['mobile']
    //         ],
    //         'app_module' => 'h5'
    //     ];

    //     $res = $order_create->setParam($data)->create();

    //     return $res;
    // }
    public function create()
    {
        $order_create = new OrderCreateModel();
        // 获取订单号
        $out_trade_no = input('out_trade_no', '');
        $create_time = input('create_time', '');
        $discount = input('discount', '');
        if (empty($out_trade_no)) {
            return $order_create->error([], "订单号不能为空");
        }
        $order = Db::name('order')->where('out_trade_no', $out_trade_no)->find();
        if ($order) {
            return $order_create->error([], "订单号已存在,请勿重复提交");
        }

        // 获取价格
        $price = input('price', 0);
        if ($price <= 0) {
            return $order_create->error([], "价格不能为0");
        }

        // 查找大于等于price的最小价格
        $min_price = Db::name('goods_sku')
            ->where('price', '>=', $price)
            ->where('is_delete', 0)
            ->where('is_virtual', 1)
            ->order('price', 'asc')
            ->value('price');

        if ($min_price) {
            // 查找所有等于最小价格的商品
            $goods_list = Db::name('goods_sku')
                ->where('price', $min_price)
                ->where('is_delete', 0)
                ->where('is_virtual', 1)
                ->select()
                ->toArray();

            // 随机挑选一个
            $goods = $goods_list[array_rand($goods_list)];
        } else {
            // 如果没有找到合适的商品，则从价格为1的商品中随机挑选一个
            $goods_list = Db::name('goods_sku')
                ->where('price', 1)
                ->where('is_delete', 0)
                ->where('is_virtual', 1)
                ->select()
                ->toArray();

            if (empty($goods_list)) {
                return $order_create->error([], "没有找到商品");
            }
            $goods = $goods_list[array_rand($goods_list)];
        }

        // 计算优惠金额和数量
        if ($goods['price'] == 1) {
            $num = ceil($price);
            $coupon_money = $num - $price;
        } else {
            $num = ceil($price / $goods['price']);
            $coupon_money = $num * $goods['price'] - $price;
        }
        $coupon_money = round($coupon_money, 2);
        if (!empty($discount)) {
            $coupon_money = $coupon_money + $discount;
        }
        // echo $goods['sku_id'];
        // die;
        // 模拟购物车
        $cart = '[{"sku_id":"' . $goods['sku_id'] . '","sku_name":"' . $goods['sku_name'] . '","stock":"","sku_price":"' . $goods['price'] . '","sku_image":"' . $goods['sku_image'] . '","spec_name":"","num":' . $num . ',"LAY_TABLE_INDEX":0}]';
        $cart_data_result = $this->tranSkuData($cart);
        if ($cart_data_result["code"] < 0) {
            return $cart_data_result;
        }
        $cart_data = $cart_data_result["data"];
        $sku_ids = $cart_data["sku_ids"];
        $nums = $cart_data["nums"];

        // 随机获取一个未删除的会员ID
        $buyer_member = Db::name('member')
            ->where('is_delete', 0)
            ->orderRaw('rand()')
            ->find();
        $buyer_member_id = $buyer_member['member_id'];
        if ($buyer_member_id <= 0)
            return $order_create->error([], "当前没有登录会员");

        // 订单数据初始化
        $data = [
            'sku_ids' => $sku_ids,
            'nums' => $nums,
            'site_id' => 1,
            'member_id' => $buyer_member_id,
            'is_balance' => 0, //是否使用余额
            'is_point' => 0, //是否使用积分
            'is_open_card' => 0,
            'order_from' => "h5",
            'order_from_name' => "H5",
            'coupon_money' => $coupon_money,
            'address_id' => 0, //收货地址ID
            'delivery' => ['delivery_type' => '']
        ];

        $res = $order_create->setParam($data)->orderPayment();


        // 创建订单
        $data = [
            'site_id' => 1,
            'order_key' => $res['order_key'], //订单缓存
            'member_id' => $buyer_member_id,
            'is_balance' => 0, //是否使用余额
            'order_from' => "h5",
            'order_from_name' => "H5",
            "buyer_message" => '',
            'out_trade_no' => $out_trade_no,
            'member_address' => [
                'mobile' => $buyer_member['mobile']
            ],
            'app_module' => 'h5'
        ];
        if ($create_time) {
            $data['create_time'] = $create_time;
        }
        $res = $order_create->setParam($data)->create();
        // print_r($res);
        // exit;
        return $res;
    }
    /**
     * 批量注册会员
     */
    public function batchAddMember()
    {
        for ($i = 0; $i < 2000; $i++) {
            $mobile = $this->generateMobileNumber();
            $data = [
                'username' => $mobile,
                'mobile' => $mobile,
                'password' => '123456',
                'nickname' => RandomNickname::generate()
            ];
            $this->addMember($data);
        }
        return 'success';
    }

    /**
     * 会员添加
     */
    public function addMember($data)
    {
        $data = [
            'site_id' => 1,
            'username' => $data['username'],
            'mobile' => $data['mobile'],
            'email' => '',
            'password' => data_md5($data['password']),
            'status' => 1,
            'headimg' => '',
            'member_level' => 1,
            'member_level_name' => '普通会员',
            'nickname' => $data['nickname'],
            'sex' => 0,
            'birthday' => 0,
            'realname' => '',
            'reg_time' => time(),
        ];

        $member_model = new MemberModel();
        return $member_model->addMember($data);
    }

    /**
     * "格式化"  购物车数据
     * @param $cart
     * @return array
     */
    public function tranSkuData($cart)
    {
        $order_create = new OrderCreateModel();
        if (empty($cart)) {
            return $order_create->error([], "购物车中还没有商品！");
        }
        $cart = json_decode($cart, true);
        $sku_ids = [];
        $nums = [];
        foreach ($cart as $k => $v) {
            $sku_ids[] = $v["sku_id"];
            $nums[$v["sku_id"]] = $v["num"];
        }
        return $order_create->success(["sku_ids" => $sku_ids, "nums" => $nums]);
    }

    /**
     * 生成手机号
     * @return string
     */
    public function generateMobileNumber()
    {
        // 中国大陆手机号合法号段（常见运营商号段）
        $prefixes = [
            '130',
            '131',
            '132',
            '133',
            '134',
            '135',
            '136',
            '137',
            '138',
            '139',
            '145',
            '147',
            '150',
            '151',
            '152',
            '153',
            '155',
            '156',
            '157',
            '158',
            '159',
            '165',
            '166',
            '170',
            '171',
            '172',
            '173',
            '175',
            '176',
            '177',
            '178',
            '180',
            '181',
            '182',
            '183',
            '184',
            '185',
            '186',
            '187',
            '188',
            '189',
            '191',
            '198',
            '199'
        ];

        // 随机选择前缀
        $prefix = $prefixes[array_rand($prefixes)];

        // 生成后8位随机数字
        $suffix = '';
        for ($i = 0; $i < 8; $i++) {
            $suffix .= mt_rand(0, 9);
        }

        return $prefix . $suffix;
    }
}
