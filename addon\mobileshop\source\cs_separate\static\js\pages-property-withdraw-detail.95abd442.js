(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-property-withdraw-detail"],{"2d6a":function(i,t,a){"use strict";var e=a("85ae"),s=a.n(e);s.a},"7ef9":function(i,t,a){"use strict";a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return e}));var e={loadingCover:a("59c1").default},s=function(){var i=this,t=i.$createElement,a=i._self._c||t;return a("v-uni-view",{staticClass:"withdrawal iphone-safe-area"},[a("v-uni-view",{staticClass:"menu_title"},[a("v-uni-text",{staticClass:"line color-base-bg margin-right"}),i._v("提现信息")],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("联系人")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.realname))])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("联系电话")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.mobile))])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("账户类型")]),a("v-uni-view",{staticClass:"dd"},[1==i.base_info.bank_type?[i._v("银行")]:3==i.base_info.bank_type?[i._v("微信")]:[i._v("支付宝")]],2)],1),1==i.base_info.bank_type?[a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("账户名称")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.settlement_bank_name))])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("提现账号")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.settlement_bank_account_number))])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("开户名")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.settlement_bank_account_name))])],1)]:i._e(),3==i.base_info.bank_type?[a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("微信昵称")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.settlement_bank_address))])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("微信号")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.settlement_bank_name))])],1)]:[a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("支付宝账号")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.account_number))])],1)],a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("申请提现金额")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.apply_money)+"元")])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("提现手续费")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.service_money)+"元")])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("提现到账金额")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.money)+"元")])],1),a("v-uni-view",{staticClass:"dl"},[a("v-uni-view",{staticClass:"dt"},[i._v("状态")]),a("v-uni-view",{staticClass:"dd"},[0==i.base_info.status?[i._v("待审核")]:1==i.base_info.status?[i._v("待转账")]:2==i.base_info.status?[i._v("已转账")]:-1==i.base_info.status?[i._v("已拒绝")]:i._e()],2)],1),a("v-uni-view",{staticClass:"dl",staticStyle:{border:"none"}},[a("v-uni-view",{staticClass:"dt"},[i._v("申请时间")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.$util.timeStampTurnTime(i.base_info.apply_time)))])],1),a("v-uni-view",{staticClass:"menu_title",staticStyle:{border:"none"}},[a("v-uni-text",{staticClass:"line color-base-bg margin-right"}),i._v("转账信息")],1),a("v-uni-view",{staticClass:"dl",staticStyle:{"align-items":"center"}},[a("v-uni-view",{staticClass:"dt"},[i._v("转账凭证")]),a("v-uni-view",{staticClass:"dd"},[a("v-uni-image",{staticClass:"img",attrs:{src:i.$util.img(i.base_info.certificate)},on:{click:function(t){arguments[0]=t=i.$handleEvent(t),i.previewMedia()}}})],1)],1),a("v-uni-view",{staticClass:"dl",staticStyle:{border:"none"}},[a("v-uni-view",{staticClass:"dt"},[i._v("转账凭证说明")]),a("v-uni-view",{staticClass:"dd"},[i._v(i._s(i.base_info.paying_money_certificate_explain?i.base_info.paying_money_certificate_explain:""))])],1),a("loading-cover",{ref:"loadingCover"})],2)},n=[]},"85ae":function(i,t,a){var e=a("de71");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[i.i,e,""]]),e.locals&&(i.exports=e.locals);var s=a("967d").default;s("54fc45f2",e,!0,{sourceMap:!1,shadowMode:!1})},c117:function(i,t,a){"use strict";a.r(t);var e=a("e77a"),s=a.n(e);for(var n in e)["default"].indexOf(n)<0&&function(i){a.d(t,i,(function(){return e[i]}))}(n);t["default"]=s.a},ce46:function(i,t,a){"use strict";a.r(t);var e=a("7ef9"),s=a("c117");for(var n in s)["default"].indexOf(n)<0&&function(i){a.d(t,i,(function(){return s[i]}))}(n);a("2d6a");var d=a("828b"),v=Object(d["a"])(s["default"],e["b"],e["c"],!1,null,"3bb13856",null,!1,e["a"],void 0);t["default"]=v.exports},de71:function(i,t,a){var e=a("c86c");t=e(!1),t.push([i.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-3bb13856]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-3bb13856]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-3bb13856]{position:fixed;left:0;right:0;z-index:998}.withdrawal[data-v-3bb13856]{padding:%?20?% 0;border-radius:%?10?%;overflow:hidden;margin:0 %?30?%}.withdrawal .dl[data-v-3bb13856]{display:flex;justify-content:space-between;padding:%?30?%;border-bottom:1px solid #eee;background-color:#fff}.withdrawal .dl[data-v-3bb13856]:last-child{border-bottom:0!important}.withdrawal .dl .dt[data-v-3bb13856]{min-width:%?200?%}.withdrawal .dl .dd[data-v-3bb13856]{flex:1;text-align:right;word-break:break-all}.withdrawal .dl .dd .img[data-v-3bb13856]{height:%?80?%;width:%?80?%}.menu_title[data-v-3bb13856]{font-size:%?32?%;font-weight:700;margin-bottom:%?10?%}.menu_title .line[data-v-3bb13856]{display:inline-block;height:%?28?%;width:%?4?%;border-radius:%?4?%}',""]),i.exports=t},e77a:function(i,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={data:function(){return{id:0,base_info:{}}},onLoad:function(i){this.$util.checkToken("/pages/property/withdraw/detail?id="+this.id)&&(i.id?(this.id=i.id,this.getBaseInfo()):this.$util.goBack("/pages/property/withdraw/list"))},onShow:function(){},methods:{getBaseInfo:function(){var i=this;this.$api.sendRequest({url:"/shopapi/shopwithdraw/detail",data:{id:this.id},success:function(t){t.code>=0?i.base_info=t.data:i.$util.showToast({title:t.message}),i.$refs.loadingCover&&i.$refs.loadingCover.hide()}})},previewMedia:function(){var i=[this.$util.img(this.base_info.certificate)];uni.previewImage({current:0,urls:i})}}};t.default=e}}]);