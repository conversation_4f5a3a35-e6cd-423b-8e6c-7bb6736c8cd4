<style>
    .layui-table-view td:last-child>div{overflow: inherit;}
    .operation-wrap{position: relative;}
    .layui-table-box{overflow: inherit;}
    .layui-table-body{overflow: inherit;}
    .popup-qrcode-wrap{text-align: center;background: #fff;border-radius: 2px;box-shadow: 0 2px 8px 0 rgba(200,201,204,.5);padding: 10px;position: absolute;z-index: 1;top: -70px;left: -190px;display: none;width: 170px;height: 230px;}
    .popup-qrcode-wrap:before, .popup-qrcode-wrap:after {left: 100%;top: 50%;border: solid transparent;content: " ";height: 0;width: 0;position: absolute;pointer-events: none;}
    .popup-qrcode-wrap:before {border-color: transparent;border-left-color: #e5e5e5;border-width: 8px;margin-top: -29px;}
    .popup-qrcode-wrap:after {border-color: transparent;border-left-color: #ffffff;border-width: 7px;margin-top: -31px;}
    .popup-qrcode-wrap img{width: 150px;height: 150px;max-width: initial;}
    .popup-qrcode-wrap p{font-size: 12px;margin: 5px 0;line-height: 1.8!important;}
    .popup-qrcode-wrap a{font-size: 12px;}
    .popup-qrcode-wrap input{opacity: 0;position: absolute;}
    .popup-qrcode-wrap .popup-qrcode-loadimg {width: 16px!important; height: 16px!important; margin-top: 107px;}
    .layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加生日有礼活动</button>
</div>

<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">活动名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="search_text" placeholder="请输入活动名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="form-row">
                <button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab table-tab" lay-filter="bargain_tab">
    <ul class="layui-tab-title">
        <li class="layui-this" data-status="">全部</li>
        <li data-status="1">进行中</li>
        <li data-status="-1">已结束</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="activity_list" lay-filter="activity_list"></table>
    </div>
</div>

<!-- 状态 -->
<script type="text/html" id="status">
    {{#  if(d.status == 0){  }}
    未开始
    {{#  }else if(d.status == 1){  }}
    进行中
    {{#  }else if(d.status == -1){  }}
    已结束
    {{#  }  }}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="operation-wrap" data-game-id="{{d.id}}">
        <div class="popup-qrcode-wrap"><img class="popup-qrcode-loadimg" src="__STATIC__/loading/loading.gif" /></div>
        <div class="table-btn">
            <a class="layui-btn" lay-event="detail">详情</a>
            {{# if(d.status == 1){ }}
            <a class="layui-btn" lay-event="edit">编辑</a>
            <a class="layui-btn" lay-event="close">关闭</a>
            {{# }else if(d.status == 0){ }}
            <a class="layui-btn" lay-event="edit">编辑</a>
            {{# }else if(d.status == -1){ }}
            <a class="layui-btn" lay-event="del">删除</a>
            {{# } }}
            <a class="layui-btn" lay-event="record">领取记录</a>
        </div>
    </div>
</script>
<script id="time" type="text/html">
    <div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
    <div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<script>
    layui.use(['form', 'element'], function() {
        var table,
            form = layui.form,
            element = layui.element,
            repeat_flag = false; //防重复标识
        form.render();

        element.on('tab(bargain_tab)', function () {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    'status': this.getAttribute('data-status')
                }
            });
        });

        table = new Table({
            elem: '#activity_list',
            url: ns.url("birthdaygift://shop/birthdaygift/lists"),
            cols: [
                [{
                    field:'activity_name',
                    title: '活动名称',
                    unresize: 'false',
                    width:'20%'
                },  {
                    title: '发放时间',
                    width:'20%',
                    unresize: 'false',
                    templet: function(data){
                        if (data.activity_time_type == 1){
                            return '生日当天'
                        }else if(data.activity_time_type == 2){
                            return '生日当周(自然周)'
                        }else if(data.activity_time_type == 3){
                            return '生日当月(自然月)'
                        }
                    }
                }, {
                    title: '状态',
                    width:'20%',
                    unresize: 'false',
                    templet: '#status'
                },{
                    title: '活动时间',
                    unresize: 'false',
                    width:'20%',
                    templet: '#time'
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
					align:'right'
                }]
            ]

        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'detail': //详情
                    location.hash = ns.hash("birthdaygift://shop/birthdaygift/detail", {"id": data.id});
                    break;
                case 'edit': //编辑
                    location.hash = ns.hash("birthdaygift://shop/birthdaygift/edit", {"id": data.id});
                    break;
                case 'del': //删除
                    deleteActivity(data.id);
                    break;
                case 'close': // 结束
                    closeActivity(data.id);
                    break;
                case 'record'://领取记录
                    location.hash = ns.hash("birthdaygift://shop/record/lists", {"id": data.id});
                    break;
            }
        });

        /**
         * 删除
         */
        function deleteActivity(id) {
            layer.confirm('确定要删除该活动吗?', function(index) {
                if (repeat_flag) return;
                repeat_flag = true;
				layer.close(index);

                $.ajax({
                    url: ns.url("birthdaygift://shop/birthdaygift/delete"),
                    data: {
                        activity_id: id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload({
                                page: {
                                    curr: 1
                                },
                            });
                        }
                    }
                });
            }, function() {
                layer.close();
                repeat_flag = false;
            });
        }

        // 结束
        function closeActivity(id) {
            layer.confirm('确定要关闭该活动吗?', function(index) {
                if (repeat_flag) return;
                repeat_flag = true;
				layer.close(index);

                $.ajax({
                    url: ns.url("birthdaygift://shop/birthdaygift/finish"),
                    data: {
                        activity_id: id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function() {
                layer.close();
                repeat_flag = false;
            });
        }

    });

    function add() {
        location.hash = ns.hash("birthdaygift://shop/birthdaygift/add");
    }
</script>
