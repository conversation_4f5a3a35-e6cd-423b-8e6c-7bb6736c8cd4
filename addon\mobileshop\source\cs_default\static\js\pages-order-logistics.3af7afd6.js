(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-logistics"],{"1e36":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("bf0f"),a("2797"),a("dc69"),a("d4b5");var i=a("6638"),o={data:function(){return{orderId:"",orderStatus:0,packageList:[],currIndex:0,status:0}},onLoad:function(t){t.order_id&&(this.orderId=t.order_id)},onShow:function(){this.$util.checkToken("/pages/order/logistics?order_id="+this.orderId)&&this.getPackageInfo()},methods:{ontabtap:function(t){this.currIndex=t},getPackageInfo:function(){var t=this;(0,i.getOrderPackageList)(this.orderId).then((function(e){if(e.code>=0){var a=e.data;t.orderStatus=a.order_status,t.packageList=a.package,t.packageList.forEach((function(e){e.trace.list&&(e.trace.list=e.trace.list.reverse()),e.status=t.status++})),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}else t.$util.showToast({title:"未获取到订单信息！"}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)}))},imgError:function(t,e){this.packageList[t].goods_list[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},editLogistics:function(t){uni.setStorageSync("editLogistics",JSON.stringify(t)),this.$util.redirectTo("/pages/order/edit_logistics")}}};e.default=o},4646:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-5349c0c6]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-5349c0c6]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-5349c0c6]{position:fixed;left:0;right:0;z-index:998}.swiper-item[data-v-5349c0c6]{padding-top:%?94?%;height:100%;padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.swiper-item .container[data-v-5349c0c6]{height:calc(100vh - %?280?%);overflow-y:scroll;box-sizing:border-box;display:flex;flex-direction:column;align-items:center;padding-bottom:%?30?%}.swiper-item .container.safearea[data-v-5349c0c6]{padding:%?68?%}.order-nav[data-v-5349c0c6]{width:100vw;flex-direction:row;white-space:nowrap;background:#fff;display:flex;position:fixed;left:0;z-index:998}.order-nav .uni-tab-item[data-v-5349c0c6]{display:inline-block;padding:%?30?% %?24?% 0}.order-nav .uni-tab-item-title[data-v-5349c0c6]{font-size:%?32?%;display:block;line-height:1;padding:0 %?10?% %?30?%;flex-wrap:nowrap;white-space:nowrap;text-align:center}.order-nav .active[data-v-5349c0c6]{display:block;border-bottom:1px solid #fff;padding:0 %?10?% %?30?%}.order-nav[data-v-5349c0c6] ::-webkit-scrollbar{width:0;height:0;color:transparent}.goods-wrap[data-v-5349c0c6]{padding:%?30?%;border-radius:%?10?%;background:#fff;position:relative;width:calc(100% - %?60?%);box-sizing:border-box;margin-top:%?20?%;padding:%?30?%}.goods-wrap .goods[data-v-5349c0c6]{display:flex;position:relative;margin-bottom:%?20?%}.goods-wrap .goods[data-v-5349c0c6]:last-of-type{margin-bottom:0}.goods-wrap .goods .goods-img[data-v-5349c0c6]{width:%?160?%;height:%?160?%;margin-right:%?20?%}.goods-wrap .goods .goods-img uni-image[data-v-5349c0c6]{width:100%;height:100%}.goods-wrap .goods .goods-info[data-v-5349c0c6]{flex:1;display:flex;flex-direction:column;justify-content:space-between;position:relative;max-width:calc(100% - %?140?%)}.goods-wrap .goods .goods-info .goods-name[data-v-5349c0c6]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;line-height:1.5;font-size:%?28?%;font-weight:500}.goods-wrap .goods .goods-info .goods-sub-section[data-v-5349c0c6]{width:100%;line-height:1.3;display:flex}.goods-wrap .goods .goods-info .goods-sub-section .goods-price[data-v-5349c0c6]{font-weight:700;font-size:%?20?%}.goods-wrap .goods .goods-info .goods-sub-section .unit[data-v-5349c0c6]{font-weight:400;font-size:%?24?%;margin-right:%?2?%}.goods-wrap .goods .goods-info .goods-sub-section uni-view[data-v-5349c0c6]{flex:1;line-height:1.3}.goods-wrap .goods .goods-info .goods-sub-section uni-view[data-v-5349c0c6]:last-of-type{text-align:left}.goods-wrap .goods .goods-info .goods-sub-section uni-view:last-of-type .iconfont[data-v-5349c0c6]{line-height:1;font-size:%?24?%}.express-company-wrap[data-v-5349c0c6]{padding:%?30?%;border-radius:%?10?%;background:#fff;position:relative;width:calc(100% - %?60?%);box-sizing:border-box;margin-top:%?20?%}.express-company-wrap .company-logo[data-v-5349c0c6]{width:%?160?%;height:%?160?%;margin-right:%?20?%;float:left}.express-company-wrap .company-logo uni-image[data-v-5349c0c6]{width:100%;height:100%}.express-company-wrap .info[data-v-5349c0c6]{flex:1}.express-company-wrap .info .company[data-v-5349c0c6]{margin-top:%?10?%}.express-company-wrap .info .delivery-no[data-v-5349c0c6]{margin-top:%?10?%}.express-company-wrap .info .copy[data-v-5349c0c6]{font-size:%?28?%;margin-left:%?10?%;float:right}.track-wrap[data-v-5349c0c6]{padding:%?30?%;border-radius:%?10?%;background:#fff;position:relative;width:calc(100% - %?60?%);box-sizing:border-box;margin-top:%?20?%}.track-wrap .track-item[data-v-5349c0c6]{position:relative;flex-wrap:wrap;overflow:visible;display:flex}.track-wrap .track-item[data-v-5349c0c6]:after{content:"";position:absolute;z-index:1;pointer-events:none;background-color:#f8f8f8;width:1px;height:150%;top:%?56?%;left:%?20?%;bottom:%?-40?%}.track-wrap .track-item .dot[data-v-5349c0c6]{margin:%?34?% %?20?% 0 %?10?%;width:%?20?%;height:%?20?%;border-radius:%?10?%;background-color:#ccc;z-index:9}.track-wrap .track-item .msg[data-v-5349c0c6]{padding:%?20?% 0;flex:1}.track-wrap .track-item .msg .text[data-v-5349c0c6]{line-height:1.5;font-size:%?28?%}.track-wrap .track-item .msg .time[data-v-5349c0c6]{color:#909399;font-size:%?20?%;line-height:1.3;margin-top:%?10?%}.track-wrap .track-item[data-v-5349c0c6]:last-of-type:after{content:unset}.footer-wrap[data-v-5349c0c6]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}[data-v-5349c0c6] .uni-scroll-view ::-webkit-scrollbar{\r\n  /* 隐藏滚动条，但依旧具备可以滚动的功能 */display:none;width:0;height:0;color:transparent;background:transparent}[data-v-5349c0c6] ::-webkit-scrollbar{display:none;width:0;height:0;color:transparent;background:transparent}',""]),t.exports=e},6638:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.adjustOrderPrice=function(t){return o.default.post("/shopapi/order/adjustPrice",{data:t})},e.closeOrder=function(t){return o.default.post("/shopapi/order/close",{data:{order_id:t}})},e.deliveryOrder=function(t){return o.default.post("/shopapi/order/delivery",{data:t})},e.editOrderDelivery=function(t){return o.default.post("/shopapi/order/editOrderDelivery",{data:t})},e.editOrderInvoicelist=function(t){return o.default.post("/shopapi/order/invoiceEdit",{data:t})},e.getOrderCondition=function(){return o.default.get("/shopapi/order/condition")},e.getOrderDetailById=function(t){return o.default.post("/shopapi/order/getOrderDetail",{data:{order_id:t}})},e.getOrderDetailInfoById=function(t){return o.default.post("/shopapi/order/detail",{data:{order_id:t}})},e.getOrderGoodsList=function(t){return o.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:t}})},e.getOrderInfoById=function(t){return o.default.post("/shopapi/order/getOrderInfo",{data:{order_id:t}})},e.getOrderInvoicelist=function(t){return o.default.post("/shopapi/order/invoicelist",{data:t})},e.getOrderList=function(t){return o.default.post("/shopapi/order/lists",{data:t})},e.getOrderLog=function(t){return o.default.post("/shopapi/order/log",{data:{order_id:t}})},e.getOrderPackageList=function(t){return o.default.post("/shopapi/order/package",{data:{order_id:t}})},e.ordErtakeDelivery=function(t){return o.default.post("/shopapi/order/takeDelivery",{data:{order_id:t}})},e.orderExtendTakeDelivery=function(t){return o.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:t}})},e.orderLocalorderDelivery=function(t){return o.default.post("/shopapi/localorder/delivery",{data:t})},e.orderOfflinePay=function(t){return o.default.post("/shopapi/order/offlinePay",{data:{order_id:t}})},e.orderVirtualDelivery=function(t){return o.default.post("/shopapi/virtualorder/delivery",{data:{order_id:t}})},e.storeOrderTakeDelivery=function(t){return o.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:t}})};var o=i(a("9027"))},"896b":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={loadingCover:a("59c1").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-scroll-view",{staticClass:"order-nav",attrs:{"scroll-x":!0,"show-scrollbar":!1}},t._l(t.packageList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-tab-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.ontabtap(i)}}},[a("v-uni-text",{staticClass:"uni-tab-item-title",class:i==t.currIndex?"active color-base-border  color-base-text":""},[t._v(t._s(e.package_name))])],1)})),1),t._l(t.packageList,(function(e,i){return a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:i==t.currIndex,expression:"packageIndex == currIndex"}],key:i,staticClass:"swiper-item"},[a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"goods-wrap"},[a("v-uni-view",{staticClass:"body"},t._l(e.goods_list,(function(e,o){return a("v-uni-view",{key:o,staticClass:"goods"},[a("v-uni-view",{staticClass:"goods-img"},[a("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"aspectFill"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(i,o)}}})],1),a("v-uni-view",{staticClass:"goods-info"},[a("v-uni-view",{staticClass:"goods-name"},[t._v(t._s(e.sku_name))]),a("v-uni-view",{staticClass:"goods-sub-section"},[a("v-uni-text",{staticClass:"iconfont iconclose"}),a("v-uni-text",[t._v(t._s(e.num))])],1)],1)],1)})),1)],1),a("v-uni-view",{staticClass:"express-company-wrap"},[a("v-uni-view",{staticClass:"company-logo"},[a("v-uni-image",{attrs:{src:t.$util.img(e.express_company_image)}})],1),a("v-uni-view",{staticClass:"info"},[a("v-uni-view",[t._v("发货时间： "+t._s(t.$util.timeStampTurnTime(e.delivery_time)))]),a("v-uni-view",{staticClass:"company"},[t._v("承运公司： "+t._s(e.express_company_name))]),a("v-uni-view",{staticClass:"delivery-no"},[a("v-uni-text",[t._v("运单号："+t._s(e.delivery_no))]),a("v-uni-text",{staticClass:"copy color-base-text",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$util.copy(e.delivery_no)}}},[t._v("复制")])],1)],1)],1),a("v-uni-view",{staticClass:"track-wrap"},[e.trace.success&&0!=e.trace.list.length?t._l(e.trace.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"track-item",class:0==i?"active":"",attrs:{"data-theme":t.themeStyle}},[a("v-uni-view",{staticClass:"dot",class:0==i?"color-base-bg":""}),a("v-uni-view",{staticClass:"msg"},[a("v-uni-view",{staticClass:"text",class:0==i?"color-base-text":""},[t._v(t._s(e.remark))]),a("v-uni-view",{staticClass:"time",class:0==i?"color-base-text":""},[t._v(t._s(e.datetime))])],1)],1)})):(e.trace.success&&e.trace.list.length,[a("v-uni-view",{staticClass:"fail-wrap font-size-base"},[t._v(t._s(e.trace.reason))])])],2),1==t.orderStatus||3==t.orderStatus?a("v-uni-view",{staticClass:"footer-wrap"},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.editLogistics(e)}}},[t._v("修改物流")])],1):t._e()],1)],1)})),a("loading-cover",{ref:"loadingCover"})],2)},r=[]},"9a99":function(t,e,a){var i=a("4646");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("10c2173f",i,!0,{sourceMap:!1,shadowMode:!1})},a687:function(t,e,a){"use strict";a.r(e);var i=a("896b"),o=a("a748");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("e031");var n=a("828b"),s=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"5349c0c6",null,!1,i["a"],void 0);e["default"]=s.exports},a748:function(t,e,a){"use strict";a.r(e);var i=a("1e36"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},e031:function(t,e,a){"use strict";var i=a("9a99"),o=a.n(i);o.a}}]);