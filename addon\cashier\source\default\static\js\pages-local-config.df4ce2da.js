(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-local-config"],{"1f2e":function(t,e,r){var n=r("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3a183249]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3a183249],\r\nuni-view[data-v-3a183249]{font-size:.14rem}body[data-v-3a183249]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3a183249]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3a183249]::-webkit-scrollbar-button{display:none}body[data-v-3a183249]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3a183249]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3a183249]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3a183249]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3a183249]{color:var(--primary-color)!important}.collect-money-config[data-v-3a183249]{position:relative}.collect-money-config .common-btn-wrap[data-v-3a183249]{position:absolute;left:0;bottom:0;right:0;padding:.24rem .2rem}.collect-money-config .common-btn-wrap .screen-btn[data-v-3a183249]{margin:0}.collect-money-config .common-wrap.fixd[data-v-3a183249]{padding:%?30?%;height:calc(100vh - .4rem);overflow-y:auto;box-sizing:border-box}.collect-money-config .form-input[data-v-3a183249]{font-size:.16rem}.collect-money-config .form-input-inline.btn[data-v-3a183249]{height:.37rem;line-height:.35rem;box-sizing:border-box;border:.01rem solid #e6e6e6;text-align:center;cursor:pointer}.collect-money-config .common-title[data-v-3a183249]{font-size:.18rem;margin-bottom:.2rem}.collect-money-config .common-form .common-form-item .form-label[data-v-3a183249]{width:1.5rem}.collect-money-config .common-form .common-form-item .form-word-aux-line[data-v-3a183249]{margin-left:1.5rem}',""]),t.exports=e},2718:function(t,e,r){"use strict";var n=r("faed"),i=r.n(n);i.a},"2e5f":function(t,e,r){"use strict";r("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("bf0f"),r("2797"),r("c9b5"),r("ab80");var n=r("bf41"),i={data:function(){return{isRepeat:!1,printerList:[],printerSelectType:"all",printerSelectIds:[],printerTips:"如果一个门店有多个收银机，请为每个收银机设备选择要调用的打印机硬件"}},onLoad:function(){this.initData(),this.getPrinterList()},onShow:function(){},methods:{initData:function(){var t=this.$util.getLocalConfig();this.printerSelectType=t.printerSelectType,this.printerSelectIds=t.printerSelectIds},getPrinterList:function(){var t=this;(0,n.getPrinterList)({page:1,page_size:0}).then((function(e){e.code>=0&&(t.printerList=e.data.list,t.printerList.forEach((function(t,e){t.printer_id=t.printer_id.toString()})))}))},printerSelectTypeChange:function(t){this.printerSelectType=t.detail.value},printerSelectIdsChange:function(t){this.printerSelectIds=t.detail.value},saveFn:function(){this.$util.setLocalConfig({printerSelectType:this.printerSelectType,printerSelectIds:this.printerSelectIds}),this.$util.showToast({title:"设置成功"})}}};e.default=i},"52fd":function(t,e,r){"use strict";r.r(e);var n=r("2e5f"),i=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(a);e["default"]=i.a},bf41:function(t,e,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addPrinter=function(t){return i.default.post("/printer/storeapi/printer/add",{data:t})},e.deletePrinter=function(t){return i.default.post("/printer/storeapi/printer/deleteprinter",{data:{printer_id:t}})},e.editPrinter=function(t){return i.default.post("/printer/storeapi/printer/edit",{data:t})},e.getOrderType=function(){return i.default.post("/printer/storeapi/printer/getordertype")},e.getPrinterInfo=function(t){return i.default.post("/printer/storeapi/printer/info",{data:{printer_id:t}})},e.getPrinterList=function(t){return i.default.post("/printer/storeapi/printer/lists",{data:t})},e.getTemplate=function(){return i.default.post("/printer/storeapi/printer/template")},e.printTicket=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=a.default.getLocalConfig();return t.printer_ids="all"==e.printerSelectType?"all":e.printerSelectIds.toString(),i.default.post("/cashier/storeapi/cashier/printticket",{data:t})},r("c9b5"),r("bf0f"),r("ab80");var i=n(r("a3b5")),a=n(r("3885"))},dd48:function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("base-page",[r("v-uni-view",{staticClass:"collect-money-config"},[r("v-uni-view",{staticClass:"common-wrap common-form fixd common-scrollbar"},[r("v-uni-view",{staticClass:"common-title"},[t._v("本机设置")]),r("v-uni-view",{staticClass:"common-form-item"},[r("v-uni-label",{staticClass:"form-label"},[t._v("打印机选择")]),r("v-uni-view",{staticClass:"form-inline"},[r("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.printerSelectTypeChange.apply(void 0,arguments)}}},[r("v-uni-label",{staticClass:"radio form-radio-item"},[r("v-uni-radio",{attrs:{value:"all",checked:"all"==t.printerSelectType}}),t._v("全部")],1),r("v-uni-label",{staticClass:"radio form-radio-item"},[r("v-uni-radio",{attrs:{value:"part",checked:"part"==t.printerSelectType}}),t._v("部分")],1)],1)],1),"all"==t.printerSelectType?r("v-uni-text",{staticClass:"form-word-aux-line"},[t._v(t._s(t.printerTips))]):t._e()],1),"part"==t.printerSelectType?r("v-uni-view",{staticClass:"common-form-item"},[r("v-uni-label",{staticClass:"form-label"}),r("v-uni-view",{staticClass:"form-inline"},[r("v-uni-checkbox-group",{staticClass:"form-checkbox-group",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.printerSelectIdsChange.apply(void 0,arguments)}}},t._l(t.printerList,(function(e,n){return r("v-uni-label",{staticClass:"form-checkbox-item"},[r("v-uni-checkbox",{attrs:{value:e.printer_id,checked:t.printerSelectIds.indexOf(e.printer_id)>-1}}),t._v(t._s(e.printer_name))],1)})),1)],1),r("v-uni-text",{staticClass:"form-word-aux-line"},[t._v(t._s(t.printerTips))])],1):t._e(),r("v-uni-view",{staticClass:"common-btn-wrap"},[r("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveFn.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)],1)],1)},i=[]},e620:function(t,e,r){"use strict";r.r(e);var n=r("dd48"),i=r("52fd");for(var a in i)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return i[t]}))}(a);r("2718");var o=r("828b"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"3a183249",null,!1,n["a"],void 0);e["default"]=c.exports},faed:function(t,e,r){var n=r("1f2e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=r("967d").default;i("a5e8b9ae",n,!0,{sourceMap:!1,shadowMode:!1})}}]);