(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-statistics-store"],{"0fad":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966"),i("3efd");var a={data:function(){return{picker:[{date_type:0,date_text:"今日实时"},{date_type:-1,date_text:"昨日"},{date_type:1,date_text:"近7天"},{date_type:2,date_text:"近30天"}],pickerCurr:0,statTotal:{},storeList:[{store_name:"全部门店",store_id:0}],storeCurr:0}},onShow:function(){this.$util.checkToken("/pages/statistics/store")&&this.pickerChange({detail:{value:this.pickerCurr}})},onLoad:function(){this.getStoreList()},methods:{getStatTotal:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.$api.sendRequest({url:"/store/shopapi/stat/getstattotal",data:e,success:function(e){e.code>=0&&(t.statTotal=e.data),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},pickerChange:function(t){this.pickerCurr=t.detail.value;var e={store_id:this.storeList[this.storeCurr].store_id};switch(this.picker[this.pickerCurr].date_type){case-1:e.start_time=this.getTime(-1).startTime,e.end_time=this.getTime(-1).endTime;break;case 1:e.start_time=this.getTime(-7).startTime,e.end_time=this.getTime(0).endTime;break;case 2:e.start_time=this.getTime(-30).startTime,e.end_time=this.getTime(0).endTime;break}this.getStatTotal(e)},storeChange:function(t){this.storeCurr=t.detail.value,this.pickerChange({detail:{value:this.pickerCurr}})},getTime:function(t){var e=new Date(new Date((new Date).toLocaleDateString()));e.setDate(e.getDate()+t);var i=parseInt(new Date(e).getTime()/1e3),a=i+86399;return{startTime:i,endTime:a}},getStoreList:function(){var t=this;this.$api.sendRequest({url:"/shopapi/store/lists",data:{page_size:0},success:function(e){e.code>=0&&(t.storeList=e.data.list,t.storeList.unshift({store_name:"全部门店",store_id:0}))}})}}};e.default=a},"0fd2":function(t,e,i){var a=i("246a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("2ec9edd9",a,!0,{sourceMap:!1,shadowMode:!1})},"203a":function(t,e,i){"use strict";i.r(e);var a=i("a989"),r=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=r.a},"246a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-623224a6]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-623224a6]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-623224a6]{position:fixed;left:0;right:0;z-index:998}.withdrawal[data-v-623224a6]{padding:1px 0}.withdrawal .withdrawal_item[data-v-623224a6]{margin:0 %?30?%}.withdrawal .withdrawal_item .withdrawal_title[data-v-623224a6]{display:flex;align-items:center;margin-top:%?40?%}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info[data-v-623224a6]{font-size:%?32?%;font-weight:700;flex:1}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info .total[data-v-623224a6]{color:#ff6a00}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info .line[data-v-623224a6]{display:inline-block;height:%?28?%;width:%?4?%;border-radius:%?4?%}.withdrawal .withdrawal_item .withdrawal_title .select[data-v-623224a6]{border:1px solid #ccc;height:%?46?%;line-height:%?46?%;border-radius:%?46?%;width:%?160?%;padding:0 %?20?%;text-align:center;font-size:%?24?%;margin-left:%?20?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.withdrawal .withdrawal_item .withdrawal_title .select uni-text[data-v-623224a6]{vertical-align:middle}.withdrawal .withdrawal_item .withdrawal_title .total[data-v-623224a6]{font-weight:bolder;font-size:%?32?%}.withdrawal .withdrawal_item .withdrawal_content[data-v-623224a6]{background-color:#fff;border-radius:%?10?%}.withdrawal .withdrawal_item .withdrawal_content.margin-top[data-v-623224a6]{margin-top:%?30?%!important}.withdrawal .withdrawal_item .withdrawal_content .flex_two[data-v-623224a6]{display:flex;flex-wrap:wrap}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item[data-v-623224a6]{padding:%?28?% %?30?%;width:calc(50% - %?60?% - 1px);border-bottom:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item[data-v-623224a6]:nth-child(2n + 1){border-right:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item.border_none[data-v-623224a6]{border-bottom:0}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item .num[data-v-623224a6]{font-size:%?30?%;font-weight:500}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item .tip[data-v-623224a6]{color:#909399;font-size:%?24?%}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item[data-v-623224a6]{padding:%?28?% %?30?%;flex:1;min-width:calc(100% / 3);box-sizing:border-box}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item.border-left[data-v-623224a6]{border-left:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item.border-right[data-v-623224a6]{border-right:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item .num[data-v-623224a6]{font-size:%?30?%;font-weight:500}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item .tip[data-v-623224a6]{color:#909399;font-size:%?24?%}.withdrawal .withdrawal_item .withdrawal_content .flex_two .overhidden[data-v-623224a6]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.withdrawal .withdrawal_item .withdrawal_content .charts[data-v-623224a6]{padding:%?30?%}.safe-area[data-v-623224a6]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?40?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?40?%)}.ranking-wrap[data-v-623224a6]{padding:%?20?% 0}.ranking-item[data-v-623224a6]{display:flex;align-items:center}.ranking-item .goods-name[data-v-623224a6]{padding:0 %?20?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1;width:0}.ranking-item .ranking[data-v-623224a6]{width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center;font-size:%?24?%}.ranking-item .sale[data-v-623224a6]{padding:0 %?20?%;font-weight:700}',""]),t.exports=e},35802:function(t,e,i){"use strict";var a=i("0fd2"),r=i.n(a);r.a},"55c4":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={loadingCover:i("59c1").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"withdrawal safe-area"},[i("v-uni-view",{staticClass:"withdrawal_item"},[i("v-uni-view",{staticClass:"withdrawal_title"},[i("v-uni-view",{staticClass:"withdrawal_title_info"},[i("v-uni-text",{staticClass:"line color-base-bg margin-right"}),i("v-uni-text",[t._v("门店数据")])],1),i("v-uni-picker",{attrs:{value:t.storeCurr,range:t.storeList,"range-key":"store_name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.storeChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"select color-tip"},[t._v(t._s(t.storeList[t.storeCurr].store_name)),i("v-uni-text",{staticClass:"pickericoniconangledown"})],1)],1),i("v-uni-picker",{attrs:{value:t.pickerCurr,range:t.picker,"range-key":"date_text"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.pickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"select color-tip"},[t._v(t._s(t.picker[t.pickerCurr].date_text)),i("v-uni-text",{staticClass:"pickericoniconangledown"})],1)],1)],1),i("v-uni-view",{staticClass:"withdrawal_content margin-top"},[i("v-uni-view",{staticClass:"flex_two"},[i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"expected_earnings_total_money",title:"预计收入",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("预计收入")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.expected_earnings_total_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"billing_money",title:"营业收入",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("开单金额数")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.billing_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"billing_count",title:"营业支出",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("开单数量")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.billing_count))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"buycard_money",title:"营业支出",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("办卡金额数")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.buycard_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"buycard_count",title:"预计收入",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("办卡数")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.buycard_count))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"recharge_money",title:"营业收入",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员充值金额")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.recharge_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"recharge_count",title:"营业支出",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员充值数量")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.recharge_count))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"refund_money",title:"营业支出",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员退款金额")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.refund_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"refund_count",title:"营业收入",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员退款数量")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.refund_count))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"order_member_count",title:"营业支出",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("门店下单会员数")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.order_member_count))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/store_stat_detail",{field:"balance_money",title:"营业支出",curr:t.pickerCurr,store_id:t.storeList[t.storeCurr].store_id})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员余额消费金额")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.balance_money)))])],1)],1)],1)],1),i("loading-cover",{ref:"loadingCover"})],1)},s=[]},a989:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(i("b03f")),s=a(i("0fad")),n=a(i("5109")),o={components:{uCharts:r.default},mixins:[s.default,n.default]};e.default=o},dbaf:function(t,e,i){"use strict";i.r(e);var a=i("55c4"),r=i("203a");for(var s in r)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(s);i("35802");var n=i("828b"),o=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"623224a6",null,!1,a["a"],void 0);e["default"]=o.exports}}]);