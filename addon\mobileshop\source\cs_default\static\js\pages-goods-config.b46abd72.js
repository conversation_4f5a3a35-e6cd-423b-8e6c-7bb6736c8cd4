(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-config"],{"1b7e":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"goods"},[o("v-uni-view",{staticClass:"order-setuo"},[o("v-uni-view",{staticClass:"order-title"},[t._v("商品设置")]),o("v-uni-view",{staticClass:"order-list"},[o("v-uni-view",{staticClass:"list-left"},[t._v("排序方式:")]),o("v-uni-view",{staticClass:"list-right"},[o("v-uni-view",{staticClass:"uni-list-cell-db"},[o("v-uni-picker",{staticStyle:{display:"inline-block"},attrs:{value:t.index,range:t.array},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange.apply(void 0,arguments)}}},[o("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.array[t.index]))])],1)],1),o("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),o("v-uni-view",{staticClass:"order-list"},[o("v-uni-view",{staticClass:"list-left"},[t._v("默认排序值:")]),o("v-uni-view",{staticClass:"list-right"},[o("v-uni-input",{attrs:{type:"number",placeholder:"0"},model:{value:t.goodsConfig.goods_sort_config.default_value,callback:function(e){t.$set(t.goodsConfig.goods_sort_config,"default_value",e)},expression:"goodsConfig.goods_sort_config.default_value"}})],1)],1),o("v-uni-view",{staticClass:"order-list"},[o("v-uni-view",{staticClass:"list-left"},[t._v("默认搜索关键词:")]),o("v-uni-view",{staticClass:"list-right"},[o("v-uni-input",{attrs:{type:"text",placeholder:"0"},model:{value:t.goodsConfig.default_words.words,callback:function(e){t.$set(t.goodsConfig.default_words,"words",e)},expression:"goodsConfig.default_words.words"}})],1)],1),o("v-uni-view",{staticClass:"order-list"},[o("v-uni-view",{staticClass:"list-left"},[t._v("热门搜索:")])],1),o("v-uni-view",{staticClass:"more-spec",class:{"safe-area":t.isIphonex}},[t._l(t.words_array,(function(e,i){return o("v-uni-view",{key:i,staticClass:"spec-item"},[o("v-uni-view",{staticClass:"spec-name"},[o("v-uni-text",{staticClass:"action iconfont iconjian",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteSpec(i)}}}),o("v-uni-text",{staticClass:"label"},[t._v("关键字:")]),o("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入关键字"},model:{value:t.words_array[i],callback:function(e){t.$set(t.words_array,i,e)},expression:"words_array[index]"}})],1)],1)})),o("v-uni-view",{staticClass:"color-base-text add-spec",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.Add()}}},[t._v("+添加")])],2)],1),o("v-uni-view",{staticClass:"footer-wrap",class:{"safe-area":t.isIphonex}},[o("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save()}}},[t._v("保存")])],1)],1)},a=[]},"225f":function(t,e,o){var i=o("92df");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=o("967d").default;a("44f53dd4",i,!0,{sourceMap:!1,shadowMode:!1})},"2aa1":function(t,e,o){"use strict";var i=o("225f"),a=o.n(i);a.a},7289:function(t,e,o){"use strict";o("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("aa9c"),o("dd2b"),o("d4b5");var i=o("e5a3"),a={data:function(){return{title:"picker",array:["正序排列","倒序排列"],index:0,words_array:[],default_value:"",words:"",goodsConfig:{default_words:{words:""},goods_sort_config:{default_value:"",type:""},hot_words:{words:"",words_array:[]}},isIphonex:!1,goodsSpecMax:4,goodsSpecFormat:[]}},onLoad:function(t){this.isIphonex=this.$util.uniappIsIPhoneX(),this.goodsSpecFormat=uni.getStorageSync("editGoodsSpecFormat")?JSON.parse(uni.getStorageSync("editGoodsSpecFormat")):[]},onShow:function(){this.goodsSet()},methods:{bindPickerChange:function(t){this.index=t.target.value},Add:function(){this.words_array.push("")},deleteSpec:function(t){var e=this;uni.showModal({title:"操作提示",content:"确定要删除吗？",success:function(o){o.confirm&&e.words_array.splice(t,1)}})},goodsSet:function(){var t=this;(0,i.getGoodsConfig)().then((function(e){e.code>=0&&e.data&&(t.goodsConfig=e.data,t.words_array=e.data.hot_words.words_array,"asc"==e.data.goods_sort_config.type?t.index=0:t.index=1)}))},save:function(){var t=this;if(this.goodsConfig.hot_words.words_array.length<=0||""==this.goodsConfig.default_words.words)return this.$util.showToast({title:"修改失败"}),!1;(0,i.setGoodsConfig)({hot_words:this.goodsConfig.hot_words.words_array,default_words:JSON.parse(JSON.stringify(this.goodsConfig.default_words.words)),sort_type:0==this.index?"asc":"desc",sort_value:this.goodsConfig.goods_sort_config.default_value}).then((function(e){var o=e.message;0==e.code&&(t.$util.showToast({title:o}),setTimeout((function(){t.$util.redirectTo("/pages/index/all_menu")}),500))}))}}};e.default=a},"8c2b":function(t,e,o){"use strict";o.r(e);var i=o("1b7e"),a=o("b8e1");for(var s in a)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(s);o("2aa1");var n=o("828b"),r=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,"198bbe56",null,!1,i["a"],void 0);e["default"]=r.exports},"92df":function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-198bbe56]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-198bbe56]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-198bbe56]{position:fixed;left:0;right:0;z-index:998}.goods .order-setuo[data-v-198bbe56]{margin:%?20?% %?30?%;background:#fff;padding:%?15?% %?30?%;border-radius:%?10?%}.goods .order-setuo .order-list[data-v-198bbe56]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;border-bottom:1px solid #eee;padding:%?20?% 0}.goods .order-setuo .order-list .list-right[data-v-198bbe56]{display:flex;flex-direction:row;align-items:center;font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#303133}.goods .order-setuo .order-list .list-right uni-input[data-v-198bbe56]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#909399;text-align:right;margin-right:%?20?%;max-width:%?280?%}.goods .order-setuo .order-list .list-right .order-content[data-v-198bbe56]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#909399;text-align:right;margin-right:%?20?%}.goods .order-setuo .order-list .list-right uni-switch[data-v-198bbe56],\r\n.goods .order-setuo .order-list .list-right .uni-switch-wrapper[data-v-198bbe56],\r\n.goods .order-setuo .order-list .list-right .uni-switch-input[data-v-198bbe56]{width:%?80?%;height:%?42?%}.goods .order-setuo .order-list .list-right .iconfont[data-v-198bbe56]{font-size:%?30?%;color:#909399}.goods .order-setuo .order-list .list-left[data-v-198bbe56]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#303133}.goods .order-setuo .order-list[data-v-198bbe56]:last-child{border:none}.goods .order-setuo .order-title[data-v-198bbe56]{font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#303133;margin-bottom:%?10?%}.goods .footer-wrap[data-v-198bbe56]{margin-top:%?80?%;padding:0 0 %?100?%}.footer-wrap[data-v-198bbe56]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;background-color:#fff}.safe-area[data-v-198bbe56]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.more-spec[data-v-198bbe56]{margin:%?20?% 0 %?80?% 0}.more-spec.safe-area[data-v-198bbe56]{margin-bottom:%?200?%}.more-spec .spec-item[data-v-198bbe56]{background-color:#fff;border-radius:%?10?%;margin-bottom:%?20?%}.more-spec .spec-item .action[data-v-198bbe56]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.more-spec .spec-item .label[data-v-198bbe56]{vertical-align:middle;margin-right:%?30?%}.more-spec .spec-item uni-input[data-v-198bbe56]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.more-spec .spec-item .spec-name[data-v-198bbe56],\r\n.more-spec .spec-item .spec-value[data-v-198bbe56]{display:flex;align-items:center;height:%?100?%;line-height:%?100?%;padding:0 %?30?%;border-bottom:1px solid #eee}.more-spec .spec-item .spec-value[data-v-198bbe56]{margin-left:%?60?%;padding-left:0}.more-spec .spec-item .add-spec-value[data-v-198bbe56]{height:%?100?%;line-height:%?100?%;margin-left:%?60?%}.more-spec .add-spec[data-v-198bbe56]{text-align:center;background-color:#fff;height:%?100?%;line-height:%?100?%;border-radius:%?10?%}.more-spec .tip[data-v-198bbe56]{text-align:center;color:#909399;font-size:%?24?%;padding:%?20?%}uni-button[data-v-198bbe56]{color:#fff;background-color:#ff6a00;margin-top:%?20?%}',""]),t.exports=e},b8e1:function(t,e,o){"use strict";o.r(e);var i=o("7289"),a=o.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(s);e["default"]=a.a},e5a3:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getCaptchaConfig=function(){return a.default.get("/shopapi/config/captchaConfig")},e.getGoodsConfig=function(){return a.default.get("/shopapi/goods/config")},e.getOrderConfig=function(){return a.default.get("/shopapi/order/config")},e.setGoodsConfig=function(t){return a.default.post("/shopapi/goods/setconfig",{data:t})},e.setOrderConfig=function(t){return a.default.post("/shopapi/order/setconfig",{data:t})},e.setShopConfig=function(t){return a.default.post("/shopapi/shop/config",{data:t})};var a=i(o("9027"))}}]);