(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-adjust_price"],{"17c8":function(t,e,i){"use strict";var a=i("8742"),o=i.n(a);o.a},"3f5e":function(t,e,i){"use strict";i.r(e);var a=i("7eca"),o=i("decb");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("17c8");var s=i("828b"),d=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"3fdad4db",null,!1,a["a"],void 0);e["default"]=d.exports},6638:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.adjustOrderPrice=function(t){return o.default.post("/shopapi/order/adjustPrice",{data:t})},e.closeOrder=function(t){return o.default.post("/shopapi/order/close",{data:{order_id:t}})},e.deliveryOrder=function(t){return o.default.post("/shopapi/order/delivery",{data:t})},e.editOrderDelivery=function(t){return o.default.post("/shopapi/order/editOrderDelivery",{data:t})},e.editOrderInvoicelist=function(t){return o.default.post("/shopapi/order/invoiceEdit",{data:t})},e.getOrderCondition=function(){return o.default.get("/shopapi/order/condition")},e.getOrderDetailById=function(t){return o.default.post("/shopapi/order/getOrderDetail",{data:{order_id:t}})},e.getOrderDetailInfoById=function(t){return o.default.post("/shopapi/order/detail",{data:{order_id:t}})},e.getOrderGoodsList=function(t){return o.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:t}})},e.getOrderInfoById=function(t){return o.default.post("/shopapi/order/getOrderInfo",{data:{order_id:t}})},e.getOrderInvoicelist=function(t){return o.default.post("/shopapi/order/invoicelist",{data:t})},e.getOrderList=function(t){return o.default.post("/shopapi/order/lists",{data:t})},e.getOrderLog=function(t){return o.default.post("/shopapi/order/log",{data:{order_id:t}})},e.getOrderPackageList=function(t){return o.default.post("/shopapi/order/package",{data:{order_id:t}})},e.ordErtakeDelivery=function(t){return o.default.post("/shopapi/order/takeDelivery",{data:{order_id:t}})},e.orderExtendTakeDelivery=function(t){return o.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:t}})},e.orderLocalorderDelivery=function(t){return o.default.post("/shopapi/localorder/delivery",{data:t})},e.orderOfflinePay=function(t){return o.default.post("/shopapi/order/offlinePay",{data:{order_id:t}})},e.orderVirtualDelivery=function(t){return o.default.post("/shopapi/virtualorder/delivery",{data:{order_id:t}})},e.storeOrderTakeDelivery=function(t){return o.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:t}})};var o=a(i("9027"))},"7eca":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={loadingCover:i("59c1").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"container",class:{"safe-area":t.isIphoneX}},[i("v-uni-view",{staticClass:"tips"},[t._v("注意：只有订单未付款时才支持改价,改价后请联系买家刷新订单核实订单金额后再支付。")]),i("v-uni-view",{staticClass:"order-goods-list"},[i("v-uni-view",{staticClass:"form-title"},[t._v("商品信息")]),t._l(t.order.order_goods,(function(e,a){return i("v-uni-view",{key:a,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(a)}}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[t._v(t._s(e.sku_name))]),i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-view",{staticClass:"num"},[t._v("x"+t._s(e.num))]),i("v-uni-view",{staticClass:"price"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(e.price))])],1)],1),i("v-uni-view",{staticClass:"total-wrap"},[i("v-uni-text",{staticClass:"label"},[t._v("小计：")]),i("v-uni-view",{staticClass:"price color-base-text"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(e.goods_money))])],1)],1)],1)],1)})),i("v-uni-view",{staticClass:"form-title"},[t._v("订单信息")]),i("v-uni-view",{staticClass:"order-info-wrap"},[i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("商品总额")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.goods_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("优惠")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.promotion_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("优惠券")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.coupon_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("积分抵现")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.point_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("发票费用")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.invoice_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("发票邮寄")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.invoice_delivery_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"cell"},[i("v-uni-text",{staticClass:"label"},[t._v("总计")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.order.pay_money))]),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1)],1),i("v-uni-view",{staticClass:"form-title"},[t._v("调整价格")]),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"form-wrap"},[i("v-uni-text",{staticClass:"label"},[t._v("调整金额")]),i("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",placeholder:"0.00"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.adjustChange()}},model:{value:t.order.adjust_money,callback:function(e){t.$set(t.order,"adjust_money",e)},expression:"order.adjust_money"}}),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),i("v-uni-view",{staticClass:"form-wrap"},[i("v-uni-text",{staticClass:"label"},[t._v("运费")]),i("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",placeholder:"0.00"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.adjustChange()}},model:{value:t.order.delivery_money,callback:function(e){t.$set(t.order,"delivery_money",e)},expression:"order.delivery_money"}}),i("v-uni-text",{staticClass:"unit"},[t._v("元")])],1)],1)],2),i("v-uni-view",{staticClass:"tips bottom"},[i("v-uni-view",[i("v-uni-text",{staticClass:"dot color-base-text"},[t._v("·")]),i("v-uni-text",{staticClass:"color-base-text"},[t._v("实际商品金额")]),i("v-uni-text",{staticClass:"interval"},[t._v("=")]),i("v-uni-text",[t._v("商品总额")]),i("v-uni-text",{staticClass:"interval"},[t._v("-")]),i("v-uni-text",[t._v("优惠金额")]),i("v-uni-text",{staticClass:"interval"},[t._v("-")]),i("v-uni-text",[t._v("优惠券金额")]),i("v-uni-text",{staticClass:"interval"},[t._v("-")]),i("v-uni-text",[t._v("积分抵现")]),i("v-uni-text",{staticClass:"interval"},[t._v("+")]),i("v-uni-text",[t._v("调价")])],1),i("v-uni-view",[i("v-uni-text",{staticClass:"dot color-base-text"},[t._v("·")]),i("v-uni-text",{staticClass:"color-base-text"},[t._v("发票费用")]),i("v-uni-text",{staticClass:"interval"},[t._v("=")]),i("v-uni-text",[t._v("实际商品金额 * 发票比率")])],1),i("v-uni-view",[i("v-uni-text",{staticClass:"dot color-base-text"},[t._v("·")]),i("v-uni-text",[t._v("实付金额")]),i("v-uni-text",{staticClass:"interval"},[t._v("=")]),i("v-uni-text",{staticClass:"color-base-text"},[t._v("实际商品金额")]),i("v-uni-text",{staticClass:"interval"},[t._v("+")]),i("v-uni-text",{staticClass:"color-base-text"},[t._v("发票费用")]),i("v-uni-text",{staticClass:"interval"},[t._v("+")]),i("v-uni-text",[t._v("运费")]),i("v-uni-text",{staticClass:"interval"},[t._v("+")]),i("v-uni-text",[t._v("发票邮寄费用")]),i("v-uni-text",{staticClass:"interval"},[t._v("-")]),i("v-uni-text",[t._v("余额")])],1)],1),i("v-uni-view",{staticClass:"footer-wrap",class:{"safe-area":t.isIphoneX}},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save()}}},[t._v("确定")])],1),i("loading-cover",{ref:"loadingCover"})],1)},r=[]},8742:function(t,e,i){var a=i("a5a5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("3a700238",a,!0,{sourceMap:!1,shadowMode:!1})},"89e3":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e838"),i("5c47"),i("0506");var a=i("6638"),o={data:function(){return{isIphoneX:!1,orderId:0,order:{},repeatFlag:!1}},onLoad:function(t){this.isIphoneX=this.$util.uniappIsIPhoneX(),this.orderId=t.order_id||0,this.getOrderDetail()},onShow:function(){},methods:{getOrderDetail:function(){var t=this;(0,a.getOrderDetailById)(this.orderId).then((function(e){0==e.code?(t.order=e.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:e.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3))}))},adjustChange:function(){var t=this.order.balance_money,e=this.order.invoice_delivery_money,i=this.order.promotion_money,a=this.order.platform_coupon_total_money,o=this.order.coupon_money,r=this.order.goods_money,s=this.order.is_invoice,d=this.order.adjust_money,n=this.order.delivery_money,l=parseFloat(r)-parseFloat(i)-parseFloat(o)+parseFloat(d)-parseFloat(a),v=1==s?this.order.invoice_rate:0,u=Math.round(parseFloat(l)*parseFloat(v)/100*100)/100,c=parseFloat(r)-parseFloat(i)-parseFloat(o)-parseFloat(a)+parseFloat(d)+parseFloat(e)+parseFloat(u)+parseFloat(n)-parseFloat(t);c=Math.round(100*c)/100,isNaN(u)&&(u=0),isNaN(c)&&(c=0),this.order.invoice_money=u.toFixed(2),this.order.pay_money=c.toFixed(2)},save:function(){var t=this;0!=this.order.adjust_money.length?/^-?\d+\.?\d{0,2}$/.test(this.order.adjust_money)?0!=this.order.delivery_money.length?/^-?\d+\.?\d{0,2}$/.test(this.order.delivery_money)?this.repeatFlag||(this.repeatFlag=!0,(0,a.adjustOrderPrice)({order_id:this.orderId,adjust_money:this.order.adjust_money,delivery_money:this.order.delivery_money}).then((function(e){t.$util.showToast({title:e.message}),0==e.code?setTimeout((function(){uni.navigateBack({delta:1})}),1e3):t.repeatFlag=!1}))):this.$util.showToast({title:"[运费金额]格式输入错误"}):this.$util.showToast({title:"请输入运费"}):this.$util.showToast({title:"[调整金额]格式输入错误"}):this.$util.showToast({title:"请输入调整金额"})}}};e.default=o},a5a5:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-3fdad4db]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-3fdad4db]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-3fdad4db]{position:fixed;left:0;right:0;z-index:998}.tips[data-v-3fdad4db]{margin:%?20?% %?30?%}.tips[data-v-3fdad4db],\r\n.tips uni-view[data-v-3fdad4db],\r\n.tips uni-text[data-v-3fdad4db]{color:#909399;font-size:%?24?%}.tips .interval[data-v-3fdad4db]{margin:0 %?4?%}.tips .dot[data-v-3fdad4db]{font-weight:700;margin-right:%?10?%}.tips.bottom[data-v-3fdad4db]{margin-bottom:%?160?%}.order-goods-list .goods-item[data-v-3fdad4db]{background:#fff;margin-top:%?20?%;display:flex;position:relative;flex-flow:row wrap;padding:%?20?% %?30?%}.order-goods-list .goods-item .goods-img[data-v-3fdad4db]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.order-goods-list .goods-item .goods-img uni-image[data-v-3fdad4db]{width:100%;height:100%}.order-goods-list .goods-item .info-wrap[data-v-3fdad4db]{flex:1;display:flex;flex-direction:column}.order-goods-list .goods-item .info-wrap .name-wrap[data-v-3fdad4db]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-bottom:%?10?%}.order-goods-list .goods-item .info-wrap .price-wrap[data-v-3fdad4db]{display:flex;margin-top:%?16?%}.order-goods-list .goods-item .info-wrap .price-wrap .num[data-v-3fdad4db]{color:#909399;font-size:%?20?%;line-height:1}.order-goods-list .goods-item .info-wrap .price-wrap .price[data-v-3fdad4db]{display:inline-block;line-height:1;font-size:%?28?%;flex:1;text-align:right}.order-goods-list .goods-item .info-wrap .price-wrap .unit[data-v-3fdad4db]{font-size:%?20?%}.order-goods-list .goods-item .total-wrap[data-v-3fdad4db]{text-align:right}.order-goods-list .goods-item .total-wrap .label[data-v-3fdad4db]{color:#909399}.order-goods-list .goods-item .total-wrap .num[data-v-3fdad4db]{color:#909399;font-size:%?20?%;line-height:1}.order-goods-list .goods-item .total-wrap .price[data-v-3fdad4db]{display:inline-block;font-weight:700;font-size:%?32?%;text-align:right}.order-goods-list .goods-item .total-wrap .unit[data-v-3fdad4db]{font-size:%?20?%;margin:0}.order-goods-list .order-info-wrap[data-v-3fdad4db]{background:#fff;margin-top:%?20?%}.order-goods-list .order-info-wrap .cell[data-v-3fdad4db]{display:flex;align-items:center;margin:0 %?30?%;height:%?80?%;line-height:%?80?%}.order-goods-list .order-info-wrap .cell .label[data-v-3fdad4db]{vertical-align:middle;margin-right:%?30?%}.order-goods-list .order-info-wrap .cell .value[data-v-3fdad4db]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.order-goods-list .form-title[data-v-3fdad4db]{margin:%?20?% %?30?%;color:#909399}.order-goods-list .unit[data-v-3fdad4db]{margin-left:%?20?%;width:%?40?%}.order-goods-list .item-wrap[data-v-3fdad4db]{background:#fff;margin-top:%?20?%}.order-goods-list .item-wrap .form-wrap[data-v-3fdad4db]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.order-goods-list .item-wrap .form-wrap[data-v-3fdad4db]:last-child{border-bottom:none}.order-goods-list .item-wrap .form-wrap .label[data-v-3fdad4db]{vertical-align:middle;margin-right:%?30?%}.order-goods-list .item-wrap .form-wrap uni-input[data-v-3fdad4db]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.footer-wrap[data-v-3fdad4db]{position:fixed;background-color:#fff;width:100%;bottom:0;padding:%?40?% 0;z-index:10}.container[data-v-3fdad4db]{padding-bottom:%?40?%}.safe-area[data-v-3fdad4db]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?40?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?40?%)}',""]),t.exports=e},decb:function(t,e,i){"use strict";i.r(e);var a=i("89e3"),o=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a}}]);