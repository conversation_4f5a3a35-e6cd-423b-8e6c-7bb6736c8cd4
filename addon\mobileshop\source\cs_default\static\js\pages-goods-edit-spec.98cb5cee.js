(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-edit-spec"],{"065f":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[0==e.goodsSpecFormat.length?a("v-uni-view",{staticClass:"single-spec"},[e._v("单规格")]):e._e(),a("v-uni-view",{staticClass:"more-spec",class:{"safe-area":e.isIphonex}},[e._l(e.goodsSpecFormat,(function(t,i){return a("v-uni-view",{key:i,staticClass:"spec-item"},[a("v-uni-view",{staticClass:"spec-name"},[a("v-uni-text",{staticClass:"action iconfont iconjian",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.deleteSpec(i)}}}),a("v-uni-text",{staticClass:"label"},[e._v("规格项")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入规格项"},model:{value:t.spec_name,callback:function(a){e.$set(t,"spec_name",a)},expression:"item.spec_name"}})],1),e._l(t.value,(function(t,n){return a("v-uni-view",{key:n,staticClass:"spec-value"},[a("v-uni-text",{staticClass:"action iconfont iconjian",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.deleteSpecValue(i,n)}}}),a("v-uni-text",{staticClass:"label"},[e._v("规格值")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入规格值"},model:{value:t.spec_value_name,callback:function(a){e.$set(t,"spec_value_name",a)},expression:"value_item.spec_value_name"}})],1)})),t.value.length<e.goodsSpecValueMax?a("v-uni-view",{staticClass:"color-base-text add-spec-value",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addSpecValue(i)}}},[e._v("+添加规格值")]):a("v-uni-view",{staticClass:"tip"},[e._v("最多添加"+e._s(e.goodsSpecValueMax)+"个规格值")])],2)})),e.goodsSpecFormat.length<e.goodsSpecMax?a("v-uni-view",{staticClass:"color-base-text add-spec",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addSpec()}}},[e._v("+添加规格")]):a("v-uni-view",{staticClass:"tip"},[e._v("最多添加"+e._s(e.goodsSpecMax)+"个规格")])],2),a("v-uni-view",{staticClass:"footer-wrap",class:{"safe-area":e.isIphonex}},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save()}}},[e._v("保存")])],1)],1)},n=[]},"1ce9":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("aa9c"),a("dd2b"),a("0c26"),a("bf0f"),a("2797"),a("d4b5");var i={data:function(){return{isIphonex:!1,goodsSpecMax:4,goodsSpecValueMax:10,goodsSpecFormat:[]}},onLoad:function(e){this.isIphonex=this.$util.uniappIsIPhoneX(),this.goodsSpecFormat=uni.getStorageSync("editGoodsSpecFormat")?JSON.parse(uni.getStorageSync("editGoodsSpecFormat")):[]},onShow:function(){},methods:{addSpec:function(){var e=-(this.goodsSpecFormat.length-1+Math.floor((new Date).getSeconds())+Math.floor((new Date).getMilliseconds())),t={spec_id:e,spec_name:"",value:[]};this.goodsSpecFormat.push(t);var a=this.goodsSpecFormat.length-1>-1?this.goodsSpecFormat.length-1:0;this.addSpecValue(a)},addSpecValue:function(e){var t=this.goodsSpecFormat[e].value.length,a=-(Math.abs(this.goodsSpecFormat[e].spec_id)+Math.floor((new Date).getSeconds())+Math.floor((new Date).getMilliseconds()))+t;this.goodsSpecFormat[e].value.push({spec_id:this.goodsSpecFormat[e].spec_id,spec_name:this.goodsSpecFormat[e].spec_name,spec_value_id:a,spec_value_name:""})},deleteSpec:function(e){var t=this;uni.showModal({title:"操作提示",content:"确定要删除此规格项吗？",success:function(a){a.confirm&&t.goodsSpecFormat.splice(e,1)}})},deleteSpecValue:function(e,t){var a=this;uni.showModal({title:"操作提示",content:"确定要删除此规格值吗？",success:function(i){i.confirm&&a.goodsSpecFormat[e].value.splice(t,1)}})},verify:function(){for(var e=!0,t=0;t<this.goodsSpecFormat.length;t++){var a=this.goodsSpecFormat[t];if(0==a.spec_name.trim().length){this.$util.showToast({title:"请输入规格项"}),e=!1;break}for(var i=0;i<a.value.length;i++){var n=a.value[i];if(0==n.spec_value_name.trim().length){this.$util.showToast({title:"请输入规格值"}),e=!1;break}}}return e},save:function(){this.verify()&&(this.goodsSpecFormat.forEach((function(e){e.value.forEach((function(t){t.spec_name=e.spec_name,t.spec_name=e.spec_name?e.spec_name:""}))})),uni.setStorageSync("editGoodsSpecFormat",JSON.stringify(this.goodsSpecFormat)),uni.navigateBack({delta:1}))}}};t.default=i},"39e9":function(e,t,a){"use strict";var i=a("d6df"),n=a.n(i);n.a},"4cec":function(e,t,a){"use strict";a.r(t);var i=a("065f"),n=a("f915");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("39e9");var s=a("828b"),c=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,"ee95013a",null,!1,i["a"],void 0);t["default"]=c.exports},"6e25":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-ee95013a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-ee95013a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-ee95013a]{position:fixed;left:0;right:0;z-index:998}.single-spec[data-v-ee95013a]{font-size:%?32?%;text-align:center;margin-top:%?20?%;padding:%?40?%}.footer-wrap[data-v-ee95013a]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;background-color:#fff}.safe-area[data-v-ee95013a]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.more-spec[data-v-ee95013a]{margin:%?20?% %?30?% %?180?% %?30?%}.more-spec.safe-area[data-v-ee95013a]{margin-bottom:%?200?%}.more-spec .spec-item[data-v-ee95013a]{background-color:#fff;border-radius:%?10?%;margin-bottom:%?20?%}.more-spec .spec-item .action[data-v-ee95013a]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.more-spec .spec-item .label[data-v-ee95013a]{vertical-align:middle;margin-right:%?30?%}.more-spec .spec-item uni-input[data-v-ee95013a]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.more-spec .spec-item .spec-name[data-v-ee95013a],\r\n.more-spec .spec-item .spec-value[data-v-ee95013a]{display:flex;align-items:center;height:%?100?%;line-height:%?100?%;padding:0 %?30?%;border-bottom:1px solid #eee}.more-spec .spec-item .spec-value[data-v-ee95013a]{margin-left:%?60?%;padding-left:0}.more-spec .spec-item .add-spec-value[data-v-ee95013a]{height:%?100?%;line-height:%?100?%;margin-left:%?60?%}.more-spec .add-spec[data-v-ee95013a]{text-align:center;background-color:#fff;height:%?100?%;line-height:%?100?%;border-radius:%?10?%}.more-spec .tip[data-v-ee95013a]{text-align:center;color:#909399;font-size:%?24?%;padding:%?20?%}',""]),e.exports=t},d6df:function(e,t,a){var i=a("6e25");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("3a1402e2",i,!0,{sourceMap:!1,shadowMode:!1})},f915:function(e,t,a){"use strict";a.r(t);var i=a("1ce9"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a}}]);