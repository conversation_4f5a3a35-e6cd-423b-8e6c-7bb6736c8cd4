<style>
    .rights_interests{line-height: 13px;}
    .len-short{width: 70px !important;}
    .hide{display: none;}
    .show{display: block;}
    .layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 10px;}
</style>

<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加礼品卡</button>
</div>

<div class="screen layui-collapse" lay-filter="card_tab">
    <div class="layui-colla-item">
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">礼品卡名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="search_text" placeholder="请输入卡名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="form-row">
                <button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
    </div>
</div>
<div class="layui-tab table-tab" lay-filter="card_type_tab">
    <ul class="layui-tab-title">
        <li class="layui-this tab1" lay-id="all">全部</li>
        <li class="tab1" lay-id="virtual">电子卡</li>
        <li class="tab2" lay-id="real">实体卡</li>
    </ul>
    <div class="layui-tab-content poster_list">
        <!-- 列表 -->
        <table id="poster_list" lay-filter="poster_list"></table>
    </div>
</div>

<script type="text/html" id="rights_interests">
    <div class=rights_interests>
        <p style="margin-top: 13px"><span>积分：{{d.point}}</span></p><br>
        <p><span>余额：{{d.balance}}</span></p><br>
    </div>
</script>
<script type="text/html" id="selling_price">
    <div class=rights_interests>
      {{# if(d.type == 1){ }}￥{{d.selling_price}}{{# }else{ }}---{{# } }}
    </div>
</script>
<script type="text/html" id="poster_status">
    <div class='table-title'>
        {{# if(d.status == 0){ }}
        <div class='title-pic' style="text-align:left">未激活</div>
        {{# }else if(d.status == 1){ }}
        <div class='title-pic' style="text-align:left">已激活</div>
        {{# }else if(d.status == 2){ }}
        <div class='title-pic' style="text-align:left">已作废</div>
        {{# } }}
    </div>
</script>

<!-- 时间 -->
<script id="time" type="text/html">
    <div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
    <div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<script type="text/html" id="validity_type">
    <div class='table-title'>
        {{# if(d.validity_type == 'forever'){ }}
        <div class='title-pic' style="text-align:left">永久有效</div>
        {{# }else if(d.validity_type == 'date'){ }}
        <div class='title-pic' style="text-align:left">有效期至{{ns.time_to_date(d.validity_time)}}</div>
        {{# }else if(d.validity_type == 'day'){ }}
        <div class='title-pic' style="text-align:left">领取后{{d.validity_day}}天内有效</div>
        {{# } }}
    </div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="table-btn">
        <a class="layui-btn" lay-event="elect_detail">详情</a>
        <a class="layui-btn" lay-event="edit">编辑</a>
        {{# if(d.status==0){ }}
        <a class="layui-btn" lay-event="start">上架</a>
        <a class="layui-btn" lay-event="delete">删除</a>
        {{# } }}
        {{# if(d.status==1){ }}
        <a class="layui-btn" lay-event="close">下架</a>
        {{# } }}
        {{# if(d.card_type=='real'){ }}
        <a class="layui-btn" lay-event="add_batch">制卡</a>
        {{# } }}
        {{# if(d.card_type=='virtual'){ }}
        <a class="layui-btn" lay-event="order">订单</a>
        {{# } }}
    </div>
</script>

<script type="text/html" id="status">
    <div class='table-title'>
        {{# if(d.status == 0){ }}
        <div class='title-pic' style="text-align:left">已结束</div>
        {{# }else if(d.status == 1){ }}
        <div class='title-pic' style="text-align:left">进行中</div>
        {{# } }}
    </div>
</script>

<!-- 编辑排序 -->
<script type="text/html" id="editSort">
    <input name="sort" type="number" onchange="editSort({{d.giftcard_id}}, this)" value="{{d.sort}}" class="layui-input edit-sort len-short">
</script>

<script type="text/html" id="card_right_type">
    <div class='table-title'>
        {{# if(d.card_right_type == 'balance'){ }}
        <div class='title-pic' style="text-align:left">储值卡</div>
        {{# }else if(d.card_right_type == 'goods'){ }}
        <div class='title-pic' style="text-align:left">礼品卡</div>
        {{# } }}
    </div>
</script>

<script>
    var table,form,laytpl,repeat_flag,layer_label,element;
    layui.use(['form','laytpl','element'], function () {
        laytpl = layui.laytpl;
        form = layui.form;
        element = layui.element;
        repeat_flag = false; //防重复标识
        form.render();

        var cols =  {
            "all": [
                [{
                    field: 'card_name',
                    title: '礼品卡名称',
                    unresize: 'false',
                    width: '16%',
                }, {
                    title: '权益类型',
                    unresize: 'false',
                    width: '8%',
                    templet: '#card_right_type'
                }, {
                    title: '卡类型',
                    unresize: 'false',
                    width: '8%',
                    field: 'card_type_name'
                }, {
                    field: 'sale_num',
                    title: '销量/激活数',
                    unresize: 'false',
                    width: '8%',
                    templet: function(data){
                        var val = data.card_type == 'real' ? data.activate_count : data.sale_num;
                        return val;
                    }
                },{
                    field: 'use_count',
                    title: '已使用',
                    unresize: 'false',
                    width: '8%',
                }, {
                    title: '有效期',
                    unresize: 'false',
                    width: '12%',
                    templet: '#validity_type'
                }, {
                    field: 'sort',
                    unresize: 'false',
                    title: '排序',
                    templet: '#editSort',
                    sort : true,
                    width: '7%',
                }, {
                    field: 'status',
                    title: '状态',
                    unresize: 'false',
                    width: '10%',
                    templet: '#status'
                },{
                    field: 'create_time',
                    title: '创建时间',
                    unresize: 'false',
                    width: '14%',
                    templet: function (data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
					align : 'right'
                }]
            ],
            "virtual": [
                [{
                    field: 'card_name',
                    title: '礼品卡名称',
                    unresize: 'false',
                    width: '16%',
                }, {
                    title: '权益类型',
                    unresize: 'false',
                    width: '8%',
                    templet: '#card_right_type'
                }, {
                    title: '卡类型',
                    unresize: 'false',
                    width: '8%',
                    field: 'card_type_name'
                }, {
                    field: 'sale_num',
                    title: '销量',
                    unresize: 'false',
                    width: '8%',
                },{
                    field: 'use_count',
                    title: '已使用',
                    unresize: 'false',
                    width: '8%',
                }, {
                    title: '有效期',
                    unresize: 'false',
                    width: '12%',
                    templet: '#validity_type'
                }, {
                    field: 'sort',
                    unresize: 'false',
                    title: '排序',
                    templet: '#editSort',
                    sort : true,
                    width: '7%',
                }, {
                    field: 'status',
                    title: '状态',
                    unresize: 'false',
                    width: '10%',
                    templet: '#status'
                },{
                    field: 'create_time',
                    title: '创建时间',
                    unresize: 'false',
                    width: '14%',
                    templet: function (data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
					align : 'right'
                }]
            ],
            "real": [
                [{
                    field: 'card_name',
                    title: '礼品卡名称',
                    unresize: 'false',
                    width: '10%',
                }, {
                    title: '权益类型',
                    unresize: 'false',
                    width: '8%',
                    templet: '#card_right_type'
                }, {
                    title: '卡类型',
                    unresize: 'false',
                    width: '8%',
                    field: 'card_type_name'
                },
                {
                    field: 'card_count',
                    title: '制卡数',
                    unresize: 'false',
                    width: '8%',
                },{
                    field: 'activate_count',
                    title: '激活数',
                    unresize: 'false',
                    width: '8%',
                },{
                    field: 'use_count',
                    title: '已使用',
                    unresize: 'false',
                    width: '8%'
                },{
                    title: '有效期',
                    unresize: 'false',
                    width: '10%',
                    templet: '#validity_type'
                }, {
                    field: 'sort',
                    unresize: 'false',
                    title: '排序',
                    templet: '#editSort',
                    sort : true,
                    width: '7%',
                }, {
                    field: 'status',
                    title: '状态',
                    unresize: 'false',
                    width: '8%',
                    templet: '#status'
                },{
                    field: 'create_time',
                    title: '创建时间',
                    unresize: 'false',
                    width: '14%',
                    templet: function (data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
					align : 'right'
                }]
            ]
        }

        element.on('tab(card_type_tab)', function () {
            table = new Table({
                elem: '#poster_list',
                url: ns.url("giftcard://shop/giftcard/lists"),
                cols: cols[this.getAttribute('lay-id')],
                where: {
                    'card_type': this.getAttribute('lay-id')
                }
            });
        });

        table = new Table({
            elem: '#poster_list',
            url: ns.url("giftcard://shop/giftcard/lists"),
            cols: cols.all
        });

		$('body').off('click', '.tab1').on('click', '.tab1', function () {
			$('.poster_list2').removeClass('show').addClass('hide');
			$('.poster_list').removeClass('hide').addClass('show');
		});

        /**
         * 监听工具栏操作
         */
        table.tool(function (obj) {
            var data = obj.data;

            switch (obj.event) {
                case 'edit':
                    location.hash = ns.hash("giftcard://shop/giftcard/edit?giftcard_id="+data.giftcard_id);
                    break;
                case 'delete':
                    del(data.giftcard_id);
                    break;
                case 'elect_detail':
                    window.open(ns.href("giftcard://shop/giftcard/detail?giftcard_id="+data.giftcard_id));
                    break;
                case 'close': //下架
                    output(data.giftcard_id);
                    break;
                case 'start': //上架
                    onput(data.giftcard_id);
                    break;
                case 'add_batch': //添加批次
                    location.hash = ns.hash("giftcard://shop/cardimport/lists",{"giftcard_id": data.giftcard_id});
                    break;
                case 'check_card': //查看卡密
                    window.open(ns.href("giftcard://shop/card/lists",{giftcard_id: data.giftcard_id}));
                    break;
                case 'order': //查看订单
                    window.open(ns.href('giftcard://shop/order/order?giftcard_id=' + data.giftcard_id))
                    break;
            }
        });

        table.on("sort",function (obj) {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    order:obj.field,
                    sort:obj.type
                }
            });
        });

        /**
         * 下架
         */
        function output(id) {
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定下架吗?',{
                btn: ['确定','取消'] //按钮
                ,cancel: function(index, layero){
                    repeat_flag = false;
					layer.close(index);
                }
            }, function (index) {
				layer.close(index);
                $.ajax({
                    url: ns.url("giftcard://shop/giftcard/isuse"),
                    data: {
                        id: id,
                        status: 0
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function(){
                repeat_flag = false;
				layer.close(index);
            });
        }

        /**
         * 上架
         */
        function onput(id) {
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定上架吗?',{
                btn: ['确定','取消'] //按钮
                ,cancel: function(index, layero){
                    repeat_flag = false;
					layer.close(index);
                }
            }, function (index) {
				layer.close(index);
                $.ajax({
                    url: ns.url("giftcard://shop/giftcard/isuse"),
                    data: {
                        id: id, status: 1
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function(index){
                repeat_flag = false;
				layer.close(index);
            });
        }

        // 监听单元格编辑
        function editSort(goods_id, event){
            var data = $(event).val();
            if (data == '') {
                $(event).val(0);
                data = 0;
            }

            if(!new RegExp("^-?[0-9]\\d*$").test(data)){
                layer.msg("排序号只能是整数");
                return ;
            }
            if(data<0){
                layer.msg("排序号必须大于0");
                return ;
            }
            $.ajax({
                type: 'POST',
                url: ns.url("giftcard://shop/giftcard/sort"),
                data: {
                    goods_id: goods_id,
                    sort: data
                },
                dataType: 'JSON',
                success: function(res) {
                    layer.msg(res.message);
                    if(res.code==0){
                        table.reload();
                    }
                }
            });
        }

        // 删除
        function del(id){
            if (repeat_flag) return false;
            repeat_flag = true;
            layer.confirm('礼品卡删除将同时删除对应会员获取的礼品卡，请谨慎处理。', {
                title:'删除礼品卡',
                btn: ['确定','取消'] //按钮
                ,cancel: function(index, layero){
                    repeat_flag = false;
					layer.close(index);
                }
            },function (index) {
				layer.close(index);
                $.ajax({
                    url: ns.url("giftcard://shop/giftcard/delete"),
                    data: {
                        giftcard_id: id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function(index){
                repeat_flag = false;
				layer.close(index);
            });
        }

        /**
         * 搜索功能
         */
        form.on('submit(search)', function (data) {
            table.reload({
                page: {curr: 1},
                where: data.field
            });
        });
    });

    function add() {
        location.hash = ns.hash("giftcard://shop/giftcard/add");
    }
</script>
