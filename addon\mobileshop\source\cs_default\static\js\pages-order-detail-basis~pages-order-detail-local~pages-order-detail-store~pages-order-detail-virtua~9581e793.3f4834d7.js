(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-detail-basis~pages-order-detail-local~pages-order-detail-store~pages-order-detail-virtua~9581e793"],{"110e":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";.uni-popup[data-v-1474503b]{position:fixed;top:0;bottom:0;left:0;right:0;z-index:999;overflow:hidden}.uni-popup__mask[data-v-1474503b]{position:absolute;top:0;bottom:0;left:0;right:0;z-index:998;background:rgba(0,0,0,.4);opacity:0}.uni-popup__mask.ani[data-v-1474503b]{transition:all .3s}.uni-popup__mask.uni-bottom[data-v-1474503b],\r\n.uni-popup__mask.uni-center[data-v-1474503b],\r\n.uni-popup__mask.uni-right[data-v-1474503b],\r\n.uni-popup__mask.uni-left[data-v-1474503b],\r\n.uni-popup__mask.uni-top[data-v-1474503b]{opacity:1}.uni-popup__wrapper[data-v-1474503b]{position:absolute;z-index:999;box-sizing:border-box}.uni-popup__wrapper.ani[data-v-1474503b]{transition:all .3s}.uni-popup__wrapper.top[data-v-1474503b]{top:0;left:0;width:100%;-webkit-transform:translateY(-100%);transform:translateY(-100%)}.uni-popup__wrapper.bottom[data-v-1474503b]{bottom:0;left:0;width:100%;-webkit-transform:translateY(100%);transform:translateY(100%);background:#fff}.uni-popup__wrapper.right[data-v-1474503b]{bottom:0;left:0;width:100%;-webkit-transform:translateX(100%);transform:translateX(100%)}.uni-popup__wrapper.left[data-v-1474503b]{bottom:0;left:0;width:100%;-webkit-transform:translateX(-100%);transform:translateX(-100%)}.uni-popup__wrapper.center[data-v-1474503b]{width:100%;height:100%;display:flex;justify-content:center;align-items:center;-webkit-transform:scale(1.2);transform:scale(1.2);opacity:0}.uni-popup__wrapper-box[data-v-1474503b]{position:relative;box-sizing:border-box;border-radius:%?10?%}.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box[data-v-1474503b]{background:#fff}.uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box[data-v-1474503b]{position:relative;max-width:80%;max-height:80%;overflow-y:scroll}.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box[data-v-1474503b],\r\n.uni-popup__wrapper.uni-custom.top .uni-popup__wrapper-box[data-v-1474503b]{width:100%;max-height:500px;overflow-y:scroll}.uni-popup__wrapper.uni-bottom[data-v-1474503b],\r\n.uni-popup__wrapper.uni-top[data-v-1474503b]{-webkit-transform:translateY(0);transform:translateY(0)}.uni-popup__wrapper.uni-left[data-v-1474503b],\r\n.uni-popup__wrapper.uni-right[data-v-1474503b]{-webkit-transform:translateX(0);transform:translateX(0)}.uni-popup__wrapper.uni-center[data-v-1474503b]{-webkit-transform:scale(1);transform:scale(1);opacity:1}\r\n\r\n/* isIphoneX系列手机底部安全距离 */.bottom.safe-area[data-v-1474503b]{padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.left.safe-area[data-v-1474503b]{padding-bottom:%?68?%}.right.safe-area[data-v-1474503b]{padding-bottom:%?68?%}',""]),t.exports=e},1336:function(t,e,a){var o=a("3cdb");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("967d").default;r("42c6a0da",o,!0,{sourceMap:!1,shadowMode:!1})},2441:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"UniPopup",props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},custom:{type:Boolean,default:!1},maskClick:{type:Boolean,default:!0},show:{type:Boolean,default:!0}},data:function(){return{ani:"",showPopup:!1,callback:null,isIphoneX:!1}},watch:{show:function(t){t?this.open():this.close()}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{clear:function(){},open:function(t){var e=this;t&&(this.callback=t),this.$emit("change",{show:!0}),this.showPopup=!0,this.$nextTick((function(){setTimeout((function(){e.ani="uni-"+e.type}),30)}))},close:function(t,e){var a=this;!this.maskClick&&t||(this.$emit("change",{show:!1}),this.ani="",this.$nextTick((function(){setTimeout((function(){a.showPopup=!1}),300)})),e&&e(),this.callback&&this.callback.call(this))}}};e.default=o},"26da":function(t,e,a){"use strict";a.r(e);var o=a("4af4"),r=a("c9c0");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("4cc2");var n=a("828b"),s=Object(n["a"])(r["default"],o["b"],o["c"],!1,null,"1474503b",null,!1,o["a"],void 0);e["default"]=s.exports},"3cdb":function(t,e,a){var o=a("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-2552ce0a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-2552ce0a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-2552ce0a]{position:fixed;left:0;right:0;z-index:998}.popup[data-v-2552ce0a]{width:80vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-2552ce0a]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-2552ce0a]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-2552ce0a]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-2552ce0a]{padding:%?20?%}.popup .popup-body uni-textarea[data-v-2552ce0a]{width:100%}.popup .popup-footer[data-v-2552ce0a]{height:%?120?%}',""]),t.exports=e},4003:function(t,e,a){"use strict";a.r(e);var o=a("b27a"),r=a("9f63");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("8e7c");var n=a("828b"),s=Object(n["a"])(r["default"],o["b"],o["c"],!1,null,"2552ce0a",null,!1,o["a"],void 0);e["default"]=s.exports},"4af4":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.showPopup?a("v-uni-view",{staticClass:"uni-popup"},[a("v-uni-view",{staticClass:"uni-popup__mask",class:[t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}}),t.isIphoneX?a("v-uni-view",{staticClass:"uni-popup__wrapper safe-area",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}},[a("v-uni-view",{staticClass:"uni-popup__wrapper-box",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1):a("v-uni-view",{staticClass:"uni-popup__wrapper",class:[t.type,t.ani,t.animation?"ani":"",t.custom?"":"uni-custom"],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close(!0)}}},[a("v-uni-view",{staticClass:"uni-popup__wrapper-box",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[t._t("default")],2)],1)],1):t._e()},r=[]},"4cc2":function(t,e,a){"use strict";var o=a("872e"),r=a.n(o);r.a},"514b":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a("6638"),r={data:function(){return{repeatFlag:!1,actionCallback:null}},methods:{offlinePay:function(t){var e=this;this.repeatFlag||(this.repeatFlag=!0,uni.showLoading({title:"操作中...",mask:!0}),(0,o.orderOfflinePay)(t).then((function(t){e.$util.showToast({title:t.message}),0==t.code&&e.actionCallback&&e.actionCallback(),e.repeatFlag=!1,uni.hideLoading()})))},orderClose:function(t){var e=this;this.repeatFlag||(this.repeatFlag=!0,uni.showLoading({title:"操作中...",mask:!0}),(0,o.closeOrder)(t).then((function(t){e.$util.showToast({title:t.message}),0==t.code&&e.actionCallback&&e.actionCallback(),e.repeatFlag=!1,uni.hideLoading()})))},storeOrderTakedeliveryFn:function(t){var e=this;this.repeatFlag||(this.repeatFlag=!0,uni.showLoading({title:"操作中...",mask:!0}),(0,o.storeOrderTakeDelivery)(t).then((function(t){e.$util.showToast({title:t.message}),0==t.code&&e.actionCallback&&e.actionCallback(),e.repeatFlag=!1,uni.hideLoading()})))},extendTakeDelivery:function(t){var e=this;uni.showModal({title:"操作提示",content:"确定要延长该订单的收货时间吗？\n单次延长收货可以延迟三天的自动收货时间",success:function(a){if(a.confirm){if(e.repeatFlag)return;e.repeatFlag=!0,uni.showLoading({title:"操作中...",mask:!0}),(0,o.orderExtendTakeDelivery)(t).then((function(t){e.$util.showToast({title:t.message}),0==t.code&&e.actionCallback&&e.actionCallback(),e.repeatFlag=!1,uni.hideLoading()}))}}})},orderDelivery:function(t){this.$util.redirectTo("/pages/order/delivery",{order_id:t})},orderLocalDelivery:function(t){this.$util.redirectTo("/pages/order/local_delivery",{order_id:t})},orderVirtualDelivery:function(t){var e=this;uni.showModal({title:"提示",content:"确定要发货？",success:function(a){a.confirm&&(0,o.orderVirtualDelivery)(t).then((function(t){t.code>=0?(e.$util.showToast({title:"发货成功"}),e.actionCallback&&e.actionCallback()):e.$util.showToast({title:t.message})}))}})},goRefund:function(t){this.$util.redirectTo("/pages/order/refund/detail",{order_goods_id:t})},shopActiveRefund:function(t){this.$util.redirectTo("/pages/order/refund/active_refund",{order_goods_id:t})},orderAdjustMoney:function(t){this.$util.redirectTo("/pages/order/adjust_price",{order_id:t})},orderAddressUpdate:function(t){this.$util.redirectTo("/pages/order/address_update",{order_id:t})},goLog:function(t){this.$util.redirectTo("/pages/order/log",{order_id:t})},takeDelivery:function(t){var e=this;uni.showModal({title:"提示",content:"确保买家已经收到您的商品，并且与买家协商完毕提前确认收货？",success:function(a){a.confirm&&(0,o.ordErtakeDelivery)(t).then((function(t){t.code>=0?(e.$util.showToast({title:"收货成功"}),e.actionCallback&&e.actionCallback()):e.$util.showToast({title:t.message})}))}})}}};e.default=r},6638:function(t,e,a){"use strict";a("6a54");var o=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.adjustOrderPrice=function(t){return r.default.post("/shopapi/order/adjustPrice",{data:t})},e.closeOrder=function(t){return r.default.post("/shopapi/order/close",{data:{order_id:t}})},e.deliveryOrder=function(t){return r.default.post("/shopapi/order/delivery",{data:t})},e.editOrderDelivery=function(t){return r.default.post("/shopapi/order/editOrderDelivery",{data:t})},e.editOrderInvoicelist=function(t){return r.default.post("/shopapi/order/invoiceEdit",{data:t})},e.getOrderCondition=function(){return r.default.get("/shopapi/order/condition")},e.getOrderDetailById=function(t){return r.default.post("/shopapi/order/getOrderDetail",{data:{order_id:t}})},e.getOrderDetailInfoById=function(t){return r.default.post("/shopapi/order/detail",{data:{order_id:t}})},e.getOrderGoodsList=function(t){return r.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:t}})},e.getOrderInfoById=function(t){return r.default.post("/shopapi/order/getOrderInfo",{data:{order_id:t}})},e.getOrderInvoicelist=function(t){return r.default.post("/shopapi/order/invoicelist",{data:t})},e.getOrderList=function(t){return r.default.post("/shopapi/order/lists",{data:t})},e.getOrderLog=function(t){return r.default.post("/shopapi/order/log",{data:{order_id:t}})},e.getOrderPackageList=function(t){return r.default.post("/shopapi/order/package",{data:{order_id:t}})},e.ordErtakeDelivery=function(t){return r.default.post("/shopapi/order/takeDelivery",{data:{order_id:t}})},e.orderExtendTakeDelivery=function(t){return r.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:t}})},e.orderLocalorderDelivery=function(t){return r.default.post("/shopapi/localorder/delivery",{data:t})},e.orderOfflinePay=function(t){return r.default.post("/shopapi/order/offlinePay",{data:{order_id:t}})},e.orderVirtualDelivery=function(t){return r.default.post("/shopapi/virtualorder/delivery",{data:{order_id:t}})},e.storeOrderTakeDelivery=function(t){return r.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:t}})};var r=o(a("9027"))},"872e":function(t,e,a){var o=a("110e");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("967d").default;r("a57c347c",o,!0,{sourceMap:!1,shadowMode:!1})},"8e7c":function(t,e,a){"use strict";var o=a("1336"),r=a.n(o);r.a},"9f63":function(t,e,a){"use strict";a.r(e);var o=a("d4c7"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},b27a:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o}));var o={uniPopup:a("26da").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("uni-popup",{ref:"remarkPopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"popup",on:{touchmove:function(e){e.preventDefault(),e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[a("v-uni-view",{staticClass:"popup-header"},[a("v-uni-text",{staticClass:"tit"},[t._v("卖家备注")]),a("v-uni-text",{staticClass:"iconfont iconclose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close()}}})],1),a("v-uni-view",{staticClass:"popup-body"},[a("v-uni-textarea",{staticClass:"uni-input",attrs:{placeholder:"请输入卖家备注",maxlength:"200"},model:{value:t.order.remark,callback:function(e){t.$set(t.order,"remark",e)},expression:"order.remark"}})],1),a("v-uni-view",{staticClass:"popup-footer"},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.saveRemark()}}},[t._v("确定")])],1)],1)],1)},i=[]},c9c0:function(t,e,a){"use strict";a.r(e);var o=a("2441"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=r.a},d4c7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={data:function(){return{callback:null,repeatFlag:!1}},props:{order:{type:Object,default:function(){return{remark:""}}}},methods:{show:function(t){this.callback=t,this.$refs["remarkPopup"].open()},close:function(){this.$refs["remarkPopup"].close()},saveRemark:function(){var t=this;0!=this.order.remark.length?this.repeatFlag||(this.repeatFlag=!0,this.$api.sendRequest({url:"/shopapi/order/orderRemark",data:{order_id:this.order.order_id,remark:this.order.remark},success:function(e){0==e.code&&(t.callback&&t.callback(),t.close()),t.repeatFlag=!1,t.$util.showToast({title:e.message})}})):this.$util.showToast({title:"请输入卖家备注"})}}};e.default=o}}]);