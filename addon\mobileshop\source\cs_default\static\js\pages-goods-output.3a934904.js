(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-output"],{"03e3":function(t,o,a){"use strict";var e=a("c71d"),i=a.n(e);i.a},"2bd2":function(t,o,a){"use strict";a("6a54"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0,a("5c47"),a("0506"),a("d4b5");var e=a("95a1"),i={data:function(){return{goodsId:0,outputList:[]}},onLoad:function(t){this.goodsId=t.goods_id||0,this.getOutputList()},onShow:function(){},methods:{getOutputList:function(){var t=this;(0,e.getOutputListById)(this.goodsId).then((function(o){t.outputList=o.data,t.$refs.loadingCover&&t.$refs.loadingCover.hide()}))},verify:function(){for(var t=!0,o=0;o<this.outputList.length;o++){var a=this.outputList[o];if(0==a.stock.length){this.$util.showToast({title:"请输入[第".concat(o+1,"个规格]的库存")}),t=!1;break}if(isNaN(a.stock)||!this.$util.data().regExp.number.test(a.stock)){this.$util.showToast({title:"[第".concat(o+1,"个规格的库存]格式输入错误")}),t=!1;break}}return t},save:function(){var t=this;this.verify()&&(0,e.editOutputList)({sku_list:JSON.stringify(this.outputList)}).then((function(o){t.$util.showToast({title:o.message}),0==o.code&&setTimeout((function(){t.$util.redirectTo("/pages/goods/list",{},"redirectTo")}),1e3)}))}}};o.default=i},"4ffb":function(t,o,a){var e=a("c86c");o=e(!1),o.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-11312266]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-11312266]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-11312266]{position:fixed;left:0;right:0;z-index:998}.container[data-v-11312266]{padding-bottom:%?40?%}.safe-area[data-v-11312266]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.goods-edit-wrap[data-v-11312266]{margin-bottom:%?160?%}.form-title[data-v-11312266]{display:flex;justify-content:space-between;margin:%?20?% %?30?%;color:#909399}.item-wrap uni-radio .uni-radio-input[data-v-11312266]{width:%?30?%!important;height:%?30?%!important}.item-wrap[data-v-11312266]{background:#fff;margin-top:%?20?%}.item-wrap .goods-type[data-v-11312266]{display:flex;margin:0 %?40?% %?20?% %?40?%;flex-wrap:wrap}.item-wrap .goods-type uni-view[data-v-11312266]{flex:1;text-align:center;border:1px solid #ccc;color:#909399;margin-right:%?40?%;margin-top:%?30?%;position:relative;height:%?80?%;line-height:%?80?%;white-space:nowrap;min-width:calc((100% - %?100?%) / 3);max-width:calc((100% - %?100?%) / 3)}.item-wrap .goods-type uni-view[data-v-11312266]:nth-child(3n+3){margin-right:0}.item-wrap .goods-type uni-view .iconfont[data-v-11312266]{display:none}.item-wrap .goods-type uni-view.selected .iconfont[data-v-11312266]{display:block;position:absolute;bottom:%?-22?%;right:%?-22?%;font-size:%?80?%}.item-wrap .form-wrap[data-v-11312266]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-11312266]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-11312266]{font-weight:700}.item-wrap .form-wrap .label[data-v-11312266]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap uni-input[data-v-11312266]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-11312266]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-11312266]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-11312266]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.more-wrap .action[data-v-11312266]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.item-wrap .form-wrap.goods-img[data-v-11312266]{height:%?200?%;line-height:%?200?%;display:block;position:relative}.item-wrap .form-wrap.goods-img .label[data-v-11312266]{display:inline-block}.item-wrap .form-wrap.goods-img .img-list[data-v-11312266]{position:absolute;width:80%;top:0;left:%?100?%;margin-top:%?40?%;margin-left:%?40?%}.item-wrap .form-wrap.goods-img .tips[data-v-11312266]{color:#909399;font-size:%?20?%;margin-top:%?20?%}.item-wrap .form-wrap .unit[data-v-11312266]{margin-left:%?20?%;width:%?40?%}.item-wrap .form-wrap.join-member-discount .label[data-v-11312266]{flex:1}.item-wrap .form-wrap.validity-type[data-v-11312266]{border-bottom:1px solid #eee!important}.footer-wrap[data-v-11312266]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;background-color:#fff}.popup[data-v-11312266]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-11312266]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-11312266]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-11312266]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-11312266]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-11312266]{height:calc(100% - %?270?%)}.popup.category[data-v-11312266]{height:50vh}.popup.category .popup-header[data-v-11312266]{border-bottom:none}.popup.category .popup-body[data-v-11312266]{padding:0 %?30?%}.popup.category .popup-body .nav[data-v-11312266]{border-bottom:%?2?% solid #eee;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.popup.category .popup-body .nav uni-text[data-v-11312266]{padding:0 0 %?20?% 0;margin-right:%?20?%;display:inline-block}.popup.category .popup-body .nav uni-text[data-v-11312266]:last-child{padding-right:0}.popup.category .popup-body .nav uni-text.selected[data-v-11312266]{border-bottom:2px solid}.popup.category .popup-body .category[data-v-11312266]{height:100%}.popup.category .popup-body .category .item[data-v-11312266]{display:block;margin:%?20?% 0 0}.popup.category .popup-body .category .item uni-text[data-v-11312266]:first-child{overflow:hidden;text-overflow:ellipsis;white-space:pre;width:90%;display:inline-block;vertical-align:middle}.popup.category .popup-body .category .item .iconfont[data-v-11312266]{float:right}.popup.category .popup-body .category .child-item[data-v-11312266]{display:flex;justify-content:space-between;margin-left:%?40?%}.popup.choose-picture[data-v-11312266]{background-color:#f8f8f8}.popup.choose-picture .popup-header[data-v-11312266]{border-bottom:none;background-color:#fff}.popup.choose-picture .popup-body[data-v-11312266]{background-color:#f8f8f8;height:auto}.popup.choose-picture .popup-body .select-wrap[data-v-11312266]{background-color:#fff;padding:0 %?30?%}.popup.choose-picture .popup-body .item[data-v-11312266]{text-align:center;padding:%?20?%;background-color:#fff;border-bottom:1px solid #eee}.popup.choose-picture .popup-body .item[data-v-11312266]:last-child{border-bottom:none}.popup.choose-picture .popup-body .item.cancle[data-v-11312266]{margin-top:%?20?%}.sku-list[data-v-11312266]{margin-bottom:%?160?%}.sku-list .item-wrap[data-v-11312266]{background:#fff;margin:%?20?% %?30?%}.sku-list .item-wrap .form-wrap .value[data-v-11312266]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}',""]),t.exports=o},"5b56":function(t,o,a){"use strict";a.d(o,"b",(function(){return i})),a.d(o,"c",(function(){return r})),a.d(o,"a",(function(){return e}));var e={loadingCover:a("59c1").default},i=function(){var t=this,o=t.$createElement,a=t._self._c||o;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticClass:"sku-list"},t._l(t.outputList,(function(o,e){return a("v-uni-view",{key:e,staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("规格")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(o.spec_name||o.sku_name))])],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("销售价")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(o.price))]),a("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-view",{staticClass:"label"},[a("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),a("v-uni-text",[t._v("库存")])],1),a("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:o.stock,callback:function(a){t.$set(o,"stock",a)},expression:"item.stock"}}),a("v-uni-text",{staticClass:"unit"},[t._v("件")])],1)],1)})),1),a("v-uni-view",{staticClass:"footer-wrap"},[a("v-uni-button",{attrs:{type:"primary"},on:{click:function(o){arguments[0]=o=t.$handleEvent(o),t.save()}}},[t._v("保存")])],1),a("loading-cover",{ref:"loadingCover"})],1)},r=[]},"8fe0":function(t,o,a){"use strict";a.r(o);var e=a("5b56"),i=a("a0c5");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(o,t,(function(){return i[t]}))}(r);a("03e3");var d=a("828b"),p=Object(d["a"])(i["default"],e["b"],e["c"],!1,null,"11312266",null,!1,e["a"],void 0);o["default"]=p.exports},"95a1":function(t,o,a){"use strict";a("6a54");var e=a("f5bd").default;Object.defineProperty(o,"__esModule",{value:!0}),o.addGoods=function(t){return i.default.post("/shopapi/goods/addGoods",{data:t})},o.addVirtualCardGoods=function(t){return i.default.post("/virtualcard/shopapi/virtualgoods/addGoods",{data:t})},o.addVirtualGoods=function(t){return i.default.post("/shopapi/virtualgoods/addGoods",{data:t})},o.copyGoods=function(t){return i.default.post("/shopapi/goods/copyGoods",{data:{goods_id:t}})},o.deleteGoods=function(t){return i.default.post("/shopapi/goods/deleteGoods",{data:{goods_ids:t}})},o.editGoods=function(t){return i.default.post("/shopapi/goods/editGoods",{data:t})},o.editOutputList=function(t){return i.default.post("/shopapi/goods/editGoodsStock",{data:t})},o.editVirtualCardGoods=function(t){return i.default.post("/virtualcard/shopapi/virtualgoods/editGoods",{data:t})},o.editVirtualGoods=function(t){return i.default.post("/shopapi/virtualgoods/editGoods",{data:t})},o.getAttrClassList=function(){return i.default.get("/shopapi/goods/getAttrClassList")},o.getAttributeListById=function(t){return i.default.post("/shopapi/goods/getAttributeList",{data:{attr_class_id:t}})},o.getCategoryTree=function(t){return i.default.post("/shopapi/goods/getCategoryTree",{data:t})},o.getCondition=function(){return i.default.get("/shopapi/goods/condition")},o.getGoodsInfoById=function(t){return i.default.post("/shopapi/goods/editGetGoodsInfo",{data:{goods_id:t}})},o.getGoodsLists=function(t){return i.default.post("/shopapi/goods/lists",{data:t})},o.getOutputListById=function(t){return i.default.post("/shopapi/goods/getOutputList",{data:{goods_id:t}})},o.getVerifyStateRemark=function(t){return i.default.post("/shopapi/goods/getVerifyStateRemark",{data:{goods_id:t}})},o.offGoods=function(t){return i.default.post("/shopapi/goods/offGoods",{data:t})},o.onGoods=function(t){return i.default.post("/shopapi/goods/onGoods",{data:t})};var i=e(a("9027"))},a0c5:function(t,o,a){"use strict";a.r(o);var e=a("2bd2"),i=a.n(e);for(var r in e)["default"].indexOf(r)<0&&function(t){a.d(o,t,(function(){return e[t]}))}(r);o["default"]=i.a},c71d:function(t,o,a){var e=a("4ffb");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var i=a("967d").default;i("b83e3988",e,!0,{sourceMap:!1,shadowMode:!1})}}]);