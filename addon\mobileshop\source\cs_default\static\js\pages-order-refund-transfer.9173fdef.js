(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-refund-transfer"],{"0c20":function(e,t,a){"use strict";a.r(t);var r=a("ce05"),i=a("9ec9");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("3323");var o=a("828b"),d=Object(o["a"])(i["default"],r["b"],r["c"],!1,null,"e902b80c",null,!1,r["a"],void 0);t["default"]=d.exports},3323:function(e,t,a){"use strict";var r=a("f285"),i=a.n(r);i.a},"34d9":function(e,t,a){var r=a("c86c");t=r(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-e902b80c]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-e902b80c]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-e902b80c]{position:fixed;left:0;right:0;z-index:998}.item-wrap[data-v-e902b80c]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-e902b80c]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap.border-none[data-v-e902b80c]{border-bottom:none}.item-wrap .form-wrap[data-v-e902b80c]:last-child{border-bottom:none}.item-wrap .form-wrap .label[data-v-e902b80c]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap .value[data-v-e902b80c]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap .value.money[data-v-e902b80c]{font-weight:700}.item-wrap .form-wrap .value .radio-wrap[data-v-e902b80c]{display:inline-block;vertical-align:middle;margin-right:%?20?%}.item-wrap .form-wrap .value .radio[data-v-e902b80c]{margin-right:%?10?%}.item-wrap .form-wrap.reason[data-v-e902b80c]{height:auto;display:block;position:relative;flex-direction:column}.item-wrap .form-wrap.reason uni-textarea[data-v-e902b80c]{width:100%}.item-wrap .form-wrap.picker[data-v-e902b80c]{border-bottom:1px solid #eee}.item-wrap .form-wrap .uni-input[data-v-e902b80c]{vertical-align:middle;display:inline-block;-webkit-box-flex:1;-webkit-flex:1;flex:1;text-align:left}.item-wrap .form-wrap.more-wrap .selected[data-v-e902b80c]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-e902b80c]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-e902b80c]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.more-wrap .action[data-v-e902b80c]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.item-wrap .form-wrap .align-right[data-v-e902b80c]{text-align:right}.item-wrap .form-tips[data-v-e902b80c]{margin-top:%?-10?%;border-bottom:1px solid #eee;padding-bottom:%?10?%}.item-wrap .form-tips uni-view[data-v-e902b80c]{color:#ccc;font-weight:400;font-size:12px;padding-left:%?24?%}.tips[data-v-e902b80c]{font-weight:700;margin:%?20?% %?30?% 0}.footer-wrap[data-v-e902b80c]{width:100%;padding:%?40?% 0;display:flex}.footer-wrap uni-button[data-v-e902b80c]{flex:1}',""]),e.exports=t},5213:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a("77e0"),i={data:function(){return{orderGoodsId:0,isIphoneX:!1,detail:{},orderInfo:{},actionCallback:null,repeatFlag:!1,refundRealMoney:"",refundTypeArray:["原路退款","线下退款","退款到余额"],refundType:0}},onLoad:function(e){this.orderGoodsId=e.order_goods_id||0},onShow:function(){var e=this;this.$util.checkToken("/pages/order/refund/detail?order_goods_id="+this.orderGoodsId)&&(this.getOrderDetail(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.actionCallback=function(){e.getOrderDetail()})},methods:{getOrderDetail:function(){var e=this;(0,r.getOrderRefundInfoById)(this.orderGoodsId).then((function(t){if(0==t.code){var a=t.data;if(e.detail=a.detail,e.detail.refund_images=a.detail.refund_images?a.detail.refund_images.split(","):"",e.refundRealMoney=a.detail.refund_apply_money,""==e.detail.refund_address){var r=uni.getStorageSync("shop_info")?JSON.parse(uni.getStorageSync("shop_info")):{};e.detail.refund_address="商家未设置联系地址",(r.full_address||r.address)&&(e.detail.refund_address=r.full_address+" "+r.address)}e.orderInfo=a.order_info,e.detail.sku_spec_format=e.detail.sku_spec_format?JSON.parse(e.detail.sku_spec_format):[],e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:t.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)}))},cancel:function(){uni.navigateBack({delta:1})},orderRefundRefuse:function(e){this.$util.redirectTo("/pages/order/refund/refuse",{order_goods_id:e})},orderRefundAgree:function(e){this.$util.redirectTo("/pages/order/refund/agree",{order_goods_id:e})},orderRefundTakeDelivery:function(e){this.$util.redirectTo("/pages/order/refund/take_delivery",{order_goods_id:e})},orderRefundTransfer:function(e){this.$util.redirectTo("/pages/order/refund/transfer",{order_goods_id:e})},orderRefundClose:function(e){var t=this;uni.showModal({title:"提示",content:"确定要关闭本次维权吗？",success:function(a){a.confirm&&(0,r.closeOrderRefund)(e).then((function(e){e.code>=0?(t.$util.showToast({title:"维权已关闭"}),t.actionCallback&&t.actionCallback()):t.$util.showToast({title:e.message})}))}})},refundTypeChange:function(e){this.refundType=e.detail.value},previewRefundImage:function(e){uni.previewImage({current:e,urls:this.detail.refund_images})}}};t.default=i},"77e0":function(e,t,a){"use strict";a("6a54");var r=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.closeOrderRefund=function(e){return i.default.post("/shopapi/orderrefund/close",{data:{order_goods_id:e}})},t.getOrderRefundCondition=function(){return i.default.get("/shopapi/orderrefund/condition")},t.getOrderRefundInfoById=function(e){return i.default.post("/shopapi/orderrefund/detail",{data:{order_goods_id:e}})},t.getOrderRefundList=function(e){return i.default.post("/shopapi/orderrefund/lists",{data:e})};var i=r(a("9027"))},"9ec9":function(e,t,a){"use strict";a.r(t);var r=a("ea0f"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},ce05:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return r}));var r={loadingCover:a("59c1").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[e._v("申请退款金额")]),a("v-uni-text",{staticClass:"value color-base-text money"},[e._v("￥"+e._s(e.detail.refund_apply_money)+e._s(e.detail.refund_delivery_money>0?"(含运费"+e.detail.refund_delivery_money+")":""))])],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[e._v("实际退款金额")]),a("v-uni-input",{staticClass:"uni-input align-right",attrs:{type:"digit",placeholder:"请输入退款金额"},model:{value:e.refundRealMoney,callback:function(t){e.refundRealMoney=t},expression:"refundRealMoney"}})],1),e.detail.use_point>0?a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[e._v("退还积分")]),a("v-uni-text",{staticClass:"value"},[e._v(e._s(e.detail.use_point)+"积分")])],1):e._e(),a("v-uni-picker",{attrs:{value:e.refundType,range:e.refundTypeArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.refundTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"form-wrap more-wrap picker"},[a("v-uni-text",{staticClass:"label"},[e._v("退款方式")]),a("v-uni-text",{staticClass:"selected color-title"},[e._v(e._s(e.refundTypeArray[e.refundType]))]),a("v-uni-text",{staticClass:"iconfont iconright"})],1)],1),a("v-uni-view",{staticClass:"form-wrap reason"},[a("v-uni-text",{staticClass:"label"},[e._v("退款说明")]),a("v-uni-textarea",{staticClass:"uni-input",attrs:{placeholder:"请输入退款说明",maxlength:"200"},model:{value:e.refundRefuseReason,callback:function(t){e.refundRefuseReason=t},expression:"refundRefuseReason"}})],1)],1),a("v-uni-view",{staticClass:"footer-wrap"},[a("v-uni-button",{attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.cancel()}}},[e._v("取消")]),a("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save()}}},[e._v("确认转账")])],1),a("loading-cover",{ref:"loadingCover"})],1)},n=[]},ea0f:function(e,t,a){"use strict";a("6a54");var r=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e838");var i=r(a("5213")),n={data:function(){return{}},mixins:[i.default],methods:{save:function(){var e=this;if(isNaN(parseFloat(this.refundRealMoney)))this.$util.showToast({title:"请输入正确的退款金额"});else if(parseFloat(this.refundRealMoney)<0)this.$util.showToast({title:"退款金额不能为负数"});else if(!this.repeatFlag){this.repeatFlag=!0;var t=this.refundType+1;this.$api.sendRequest({url:"/shopapi/orderrefund/complete",data:{order_goods_id:this.orderGoodsId,refund_money_type:t,shop_refund_remark:this.refundRefuseReason,refund_real_money:this.refundRealMoney},success:function(t){0==t.code&&setTimeout((function(){e.cancel()}),1e3),e.repeatFlag=!1,e.$util.showToast({title:t.message})}})}}}};t.default=n},f285:function(e,t,a){var r=a("34d9");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var i=a("967d").default;i("ff20f676",r,!0,{sourceMap:!1,shadowMode:!1})}}]);