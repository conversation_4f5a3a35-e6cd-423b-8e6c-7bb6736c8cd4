(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-modify_pwd"],{"13e6":function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.getCaptcha=function(e){return i.default.post("/shopapi/captcha/captcha",{data:{captcha_id:e}})},r.login=function(e){return i.default.post("/shopapi/login/login",{data:e})},r.modifyPassword=function(e){return i.default.post("/shopapi/login/modifyPassword",{data:e})};var i=a(t("9027"))},"3c82":function(e,r,t){"use strict";var a=t("a9ff"),i=t.n(a);i.a},"5ec1":function(e,r,t){"use strict";t.d(r,"b",(function(){return a})),t.d(r,"c",(function(){return i})),t.d(r,"a",(function(){}));var a=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("v-uni-view",[t("v-uni-view",{staticClass:"item-wrap"},[t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-view",{staticClass:"label"},[e._v("原密码")]),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入原密码",maxlength:"100",password:"true"},model:{value:e.formData.old_pass,callback:function(r){e.$set(e.formData,"old_pass",r)},expression:"formData.old_pass"}})],1),t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-view",{staticClass:"label"},[e._v("新密码")]),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入新密码",maxlength:"100",password:"true"},model:{value:e.formData.new_pass,callback:function(r){e.$set(e.formData,"new_pass",r)},expression:"formData.new_pass"}})],1),t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-view",{staticClass:"label"},[e._v("确认密码")]),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入确认密码",maxlength:"100",password:"true"},model:{value:e.formData.repeat_pass,callback:function(r){e.$set(e.formData,"repeat_pass",r)},expression:"formData.repeat_pass"}})],1)],1),t("v-uni-button",{attrs:{type:"primary"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.save()}}},[e._v("保存")])],1)},i=[]},"61e2":function(e,r,t){t("23f4"),t("7d2f"),t("5c47"),t("9c4e"),t("ab80"),t("0506"),t("64aa"),t("5ef2"),e.exports={error:"",check:function(e,r){for(var t=0;t<r.length;t++){if(!r[t].checkType)return!0;if(!r[t].name)return!0;if(!r[t].errorMsg)return!0;if(!e[r[t].name])return this.error=r[t].errorMsg,!1;switch(r[t].checkType){case"custom":if("function"==typeof r[t].validate&&!r[t].validate(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"required":var a=new RegExp("/[S]+/");if(a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"string":a=new RegExp("^.{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"digit":a=new RegExp("^(d{0,10}(.?d{0,2}){"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"between":if(!this.isNumber(e[r[t].name]))return this.error=r[t].errorMsg,!1;var i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"same":if(e[r[t].name]!=r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"notsame":if(e[r[t].name]==r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"email":a=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"phoneno":a=/^\d{11}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"reg":a=new RegExp(r[t].checkRule);if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"in":if(-1==r[t].checkRule.indexOf(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"notnull":if(0==e[r[t].name]||void 0==e[r[t].name]||null==e[r[t].name]||e[r[t].name].length<1)return this.error=r[t].errorMsg,!1;break;case"lengthMin":if(e[r[t].name].length<r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"lengthMax":if(e[r[t].name].length>r[t].checkRule)return this.error=r[t].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"6ae8":function(e,r,t){"use strict";t.r(r);var a=t("5ec1"),i=t("9c61");for(var n in i)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return i[e]}))}(n);t("3c82");var s=t("828b"),o=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"3e3f47fe",null,!1,a["a"],void 0);r["default"]=o.exports},"7d9e":function(e,r,t){var a=t("c86c");r=a(!1),r.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-3e3f47fe]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-3e3f47fe]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-3e3f47fe]{position:fixed;left:0;right:0;z-index:998}.item-wrap[data-v-3e3f47fe]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-3e3f47fe]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;min-height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-3e3f47fe]:last-child{border-bottom:none}.item-wrap .form-wrap .label[data-v-3e3f47fe]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap uni-input[data-v-3e3f47fe]{vertical-align:middle;display:inline-block;flex:1;text-align:right}uni-button[data-v-3e3f47fe]{margin-top:%?40?%}',""]),e.exports=r},"9c61":function(e,r,t){"use strict";t.r(r);var a=t("d544"),i=t.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return a[e]}))}(n);r["default"]=i.a},a9ff:function(e,r,t){var a=t("7d9e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=t("967d").default;i("edc78bdc",a,!0,{sourceMap:!1,shadowMode:!1})},d544:function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i=a(t("61e2")),n=t("13e6"),s={data:function(){return{formData:{old_pass:"",new_pass:"",repeat_pass:""}}},onShow:function(){this.$util.checkToken("/pages/my/index")},methods:{save:function(){var e=this;this.verify()&&(0,n.modifyPassword)({new_pass:this.formData.new_pass,old_pass:this.formData.old_pass}).then((function(r){e.$util.showToast({title:r.message}),0==r.code&&setTimeout((function(){uni.navigateBack({delta:1})}),1e3)}))},verify:function(){var e=e=[{name:"old_pass",checkType:"required",errorMsg:"请输入原密码"},{name:"new_pass",checkType:"required",errorMsg:"请输入新密码"},{name:"repeat_pass",checkType:"required",errorMsg:"请输入确认密码"}],r=i.default.check(this.formData,e);return r?this.formData.new_pass==this.formData.repeat_pass||(this.$util.showToast({title:"两次密码不一致"}),!1):(this.$util.showToast({title:i.default.error}),!1)}}};r.default=s}}]);