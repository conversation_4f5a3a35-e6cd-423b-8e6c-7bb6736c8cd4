(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-delivery"],{"075c":function(e,t,i){"use strict";i("6a54");var r=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getExpressCompanyList=function(){return a.default.get("/shopapi/express/expressCompany")},t.getExpressTemplateList=function(){return a.default.get("/shopapi/express/getExpressTemplateList")};var a=r(i("9027"))},3011:function(e,t,i){"use strict";var r=i("79ee"),a=i.n(r);a.a},3425:function(e,t,i){"use strict";i.r(t);var r=i("c06c"),a=i.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},6638:function(e,t,i){"use strict";i("6a54");var r=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.adjustOrderPrice=function(e){return a.default.post("/shopapi/order/adjustPrice",{data:e})},t.closeOrder=function(e){return a.default.post("/shopapi/order/close",{data:{order_id:e}})},t.deliveryOrder=function(e){return a.default.post("/shopapi/order/delivery",{data:e})},t.editOrderDelivery=function(e){return a.default.post("/shopapi/order/editOrderDelivery",{data:e})},t.editOrderInvoicelist=function(e){return a.default.post("/shopapi/order/invoiceEdit",{data:e})},t.getOrderCondition=function(){return a.default.get("/shopapi/order/condition")},t.getOrderDetailById=function(e){return a.default.post("/shopapi/order/getOrderDetail",{data:{order_id:e}})},t.getOrderDetailInfoById=function(e){return a.default.post("/shopapi/order/detail",{data:{order_id:e}})},t.getOrderGoodsList=function(e){return a.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:e}})},t.getOrderInfoById=function(e){return a.default.post("/shopapi/order/getOrderInfo",{data:{order_id:e}})},t.getOrderInvoicelist=function(e){return a.default.post("/shopapi/order/invoicelist",{data:e})},t.getOrderList=function(e){return a.default.post("/shopapi/order/lists",{data:e})},t.getOrderLog=function(e){return a.default.post("/shopapi/order/log",{data:{order_id:e}})},t.getOrderPackageList=function(e){return a.default.post("/shopapi/order/package",{data:{order_id:e}})},t.ordErtakeDelivery=function(e){return a.default.post("/shopapi/order/takeDelivery",{data:{order_id:e}})},t.orderExtendTakeDelivery=function(e){return a.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:e}})},t.orderLocalorderDelivery=function(e){return a.default.post("/shopapi/localorder/delivery",{data:e})},t.orderOfflinePay=function(e){return a.default.post("/shopapi/order/offlinePay",{data:{order_id:e}})},t.orderVirtualDelivery=function(e){return a.default.post("/shopapi/virtualorder/delivery",{data:{order_id:e}})},t.storeOrderTakeDelivery=function(e){return a.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:e}})};var a=r(i("9027"))},"6aba":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return r}));var r={loadingCover:i("59c1").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("v-uni-view",{staticClass:"form-title"},[e._v("选择发货商品")]),i("v-uni-view",{staticClass:"goods-wrap"},e._l(e.orderGoodsList,(function(t,r){return i("v-uni-view",{key:r,staticClass:"goods-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.change(r)}}},[i("v-uni-view",{staticClass:"iconfont",class:[t.checked?"iconyuan_checked color-base-text":"iconyuan_checkbox",t.disabled?"disabled":""]}),i("v-uni-view",{staticClass:"goods-img"},[i("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imgError(r)}}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[e._v(e._s(t.sku_name))]),i("v-uni-view",{staticClass:"num"},[e._v("x"+e._s(t.num))]),t.delivery_no?i("v-uni-view",{staticClass:"delivery-no"},[e._v("物流单号："+e._s(t.delivery_no))]):e._e(),i("v-uni-view",{staticClass:"delivery-status-name color-base-text"},[i("v-uni-text",[e._v(e._s(t.delivery_status_name))]),0!=t.refund_status?i("v-uni-view",{staticClass:"refund",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.goRefund(t.order_goods_id)}}},[i("v-uni-text",[e._v(e._s(t.refund_status_name))]),i("v-uni-text",{staticClass:"color-base-text"},[e._v("(处理维权)")])],1):e._e()],1)],1)],1)})),1),i("v-uni-view",{staticClass:"form-title"},[e._v("填写物流信息")]),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"form-wrap"},[i("v-uni-text",{staticClass:"label"},[e._v("收货地址")]),i("v-uni-text",{staticClass:"value"},[e._v(e._s(e.order.full_address)+" "+e._s(e.order.address))])],1),i("v-uni-view",{staticClass:"form-wrap delivery-way"},[i("v-uni-text",{staticClass:"label"},[e._v("发货方式")]),i("v-uni-button",{attrs:{type:1==e.data.delivery_type?"primary":"default",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.data.delivery_type=1}}},[e._v("物流发货")]),i("v-uni-button",{attrs:{type:0==e.data.delivery_type?"primary":"default",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.data.delivery_type=0}}},[e._v("无需物流")])],1),1==e.data.delivery_type?[i("v-uni-view",{staticClass:"form-wrap more-wrap"},[i("v-uni-text",{staticClass:"label"},[e._v("物流公司")]),i("v-uni-picker",{staticClass:"selected",attrs:{value:e.picker.index,range:e.picker.arr},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindPickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input",class:{"color-tip":!e.company_name}},[e._v(e._s(e.company_name?e.company_name:"请选择物流公司"))])],1),i("v-uni-text",{staticClass:"iconfont iconright"})],1),i("v-uni-view",{staticClass:"form-wrap"},[i("v-uni-view",{staticClass:"label"},[e._v("快递单号")]),i("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入快递单号"},model:{value:e.data.delivery_no,callback:function(t){e.$set(e.data,"delivery_no",t)},expression:"data.delivery_no"}})],1)]:e._e()],2),i("v-uni-view",{staticClass:"footer-wrap"},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save()}}},[e._v("确定")])],1),i("loading-cover",{ref:"loadingCover"})],1)},o=[]},"79ee":function(e,t,i){var r=i("c1e7");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=i("967d").default;a("d6b023ba",r,!0,{sourceMap:!1,shadowMode:!1})},"86a8":function(e,t,i){"use strict";i.r(t);var r=i("6aba"),a=i("3425");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("3011");var d=i("828b"),n=Object(d["a"])(a["default"],r["b"],r["c"],!1,null,"677d666e",null,!1,r["a"],void 0);t["default"]=n.exports},c06c:function(e,t,i){"use strict";i("6a54");var r=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("bf0f"),i("2797"),i("aa9c"),i("c9b5"),i("ab80");var a=r(i("2634")),o=r(i("2fdc")),d=i("6638"),n=i("075c"),s={data:function(){return{order:{},orderGoodsList:[],repeatFlag:!1,data:{order_id:0,delivery_type:1,express_company_id:0,delivery_no:"",order_goods_ids:[]},expressCompany:[],company_name:"",picker:{index:0,arr:[]}}},onLoad:function(e){var t=this;return(0,o.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.data.order_id=e.order_id||0,t.getShopExpressCompanyList(),i.next=4,t.getOrderInfo();case 4:t.getOrderGoodsListFn();case 5:case"end":return i.stop()}}),i)})))()},onShow:function(){},methods:{getOrderInfo:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,d.getOrderInfoById)(e.data.order_id);case 2:i=t.sent,0==i.code?e.order=i.data:(e.$util.showToast({title:i.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3));case 4:case"end":return t.stop()}}),t)})))()},getOrderGoodsListFn:function(){var e=this;(0,d.getOrderGoodsList)(this.data.order_id).then((function(t){0==t.code&&(e.orderGoodsList=t.data,e.orderGoodsList.forEach((function(t){t.checked=!0,t.disabled=0!=t.delivery_status||1==e.order.is_lock})),e.$refs.loadingCover&&e.$refs.loadingCover.hide())}))},getShopExpressCompanyList:function(){var e=this;(0,n.getExpressCompanyList)().then((function(t){0==t.code&&(e.expressCompany=t.data,e.expressCompany.forEach((function(t,i){e.picker.arr.push(t.company_name)})))}))},bindPickerChange:function(e){0!=this.expressCompany.length&&(this.picker.index=e.target.value,this.data.express_company_id=this.expressCompany[this.picker.index].company_id,this.company_name=this.expressCompany[this.picker.index].company_name)},change:function(e){this.orderGoodsList[e].disabled||(this.orderGoodsList[e].checked=!this.orderGoodsList[e].checked,this.$forceUpdate())},goRefund:function(e){this.$util.redirectTo("/pages/order/refund/detail",{order_goods_id:e})},verify:function(){var e=0;if(this.orderGoodsList.forEach((function(t){!t.disabled&&t.checked&&e++,t.disabled&&t.checked&&e++})),0==e)return this.$util.showToast({title:"请选择发货商品"}),!1;if(1==this.data.delivery_type){if(0==this.data.express_company_id)return this.$util.showToast({title:"请选择物流公司"}),!1;if(0==this.data.delivery_no.length)return this.$util.showToast({title:"请输入快递单号"}),!1}return!0},save:function(){var e=this;this.verify()&&(0==this.data.delivery_type&&(this.data.express_company_id=0,this.data.delivery_no=""),this.data.order_goods_ids=[],this.orderGoodsList.forEach((function(t){!t.disabled&&t.checked&&e.data.order_goods_ids.push(t.order_goods_id),(t.delivery_status&&"未发货"==t.delivery_status_name||e.order.is_lock&&"未发货"==t.delivery_status_name)&&e.data.order_goods_ids.push(t.order_goods_id)})),this.data.order_goods_ids=this.data.order_goods_ids.toString(),this.repeatFlag||(this.repeatFlag=!0,(0,d.deliveryOrder)(this.data).then((function(t){0==t.code?(e.$util.showToast({title:"发货成功"}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)):(e.$util.showToast({title:t.message}),e.repeatFlag=!1)}))))},imgError:function(e){this.orderGoodsList[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()}}};t.default=s},c1e7:function(e,t,i){var r=i("c86c");t=r(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-677d666e]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-677d666e]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-677d666e]{position:fixed;left:0;right:0;z-index:998}.form-title[data-v-677d666e]{margin:%?20?% %?30?%;color:#909399}.goods-item[data-v-677d666e]{background:#fff;margin-top:%?20?%;display:flex;position:relative;flex-flow:row wrap;padding:%?20?% %?30?%;align-items:center}.goods-item .iconfont[data-v-677d666e]{font-size:%?40?%;margin-right:%?30?%;color:#909399}.goods-item .iconfont.disabled[data-v-677d666e]{color:#909399!important}.goods-item .goods-img[data-v-677d666e]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.goods-item .goods-img uni-image[data-v-677d666e]{width:100%;height:100%}.goods-item .info-wrap[data-v-677d666e]{flex:1;display:flex;flex-direction:column}.goods-item .info-wrap .name-wrap[data-v-677d666e]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-bottom:%?10?%}.goods-item .info-wrap .num[data-v-677d666e]{margin-top:%?4?%;color:#909399;font-size:%?20?%;line-height:1}.goods-item .info-wrap .delivery-no[data-v-677d666e]{margin-bottom:%?4?%;display:inline-block;line-height:1;font-size:%?24?%;flex:1;text-align:right}.goods-item .info-wrap .delivery-status-name[data-v-677d666e]{text-align:right;font-weight:700}.goods-item .info-wrap .delivery-status-name .refund uni-text[data-v-677d666e]:first-child{font-weight:400}.item-wrap[data-v-677d666e]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-677d666e]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-677d666e]:last-child{border-bottom:none}.item-wrap .form-wrap .label[data-v-677d666e]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap.delivery-way .label[data-v-677d666e]{flex:1}.item-wrap .form-wrap.delivery-way uni-button[data-v-677d666e]:last-child{margin-left:%?20?%!important}.item-wrap .form-wrap .value[data-v-677d666e]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap uni-input[data-v-677d666e]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-677d666e]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-677d666e]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-677d666e]{color:#909399;margin-left:%?20?%}.footer-wrap[data-v-677d666e]{width:100%;padding:%?40?% 0;bottom:0;z-index:10}',""]),e.exports=t}}]);