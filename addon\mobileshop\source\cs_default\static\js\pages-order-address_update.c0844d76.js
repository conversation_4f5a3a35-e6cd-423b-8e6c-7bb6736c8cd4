(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-address_update"],{"05ca":function(e,r,t){"use strict";var a=t("1fee"),i=t.n(a);i.a},"095e":function(e,r,t){"use strict";t.r(r);var a=t("6f94"),i=t("5f83");for(var n in i)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return i[e]}))}(n);var d=t("828b"),s=Object(d["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);r["default"]=s.exports},"1fee":function(e,r,t){var a=t("247a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=t("967d").default;i("d9c8c3b4",a,!0,{sourceMap:!1,shadowMode:!1})},"247a":function(e,r,t){var a=t("c86c");r=a(!1),r.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-4c7bb94c]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-4c7bb94c]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-4c7bb94c]{position:fixed;left:0;right:0;z-index:998}.item-wrap[data-v-4c7bb94c]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-4c7bb94c]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-4c7bb94c]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-4c7bb94c]{font-weight:700}.item-wrap .form-wrap .label[data-v-4c7bb94c]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap uni-input[data-v-4c7bb94c]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-4c7bb94c]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .iconfont[data-v-4c7bb94c]{color:#909399;margin-left:%?20?%}.footer-wrap[data-v-4c7bb94c]{width:100%;padding:%?40?% 0}',""]),e.exports=r},"5f81":function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i=a(t("5de6")),n=a(t("2634")),d=a(t("2fdc"));t("fd3c"),t("bf0f"),t("2797"),t("aa9c");var s={props:{defaultRegions:{type:Array}},data:function(){return{pickerValueArray:[],cityArr:[],districtArr:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1}},watch:{defaultRegions:{handler:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];3===e.length&&e.join("")!==r.join("")&&this.handleDefaultRegions()},immediate:!0}},computed:{multiArray:function(){if(this.isLoadDefaultAreas){var e=this.pickedArr.map((function(e){return e.map((function(e){return e.label}))}));return e}},pickedArr:function(){return this.isInitMultiArray?[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:[this.pickerValueArray[0],this.cityArr,this.districtArr]}},created:function(){this.getDefaultAreas(0,{level:0})},methods:{handleColumnChange:function(e){var r=this;return(0,d.default)((0,n.default)().mark((function t(){var a,i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:r.isInitMultiArray=!1,a=e.detail.column,i=e.detail.value,r.multiIndex[a]=i,t.t0=a,t.next=0===t.t0?7:1===t.t0?14:2===t.t0?18:19;break;case 7:return t.next=9,r.getAreasAsync(r.pickerValueArray[0][r.multiIndex[a]].value);case 9:return r.cityArr=t.sent,t.next=12,r.getAreasAsync(r.cityArr[0].value);case 12:return r.districtArr=t.sent,t.abrupt("break",19);case 14:return t.next=16,r.getAreasAsync(r.cityArr[r.multiIndex[a]].value);case 16:return r.districtArr=t.sent,t.abrupt("break",19);case 18:return t.abrupt("break",19);case 19:case"end":return t.stop()}}),t)})))()},handleValueChange:function(e){var r=(0,i.default)(e.detail.value,3),t=r[0],a=r[1],n=r[2],d=(0,i.default)(this.pickedArr,3),s=d[0],o=d[1],l=d[2],u=[s[t],o[a],l[n]];this.$emit("getRegions",u)},handleDefaultRegions:function(){var e=this,r=setInterval((function(){if(e.isLoadDefaultAreas){e.isInitMultiArray=!1;for(var t=0;t<e.defaultRegions.length;t++)for(var a=function(r){e.defaultRegions[t]==e.pickerValueArray[t][r].value&&1==e.pickerValueArray[t][r].level&&(e.$set(e.multiIndex,t,r),e.getAreas(e.pickerValueArray[t][r].value,(function(t){e.cityArr=t;for(var a=function(t){if(e.defaultRegions[1]==e.cityArr[t].value)return e.$set(e.multiIndex,1,t),e.getAreas(e.cityArr[t].value,(function(a){e.districtArr=a;for(var i=0;i<e.districtArr.length;i++)if(e.defaultRegions[2]==e.districtArr[i].value){e.$set(e.multiIndex,2,i),e.handleValueChange({detail:{value:[r,t,i]}});break}})),"break"},i=0;i<e.cityArr.length;i++){var n=a(i);if("break"===n)break}})))},i=0;i<e.pickerValueArray[t].length;i++)a(i);e.isLoadDefaultAreas&&clearInterval(r)}}),100)},getDefaultAreas:function(e,r){var t=this;this.$api.sendRequest({url:"/shopapi/address/lists",data:{pid:e},success:function(e){if(0==e.code){var a=[],i=void 0;e.data.forEach((function(e,t){void 0!=r&&(0==r.level&&void 0!=r.province_id?i=r.province_id:1==r.level&&void 0!=r.city_id?i=r.city_id:2==r.level&&void 0!=r.district_id&&(i=r.district_id)),void 0==i&&0==t&&(i=e.id),a.push({value:e.id,label:e.name,level:e.level})})),t.pickerValueArray[r.level]=a,r.level+1<3?(r.level++,t.getDefaultAreas(i,r)):(t.isInitMultiArray=!0,t.isLoadDefaultAreas=!0)}}})},getAreasAsync:function(e){var r=this;return(0,d.default)((0,n.default)().mark((function t(){var a,i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,r.$api.sendRequest({url:"/shopapi/address/lists",data:{pid:e},async:!1});case 2:if(a=t.sent,0!=a.code){t.next=7;break}return i=[],a.data.forEach((function(e,r){i.push({value:e.id,label:e.name,level:e.level})})),t.abrupt("return",i);case 7:case"end":return t.stop()}}),t)})))()},getAreas:function(e,r){this.$api.sendRequest({url:"/shopapi/address/lists",data:{pid:e},success:function(e){if(0==e.code){var t=[];e.data.forEach((function(e,r){t.push({value:e.id,label:e.name,level:e.level})})),r&&r(t)}}})}}};r.default=s},"5f83":function(e,r,t){"use strict";t.r(r);var a=t("5f81"),i=t.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return a[e]}))}(n);r["default"]=i.a},"61e2":function(e,r,t){t("23f4"),t("7d2f"),t("5c47"),t("9c4e"),t("ab80"),t("0506"),t("64aa"),t("5ef2"),e.exports={error:"",check:function(e,r){for(var t=0;t<r.length;t++){if(!r[t].checkType)return!0;if(!r[t].name)return!0;if(!r[t].errorMsg)return!0;if(!e[r[t].name])return this.error=r[t].errorMsg,!1;switch(r[t].checkType){case"custom":if("function"==typeof r[t].validate&&!r[t].validate(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"required":var a=new RegExp("/[S]+/");if(a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"string":a=new RegExp("^.{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"digit":a=new RegExp("^(d{0,10}(.?d{0,2}){"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"between":if(!this.isNumber(e[r[t].name]))return this.error=r[t].errorMsg,!1;var i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;i=r[t].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[r[t].name]>i[1]||e[r[t].name]<i[0])return this.error=r[t].errorMsg,!1;break;case"same":if(e[r[t].name]!=r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"notsame":if(e[r[t].name]==r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"email":a=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"phoneno":a=/^\d{11}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"reg":a=new RegExp(r[t].checkRule);if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"in":if(-1==r[t].checkRule.indexOf(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"notnull":if(0==e[r[t].name]||void 0==e[r[t].name]||null==e[r[t].name]||e[r[t].name].length<1)return this.error=r[t].errorMsg,!1;break;case"lengthMin":if(e[r[t].name].length<r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"lengthMax":if(e[r[t].name].length>r[t].checkRule)return this.error=r[t].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},6243:function(e,r,t){"use strict";t.r(r);var a=t("f6d4"),i=t.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return a[e]}))}(n);r["default"]=i.a},6638:function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.adjustOrderPrice=function(e){return i.default.post("/shopapi/order/adjustPrice",{data:e})},r.closeOrder=function(e){return i.default.post("/shopapi/order/close",{data:{order_id:e}})},r.deliveryOrder=function(e){return i.default.post("/shopapi/order/delivery",{data:e})},r.editOrderDelivery=function(e){return i.default.post("/shopapi/order/editOrderDelivery",{data:e})},r.editOrderInvoicelist=function(e){return i.default.post("/shopapi/order/invoiceEdit",{data:e})},r.getOrderCondition=function(){return i.default.get("/shopapi/order/condition")},r.getOrderDetailById=function(e){return i.default.post("/shopapi/order/getOrderDetail",{data:{order_id:e}})},r.getOrderDetailInfoById=function(e){return i.default.post("/shopapi/order/detail",{data:{order_id:e}})},r.getOrderGoodsList=function(e){return i.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:e}})},r.getOrderInfoById=function(e){return i.default.post("/shopapi/order/getOrderInfo",{data:{order_id:e}})},r.getOrderInvoicelist=function(e){return i.default.post("/shopapi/order/invoicelist",{data:e})},r.getOrderList=function(e){return i.default.post("/shopapi/order/lists",{data:e})},r.getOrderLog=function(e){return i.default.post("/shopapi/order/log",{data:{order_id:e}})},r.getOrderPackageList=function(e){return i.default.post("/shopapi/order/package",{data:{order_id:e}})},r.ordErtakeDelivery=function(e){return i.default.post("/shopapi/order/takeDelivery",{data:{order_id:e}})},r.orderExtendTakeDelivery=function(e){return i.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:e}})},r.orderLocalorderDelivery=function(e){return i.default.post("/shopapi/localorder/delivery",{data:e})},r.orderOfflinePay=function(e){return i.default.post("/shopapi/order/offlinePay",{data:{order_id:e}})},r.orderVirtualDelivery=function(e){return i.default.post("/shopapi/virtualorder/delivery",{data:{order_id:e}})},r.storeOrderTakeDelivery=function(e){return i.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:e}})};var i=a(t("9027"))},"6f94":function(e,r,t){"use strict";t.d(r,"b",(function(){return a})),t.d(r,"c",(function(){return i})),t.d(r,"a",(function(){}));var a=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("v-uni-view",{staticClass:"pick-regions"},[t("v-uni-picker",{attrs:{mode:"multiSelector",value:e.multiIndex,range:e.multiArray},on:{change:function(r){arguments[0]=r=e.$handleEvent(r),e.handleValueChange.apply(void 0,arguments)},columnchange:function(r){arguments[0]=r=e.$handleEvent(r),e.handleColumnChange.apply(void 0,arguments)}}},[e._t("default")],2)],1)},i=[]},"7b5f":function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.editAddress=function(e){return i.default.post("/shopapi/order/editAddress",{data:e})},r.getAddressInfo=function(e){return i.default.post("/api/memberaddress/tranAddressInfo",{data:e})};var i=a(t("9027"))},"7d82":function(e,r,t){"use strict";t.d(r,"b",(function(){return i})),t.d(r,"c",(function(){return n})),t.d(r,"a",(function(){return a}));var a={pickRegions:t("095e").default,loadingCover:t("59c1").default},i=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("v-uni-view",[t("v-uni-view",{staticClass:"item-wrap"},[t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-view",{staticClass:"label"},[t("v-uni-text",{staticClass:"required color-base-text"},[e._v("*")]),t("v-uni-text",[e._v("收货人")])],1),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请填写收货联系人"},model:{value:e.order.name,callback:function(r){e.$set(e.order,"name",r)},expression:"order.name"}})],1),t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-view",{staticClass:"label"},[t("v-uni-text",{staticClass:"required color-base-text"},[e._v("*")]),t("v-uni-text",[e._v("手机号码")])],1),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请填写手机号码"},model:{value:e.order.mobile,callback:function(r){e.$set(e.order,"mobile",r)},expression:"order.mobile"}})],1),t("v-uni-view",{staticClass:"form-wrap more-wrap"},[t("v-uni-view",{staticClass:"label"},[t("v-uni-text",{staticClass:"required color-base-text"},[e._v("*")]),t("v-uni-text",[e._v("收货地址")])],1),3==e.order.order_type?t("v-uni-text",{staticClass:"selected",on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.selectAddress.apply(void 0,arguments)}}},[e._v(e._s(e.order.full_address?e.order.full_address:"请选择"))]):t("v-uni-view",{staticClass:"selected"},[t("pick-regions",{attrs:{"default-regions":e.defaultRegions},on:{getRegions:function(r){arguments[0]=r=e.$handleEvent(r),e.handleGetRegions.apply(void 0,arguments)}}},[t("v-uni-text",{staticClass:"select-address",class:{"color-tip":!e.order.full_address}},[e._v(e._s(e.order.full_address?e.order.full_address:"请选择省市区县"))])],1)],1),3==e.order.order_type?t("v-uni-text",{staticClass:"iconfont iconright",on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.selectAddress.apply(void 0,arguments)}}}):e._e()],1),t("v-uni-view",{staticClass:"form-wrap"},[t("v-uni-view",{staticClass:"label"},[t("v-uni-text",{staticClass:"required color-base-text"},[e._v("*")]),t("v-uni-text",[e._v("详细地址")])],1),t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入详细地址"},model:{value:e.order.address,callback:function(r){e.$set(e.order,"address",r)},expression:"order.address"}})],1)],1),t("v-uni-view",{staticClass:"footer-wrap"},[t("v-uni-button",{attrs:{type:"primary"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.save()}}},[e._v("确定")])],1),t("loading-cover",{ref:"loadingCover"})],1)},n=[]},f1a5:function(e,r,t){"use strict";t.r(r);var a=t("7d82"),i=t("6243");for(var n in i)["default"].indexOf(n)<0&&function(e){t.d(r,e,(function(){return i[e]}))}(n);t("05ca");var d=t("828b"),s=Object(d["a"])(i["default"],a["b"],a["c"],!1,null,"4c7bb94c",null,!1,a["a"],void 0);r["default"]=s.exports},f6d4:function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("d4b5"),t("0c26");var i=a(t("61e2")),n=a(t("412b")),d=t("6638"),s=t("7b5f"),o={data:function(){return{orderId:0,order:{},repeatFlag:!1,defaultRegions:[],formData:{},addressValue:"",option:{}}},onLoad:function(e){this.orderId=e.order_id||0,e.name&&(this.option=e),this.getOrderInfo()},onShow:function(){},methods:{getOrderInfo:function(){var e=this;(0,d.getOrderInfoById)(this.orderId).then((function(r){if(0==r.code){if(e.order=r.data,e.defaultRegions=[r.data.province_id,r.data.city_id,r.data.district_id],e.option.name){e.order.address=e.option.name,uni.getStorageSync("addressInfo")&&(e.order=JSON.parse(uni.getStorageSync("addressInfo"))),e.order.address=e.option.name,e.getAddress(e.option.latng);var t=e.option.latng.split(",");e.order.latitude=t[0],e.order.longitude=t[1]}e.$refs.loadingCover&&setTimeout((function(){e.$refs.loadingCover.hide()}),100)}else e.$util.showToast({title:r.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)}))},handleGetRegions:function(e){this.order.full_address="",this.order.full_address+=void 0!=e[0]?e[0].label:"",this.order.full_address+=void 0!=e[1]?"-"+e[1].label:"",this.order.full_address+=void 0!=e[2]?"-"+e[2].label:"",this.addressValue="",this.addressValue+=void 0!=e[0]?e[0].value:"",this.addressValue+=void 0!=e[1]?"-"+e[1].value:"",this.addressValue+=void 0!=e[2]?"-"+e[2].value:""},getAddress:function(e){var r=this;(0,s.getAddressInfo)({latlng:e}.then((function(e){0==e.code?(r.order.full_address="",r.order.full_address+=void 0!=e.data.province?e.data.province:"",r.order.full_address+=void 0!=e.data.city?"-"+e.data.city:"",r.order.full_address+=void 0!=e.data.district?"-"+e.data.district:"",r.addressValue="",r.addressValue+=void 0!=e.data.province_id?e.data.province_id:"",r.addressValue+=void 0!=e.data.city_id?"-"+e.data.city_id:"",r.addressValue+=void 0!=e.data.district_id?"-"+e.data.district_id:""):r.$util.showToast({title:"数据有误"})})))},selectAddress:function(){var e=this.order;uni.setStorageSync("addressInfo",JSON.stringify(e));var r=n.default.h5Domain+"/pages/order/address_update?order_id="+this.order.order_id;window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(r)+"&key="+n.default.mpKey+"&referer=myapp"},save:function(){var e=this;if(!this.repeatFlag)if(this.repeatFlag=!0,this.verify()){var r=this.addressValue.split("-"),t={order_id:this.orderId,name:this.order.name,mobile:this.order.mobile,telephone:this.order.telephone,province_id:r[0],city_id:r[1],district_id:r[2],address:this.order.address,full_address:this.order.full_address};3==this.order.order_type&&(t.latitude=this.order.latitude,t.longitude=this.order.longitude),(0,s.editAddress)(t).then((function(r){e.repeatFlag=!1,0==r.code&&(e.$util.showToast({title:r.message}),uni.removeStorageSync("addressInfo"),setTimeout((function(){uni.navigateBack({delta:1})}),1e3))}))}else this.repeatFlag=!1},verify:function(){this.order.name=this.order.name.trim(),this.order.mobile=this.order.mobile.trim(),this.order.address=this.order.address.trim();var e=i.default.check(this.order,[{name:"name",checkType:"required",errorMsg:"请输入姓名"},{name:"mobile",checkType:"required",errorMsg:"请输入手机号"},{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"full_address",checkType:"required",errorMsg:"请选择省市区县"},{name:"address",checkType:"required",errorMsg:"详细地址不能为空"}]);return!!e||(this.$util.showToast({title:i.default.error}),this.flag=!1,!1)}}};r.default=o}}]);