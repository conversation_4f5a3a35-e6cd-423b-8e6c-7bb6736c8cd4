(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-store-settlement"],{2445:function(t,e,a){"use strict";a.r(e);var i=a("4f6f"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"4f6f":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a("d059"),n={data:function(){var t=this;return{bankType:{1:"微信",2:"支付宝",3:"银行卡"},withdrawConfig:{is_settlement:0},cols:[{width:12,title:"结算方式",field:"transfer_type_name",align:"left"},{width:12,title:"结算类型",field:"settlement_type_name",align:"left"},{width:12,title:"结算金额",align:"left",return:function(e){return t.$util.moneyFormat(e.money)}},{width:12,title:"结算状态",field:"status_name"},{width:15,title:"申请时间",align:"center",return:function(e){return e.apply_time?t.$util.timeFormat(e.apply_time):""}},{width:15,title:"转账时间",align:"center",return:function(e){return e.transfer_time?t.$util.timeFormat(e.transfer_time):""}},{width:20,title:"操作",action:!0,align:"right"}],isRepeat:!1,withdrawDetail:null}},onLoad:function(){},onShow:function(){this.getWithdrawConfigFn(),this.$store.dispatch("app/getStoreInfoFn")},methods:{switchStoreAfter:function(){this.$refs.table.load()},getWithdrawConfigFn:function(){var t=this;(0,i.getWithdrawConfig)().then((function(e){0==e.code&&(t.withdrawConfig=e.data)}))},applyWithdraw:function(){this.$refs.applyWithdraw.open()},apply:function(){var t=this;this.isRepeat||(this.isRepeat=!0,(0,i.applyWithdraw)(this.globalStoreInfo.account).then((function(e){0==e.code?(t.$store.dispatch("app/getStoreInfoFn"),t.$refs.applyWithdraw.close(),t.$refs.table.load(),setTimeout((function(){t.isRepeat=!1}),500)):(t.isRepeat=!1,t.$util.showToast({title:e.message}))})))},detail:function(t){var e=this;(0,i.withdrawDetail)(t.value.withdraw_id).then((function(t){0==t.code&&(e.withdrawDetail=t.data,e.$refs.detailPopup.open("center"))}))}}};e.default=n},"655a":function(t,e,a){"use strict";a.r(e);var i=a("e1af"),n=a("2445");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("d173");var r=a("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"65bf7f68",null,!1,i["a"],void 0);e["default"]=s.exports},"9a0a":function(t,e,a){var i=a("a1ab");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("4864a1a6",i,!0,{sourceMap:!1,shadowMode:!1})},a1ab:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-65bf7f68]{display:none}\r\n/* 收银台相关 */uni-text[data-v-65bf7f68],\r\nuni-view[data-v-65bf7f68]{font-size:.14rem}body[data-v-65bf7f68]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-65bf7f68]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-65bf7f68]::-webkit-scrollbar-button{display:none}body[data-v-65bf7f68]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-65bf7f68]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-65bf7f68]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-65bf7f68]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-65bf7f68]{color:var(--primary-color)!important}.page-height[data-v-65bf7f68]{height:100%}.account-wrap[data-v-65bf7f68]{flex:1;width:0;padding:.15rem;margin-right:.15rem;background:#fff}.account-wrap .head-title[data-v-65bf7f68]{font-size:.16rem}.account-wrap .account-item[data-v-65bf7f68]{padding:.2rem 0;cursor:pointer}.account-wrap .account-item .account-title[data-v-65bf7f68]{font-size:.14rem;color:#aaa}.account-wrap .account-item .money[data-v-65bf7f68]{font-size:.2rem;font-weight:700;margin-top:.15rem;display:flex;align-items:center}.account-wrap .account-item .money .iconqianhou2[data-v-65bf7f68]{line-height:1;color:#bbb;margin-left:.05rem}.account-wrap .account-item .action[data-v-65bf7f68]{margin-top:.3rem;height:.4rem}.account-wrap .account-item .action uni-button[data-v-65bf7f68]{display:inline-block;margin-right:.1rem;width:auto;min-width:.8rem;line-height:.4rem;height:.4rem}.account-wrap .sub-bottom[data-v-65bf7f68]{border-top:.01rem solid #f5f5f5}.account-wrap .sub-bottom .account-item[data-v-65bf7f68]{flex:1}.info-wrap[data-v-65bf7f68]{width:30vw;padding:.15rem;background:#fff}.info-wrap .head-title[data-v-65bf7f68]{font-size:.16rem}.info-wrap .info-content[data-v-65bf7f68]{margin-top:.2rem}.info-wrap .info-content .info-text[data-v-65bf7f68]{margin-bottom:.1rem}.info-wrap .empty[data-v-65bf7f68]{padding:1rem;text-align:center}.settlement-record[data-v-65bf7f68]{padding:.15rem;flex:1;height:0;margin:.15rem 0;background:#fff}.settlement-record .head-title[data-v-65bf7f68]{font-size:.16rem}.settlement-record .record-wrap[data-v-65bf7f68]{padding-top:.15rem;flex:1;height:0;overflow-y:scroll}.pop-box[data-v-65bf7f68]{background:#fff;width:5rem;height:60vh;display:flex;flex-direction:column}.pop-box .pop-header[data-v-65bf7f68]{width:100%;padding:0 .15rem 0 .2rem;height:.5rem;margin:0 auto;line-height:.5rem;border-bottom:.01rem solid #f0f0f0;font-size:.14rem;color:#333;overflow:hidden;border-radius:.02rem .2rem 0 0;box-sizing:border-box;display:flex;justify-content:space-between}.pop-box .pop-header .pop-header-close[data-v-65bf7f68]{cursor:pointer}.pop-box .pop-header .pop-header-close uni-text[data-v-65bf7f68]{font-size:.18rem}.pop-box .pop-content[data-v-65bf7f68]{flex:1;height:0;padding:.1rem .2rem;box-sizing:border-box;font-weight:900;overflow-y:scroll}.pop-box .pop-contents[data-v-65bf7f68]{margin-top:.3rem;width:3rem;height:.8rem;padding:.1rem .2rem;box-sizing:border-box;font-weight:900;display:flex;flex-direction:column;flex-wrap:wrap;justify-content:space-between}.pop-box .pop-content-item[data-v-65bf7f68]{margin-left:.3rem}.pop-box .pop-content-items[data-v-65bf7f68]{margin-left:.3rem}.pop-box .pop-content-text[data-v-65bf7f68]{padding:.1rem}.pop-box .pop-contents-text[data-v-65bf7f68]{margin-left:.4rem;font-weight:400;padding:.1rem}.apply-withdraw[data-v-65bf7f68]{width:3.8rem;border-radius:.06rem;background:#fff;padding:.6rem .15rem .2rem .15rem;box-sizing:border-box}.apply-withdraw .title[data-v-65bf7f68]{font-size:.16rem;text-align:center}.apply-withdraw .money[data-v-65bf7f68]{font-weight:700;font-size:.16rem}.apply-withdraw .btn[data-v-65bf7f68]{width:100%;display:flex;align-items:center;justify-content:center;margin-top:.3rem}.apply-withdraw .btn .btn[data-v-65bf7f68]{width:auto;padding:0 .3rem;margin:0;height:.35rem}.apply-withdraw .btn .btn[data-v-65bf7f68]:last-child{margin-left:.2rem}.voucher-img[data-v-65bf7f68]{width:1.5rem}',""]),t.exports=e},d059:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.accountExport=function(t){return n.default.post("/store/storeapi/account/export",{data:t})},e.applyWithdraw=function(t){return n.default.post("/store/storeapi/withdraw/apply",{data:{money:t}})},e.getAccountScreen=function(){return n.default.post("/store/storeapi/account/screen")},e.getWithdrawConfig=function(){return n.default.post("/store/storeapi/store/withdrawconfig")},e.getWithdrawPage=function(t){return n.default.post("/store/storeapi/withdraw/page",{data:t})},e.getWithdrawScreen=function(){return n.default.post("/store/storeapi/withdraw/screen")},e.withdrawDetail=function(t){return n.default.post("/store/storeapi/withdraw/detail",{data:{withdraw_id:t}})};var n=i(a("a3b5"))},d173:function(t,e,a){"use strict";var i=a("9a0a"),n=a.n(i);n.a},e1af:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uniDataTable:a("01ce").default,uniPopup:a("cea0").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"page-height uni-flex uni-column"},[a("v-uni-view",{staticClass:"uni-flex uni-row"},[a("v-uni-view",{staticClass:"account-wrap"},[a("v-uni-view",{staticClass:"head-title"},[t._v("账户概览")]),a("v-uni-view",{staticClass:"account-item"},[a("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/store/acccount_record")}}},[a("v-uni-view",{staticClass:"account-title"},[t._v("待结算金额（元）")]),a("v-uni-view",{staticClass:"money"},[t._v(t._s(t._f("moneyFormat")(t.globalStoreInfo.account))),a("v-uni-text",{staticClass:"iconfont iconqianhou2"})],1)],1),a("v-uni-view",{staticClass:"action"},[1==t.globalStoreInfo.is_settlement&&1==t.withdrawConfig.is_settlement&&4==t.withdrawConfig.period_type&&t.globalStoreInfo.account>0&&parseFloat(t.globalStoreInfo.account)>=parseFloat(t.withdrawConfig.withdraw_least)?a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.applyWithdraw.apply(void 0,arguments)}}},[t._v("申请结算")]):t._e()],1)],1),a("v-uni-view",{staticClass:"uni-flex uni-row sub-bottom"},[a("v-uni-view",{staticClass:"account-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/store/settlement_record")}}},[a("v-uni-view",{staticClass:"account-title"},[t._v("打款中金额（元）")]),a("v-uni-view",{staticClass:"money"},[t._v(t._s(t._f("moneyFormat")(t.globalStoreInfo.account_apply))),a("v-uni-text",{staticClass:"iconfont iconqianhou2"})],1)],1),a("v-uni-view",{staticClass:"account-item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/store/settlement_record")}}},[a("v-uni-view",{staticClass:"account-title"},[t._v("已打款金额（元）")]),a("v-uni-view",{staticClass:"money"},[t._v(t._s(t._f("moneyFormat")(t.globalStoreInfo.account_withdraw))),a("v-uni-text",{staticClass:"iconfont iconqianhou2"})],1)],1)],1)],1),a("v-uni-view",{staticClass:"info-wrap"},[a("v-uni-view",{staticClass:"head-title"},[t._v("结算账户")]),a("v-uni-view",{staticClass:"info-content"},[1==t.globalStoreInfo.is_settlement&&1==t.withdrawConfig.is_settlement?[a("v-uni-view",{staticClass:"info-text"},[t._v("结算方式："),a("v-uni-text",[t._v("总部收款，")]),1==t.withdrawConfig.period_type?a("v-uni-text",[t._v("每日自动结算")]):t._e(),2==t.withdrawConfig.period_type?a("v-uni-text",[t._v("每周自动结算")]):t._e(),3==t.withdrawConfig.period_type?a("v-uni-text",[t._v("每月自动结算")]):t._e(),4==t.withdrawConfig.period_type?a("v-uni-text",[t._v("门店申请结算")]):t._e()],1),a("v-uni-view",{staticClass:"info-text"},[t._v("账户类型："+t._s(t.bankType[t.globalStoreInfo.bank_type]))]),1==t.globalStoreInfo.bank_type?[a("v-uni-view",{staticClass:"info-text"},[t._v("微信名："+t._s(t.globalStoreInfo.bank_user_name))])]:t._e(),2==t.globalStoreInfo.bank_type?[a("v-uni-view",{staticClass:"info-text"},[t._v("真实姓名："+t._s(t.globalStoreInfo.bank_user_name))]),a("v-uni-view",{staticClass:"info-text"},[t._v("支付宝账号："+t._s(t.globalStoreInfo.bank_type_account))])]:t._e(),3==t.globalStoreInfo.bank_type?[a("v-uni-view",{staticClass:"info-text"},[t._v("开户行："+t._s(t.globalStoreInfo.bank_type_name))]),a("v-uni-view",{staticClass:"info-text"},[t._v("户头："+t._s(t.globalStoreInfo.bank_user_name))]),a("v-uni-view",{staticClass:"info-text"},[t._v("账户："+t._s(t.globalStoreInfo.bank_type_account))])]:t._e()]:a("v-uni-view",{staticClass:"empty"},[t._v("无需结算")])],2)],1)],1),a("v-uni-view",{staticClass:"settlement-record uni-flex uni-column"},[a("v-uni-view",{staticClass:"head-title"},[t._v("结算记录")]),a("v-uni-view",{staticClass:"record-wrap common-scrollbar"},[a("uni-data-table",{ref:"table",attrs:{url:"/store/storeapi/withdraw/page",cols:t.cols,pagesize:8},scopedSlots:t._u([{key:"action",fn:function(e){return[a("v-uni-view",{staticClass:"common-table-action"},[a("v-uni-text",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.detail(e)}}},[t._v("查看详情")])],1)]}}])})],1)],1)],1),a("uni-popup",{ref:"applyWithdraw",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"apply-withdraw"},[a("v-uni-view",{staticClass:"title"},[t._v("本次可结算金额为"),t.globalStoreInfo?a("v-uni-text",{staticClass:"money"},[t._v(t._s(t._f("moneyFormat")(t.globalStoreInfo.account)))]):t._e(),t._v("元，是否申请结算？")],1),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.apply.apply(void 0,arguments)}}},[t._v("确定")]),a("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.applyWithdraw.close()}}},[t._v("取消")])],1)],1)],1),a("uni-popup",{ref:"detailPopup"},[a("v-uni-view",{staticClass:"pop-box"},[a("v-uni-view",{staticClass:"pop-header"},[a("v-uni-view",{},[t._v("结算详情")]),a("v-uni-view",{staticClass:"pop-header-close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.detailPopup.close()}}},[a("v-uni-text",{staticClass:"iconguanbi1 iconfont"})],1)],1),t.withdrawDetail?a("v-uni-view",{staticClass:"pop-content common-scrollbar"},[a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("结算信息")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算编号："+t._s(t.withdrawDetail.withdraw_no))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算状态："+t._s(t.withdrawDetail.status_name))]),-1==t.withdrawDetail.status||-2==t.withdrawDetail.status?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("拒绝理由："+t._s(t.withdrawDetail.refuse_reason))]):t._e(),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算金额："+t._s(t._f("moneyFormat")(t.withdrawDetail.money)))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算方式："+t._s(t.withdrawDetail.transfer_type_name))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算类型："+t._s(t.withdrawDetail.settlement_type_name))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算申请时间："+t._s(t._f("timeFormat")(t.withdrawDetail.apply_time)))]),"bank"==t.withdrawDetail.transfer_type?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("银行名称："+t._s(t.withdrawDetail.bank_name))]):t._e(),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算收款账号："+t._s(t.withdrawDetail.account_number))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算方式："+t._s(t.withdrawDetail.transfer_type_name))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("真实姓名："+t._s(t.withdrawDetail.realname))]),t.withdrawDetail.voucher_img?a("v-uni-view",{staticClass:"pop-contents-text flex"},[t._v("转账凭证："),a("v-uni-image",{staticClass:"voucher-img",attrs:{src:t.$util.img(t.withdrawDetail.voucher_img)}})],1):t._e(),t.withdrawDetail.voucher_desc?a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("凭证说明："+t._s(t.withdrawDetail.voucher_desc))]):t._e()],1),"apply"!=t.withdrawDetail.settlement_type?a("v-uni-view",{staticClass:"pop-content-item"},[a("v-uni-view",{staticClass:"pop-content-text"},[t._v("周期结算")]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("周期结算编号："+t._s(t.withdrawDetail.settlement_info.settlement_no))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("周期开始时间："+t._s(t.withdrawDetail.settlement_info.start_time))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("周期结束时间："+t._s(t.withdrawDetail.settlement_info.end_time))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算订单总额："+t._s(t.withdrawDetail.settlement_info.order_money))]),a("v-uni-view",{staticClass:"pop-contents-text"},[t._v("结算总分销佣金："+t._s(t.withdrawDetail.settlement_info.commission))])],1):t._e()],1):t._e()],1)],1)],1)},o=[]}}]);