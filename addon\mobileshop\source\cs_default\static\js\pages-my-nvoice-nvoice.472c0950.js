(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-nvoice-nvoice"],{"06f5":function(t,i,n){"use strict";n.d(i,"b",(function(){return e})),n.d(i,"c",(function(){return o})),n.d(i,"a",(function(){}));var e=function(){var t=this,i=t.$createElement,n=t._self._c||i;return n("v-uni-view",{staticClass:"nvoice"},[n("v-uni-form",{on:{submit:function(i){arguments[0]=i=t.$handleEvent(i),t.formSubmit.apply(void 0,arguments)}}},[t._l(t.list,(function(i,e){return n("v-uni-view",{key:e,staticClass:"nvoice-input"},[n("v-uni-input",{attrs:{type:"text",name:"aa",placeholder:"请填写发票内容",value:i},on:{blur:function(i){arguments[0]=i=t.$handleEvent(i),t.onBlur(i,e)}}}),n("v-uni-text",{staticClass:"action iconfont iconjian",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.deleteSpecValue(i,e)}}})],1)})),n("v-uni-view",{staticClass:"add-nvoice",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.addContent.apply(void 0,arguments)}}},[t._v("+添加内容")]),n("v-uni-view",{staticClass:"footer-wrap"},[n("v-uni-button",{attrs:{type:"primary","form-type":"submit"}},[t._v("保存")])],1)],2)],1)},o=[]},"353f":function(t,i,n){var e=n("c60d");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var o=n("967d").default;o("68a2ea92",e,!0,{sourceMap:!1,shadowMode:!1})},"7c4c":function(t,i,n){"use strict";n("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,n("aa9c"),n("dd2b");var e={data:function(){return{list:[]}},onLoad:function(t){JSON.parse(t.list);this.list=JSON.parse(t.list),""!=this.list[0]&&this.list.length||this.list.push("")},methods:{onBlur:function(t,i){this.list[i]=t.detail.value},addContent:function(){for(var t=0;t<this.list.length;t++)if(""==this.list[t])return void this.$util.showToast({title:"请填写后再添加"});this.list.push("")},deleteSpecValue:function(t){var i=this;uni.showModal({title:"操作提示",content:"确定要删除此发票内容吗？",success:function(n){if(n.confirm)for(var e=0;e<i.list.length;e++)t==i.list[e]&&i.list.splice(e,1)}})},formSubmit:function(t){uni.setStorageSync("invoicecontent",this.list),uni.navigateBack({delta:1})}}};i.default=e},"810f":function(t,i,n){"use strict";n.r(i);var e=n("06f5"),o=n("8541");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(i,t,(function(){return o[t]}))}(a);n("d3f9");var c=n("828b"),s=Object(c["a"])(o["default"],e["b"],e["c"],!1,null,"5425375f",null,!1,e["a"],void 0);i["default"]=s.exports},8541:function(t,i,n){"use strict";n.r(i);var e=n("7c4c"),o=n.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){n.d(i,t,(function(){return e[t]}))}(a);i["default"]=o.a},c60d:function(t,i,n){var e=n("c86c");i=e(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-5425375f]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-5425375f]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-5425375f]{position:fixed;left:0;right:0;z-index:998}.nvoice[data-v-5425375f]{padding:0 %?30?%}.nvoice .nvoice-input[data-v-5425375f]{margin:%?20?% 0;padding:%?30?%;background:#fff;border-radius:%?10?%;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.nvoice .nvoice-input .action[data-v-5425375f]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700}.nvoice .nvoice-input uni-input[data-v-5425375f]{width:100%;font-size:%?30?%;font-family:PingFang SC;font-weight:500;color:#909399}.nvoice .add-nvoice[data-v-5425375f]{padding:%?20?%;background:#fff;border-radius:%?10?%;font-size:%?30?%;font-family:PingFang SC;font-weight:500;color:#ff6a00;text-align:center}.nvoice .footer-wrap[data-v-5425375f]{width:%?690?%;position:fixed;left:%?30?%;bottom:%?60?%}',""]),t.exports=i},d3f9:function(t,i,n){"use strict";var e=n("353f"),o=n.n(e);o.a}}]);