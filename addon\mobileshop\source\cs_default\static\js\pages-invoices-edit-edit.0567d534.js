(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-invoices-edit-edit"],{3429:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i("6638"),r={data:function(){return{invoiceCode:"",invoiceRemark:"",arr:["未开票","已开票"],index:0,order_id:"",detailData:{}}},onLoad:function(e){this.order_id=e.order_id,this.ondetail()},methods:{ondetail:function(){var e=this;(0,a.getOrderDetailById)(this.order_id).then((function(t){e.detailData=t.data,e.index=t.data.invoice_status,e.invoiceCode=t.data.invoice_code,e.invoiceRemark=t.data.invoice_remark}))},bindPickerChange:function(e){this.index=e.target.value},onBtn:function(){var e=this;(0,a.editOrderInvoicelist)({order_id:this.order_id,invoice_status:this.index,invoice_code:this.invoiceCode,invoice_remark:this.invoiceRemark}).then((function(t){var i=t.message;e.$util.showToast({title:i})}))}}};t.default=r},"455b":function(e,t,i){"use strict";i.r(t);var a=i("3429"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"589c":function(e,t,i){"use strict";var a=i("e5f3"),r=i.n(a);r.a},"60d3":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"edit"},[i("v-uni-view",{staticClass:"edit-box"},[i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("发票抬头")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(e.detailData.invoice_title))])],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("发票类型")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(e.detailData.order_type_name))])],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("纳税人识别号")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(e.detailData.invoice_title))])],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("发票抬头类型")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(1==e.detailData.invoice_title_type?"个人":"企业"))])],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("真实姓名")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(e.detailData.name))])],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("联系电话")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(e.detailData.mobile))])],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("邮箱地址")]),i("v-uni-view",{staticClass:"item-left"},[e._v(e._s(e.detailData.invoice_email))])],1),i("v-uni-picker",{attrs:{value:e.index,range:e.arr},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindPickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-text",{staticClass:"item-left"},[e._v("开票状态")]),i("v-uni-view",{staticClass:"item-left"},[i("v-uni-text",{staticClass:"selected color-title"},[e._v(e._s(e.arr[e.index]))]),i("v-uni-text",{staticClass:"iconfont iconright"})],1)],1)],1),1==e.index?[i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("发票编号")]),i("v-uni-input",{attrs:{type:"text",placeholder:"请输入发票编号","placeholder-class":"intext"},model:{value:e.invoiceCode,callback:function(t){e.invoiceCode=t},expression:"invoiceCode"}})],1),i("v-uni-view",{staticClass:"edit-item"},[i("v-uni-view",{staticClass:"item-left"},[e._v("发票备注")]),i("v-uni-input",{attrs:{type:"text",placeholder:"请输入备注","placeholder-class":"intext"},model:{value:e.invoiceRemark,callback:function(t){e.invoiceRemark=t},expression:"invoiceRemark"}})],1)]:e._e()],2),i("v-uni-view",{staticClass:"btn"},[i("v-uni-button",{attrs:{type:"default"}},[e._v("取消")]),i("v-uni-button",{staticClass:"btn-que",attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onBtn()}}},[e._v("确定")])],1)],1)},r=[]},6638:function(e,t,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.adjustOrderPrice=function(e){return r.default.post("/shopapi/order/adjustPrice",{data:e})},t.closeOrder=function(e){return r.default.post("/shopapi/order/close",{data:{order_id:e}})},t.deliveryOrder=function(e){return r.default.post("/shopapi/order/delivery",{data:e})},t.editOrderDelivery=function(e){return r.default.post("/shopapi/order/editOrderDelivery",{data:e})},t.editOrderInvoicelist=function(e){return r.default.post("/shopapi/order/invoiceEdit",{data:e})},t.getOrderCondition=function(){return r.default.get("/shopapi/order/condition")},t.getOrderDetailById=function(e){return r.default.post("/shopapi/order/getOrderDetail",{data:{order_id:e}})},t.getOrderDetailInfoById=function(e){return r.default.post("/shopapi/order/detail",{data:{order_id:e}})},t.getOrderGoodsList=function(e){return r.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:e}})},t.getOrderInfoById=function(e){return r.default.post("/shopapi/order/getOrderInfo",{data:{order_id:e}})},t.getOrderInvoicelist=function(e){return r.default.post("/shopapi/order/invoicelist",{data:e})},t.getOrderList=function(e){return r.default.post("/shopapi/order/lists",{data:e})},t.getOrderLog=function(e){return r.default.post("/shopapi/order/log",{data:{order_id:e}})},t.getOrderPackageList=function(e){return r.default.post("/shopapi/order/package",{data:{order_id:e}})},t.ordErtakeDelivery=function(e){return r.default.post("/shopapi/order/takeDelivery",{data:{order_id:e}})},t.orderExtendTakeDelivery=function(e){return r.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:e}})},t.orderLocalorderDelivery=function(e){return r.default.post("/shopapi/localorder/delivery",{data:e})},t.orderOfflinePay=function(e){return r.default.post("/shopapi/order/offlinePay",{data:{order_id:e}})},t.orderVirtualDelivery=function(e){return r.default.post("/shopapi/virtualorder/delivery",{data:{order_id:e}})},t.storeOrderTakeDelivery=function(e){return r.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:e}})};var r=a(i("9027"))},bcec:function(e,t,i){"use strict";i.r(t);var a=i("60d3"),r=i("455b");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("589c");var d=i("828b"),o=Object(d["a"])(r["default"],a["b"],a["c"],!1,null,"9aa31a6e",null,!1,a["a"],void 0);t["default"]=o.exports},c2ff:function(e,t,i){var a=i("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-9aa31a6e]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-9aa31a6e]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-9aa31a6e]{position:fixed;left:0;right:0;z-index:998}.edit[data-v-9aa31a6e]{padding:%?20?% 0 0}.edit-box[data-v-9aa31a6e]{background:#fff}.edit-item[data-v-9aa31a6e]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;border-bottom:1px solid #eee;background:#fff;padding:%?26?% 0;margin:0 %?30?%}.edit-item .item-left[data-v-9aa31a6e]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#303133}.edit-item .item-left .iconright[data-v-9aa31a6e]{display:inline-block;margin-left:%?10?%;color:#cacbce}.edit-item uni-input[data-v-9aa31a6e]{text-align:right;font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#303133}.edit-item .intext[data-v-9aa31a6e]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#909399}.edit-item[data-v-9aa31a6e]:last-child{border:none}.btn[data-v-9aa31a6e]{display:flex;padding:%?70?% 0}.btn uni-button[data-v-9aa31a6e]{width:%?330?%;height:%?80?%;border:1px solid #ccc;border-radius:%?40?%}.btn .btn-que[data-v-9aa31a6e]{width:%?330?%;height:%?80?%;background:#ff6a00;border-radius:%?40?%;border:none;color:#fff}',""]),e.exports=t},e5f3:function(e,t,i){var a=i("c2ff");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=i("967d").default;r("a67518e6",a,!0,{sourceMap:!1,shadowMode:!1})}}]);