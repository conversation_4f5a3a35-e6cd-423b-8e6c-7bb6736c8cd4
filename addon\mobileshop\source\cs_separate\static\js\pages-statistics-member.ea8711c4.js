(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-statistics-member"],{"4ab5":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(a("b03f")),n=i(a("8ae3")),d=i(a("5109")),l={components:{uCharts:r.default},mixins:[n.default,d.default]};e.default=l},"4c3e":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-089a0f4a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-089a0f4a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-089a0f4a]{position:fixed;left:0;right:0;z-index:998}.withdrawal[data-v-089a0f4a]{padding:1px 0}.withdrawal .withdrawal_item[data-v-089a0f4a]{margin:0 %?30?%}.withdrawal .withdrawal_item .withdrawal_title[data-v-089a0f4a]{display:flex;align-items:center;margin-top:%?40?%}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info[data-v-089a0f4a]{font-size:%?32?%;font-weight:700;flex:1}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info .total[data-v-089a0f4a]{color:#ff6a00}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info .line[data-v-089a0f4a]{display:inline-block;height:%?28?%;width:%?4?%;border-radius:%?4?%}.withdrawal .withdrawal_item .withdrawal_title .select[data-v-089a0f4a]{border:1px solid #ccc;height:%?46?%;line-height:%?46?%;border-radius:%?46?%;width:%?160?%;padding:0 %?20?%;text-align:center;font-size:%?24?%;margin-left:%?20?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.withdrawal .withdrawal_item .withdrawal_title .select uni-text[data-v-089a0f4a]{vertical-align:middle}.withdrawal .withdrawal_item .withdrawal_title .total[data-v-089a0f4a]{font-weight:bolder;font-size:%?32?%}.withdrawal .withdrawal_item .withdrawal_content[data-v-089a0f4a]{background-color:#fff;border-radius:%?10?%}.withdrawal .withdrawal_item .withdrawal_content.margin-top[data-v-089a0f4a]{margin-top:%?30?%!important}.withdrawal .withdrawal_item .withdrawal_content .flex_two[data-v-089a0f4a]{display:flex;flex-wrap:wrap}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item[data-v-089a0f4a]{padding:%?28?% %?30?%;width:calc(50% - %?60?% - 1px);border-bottom:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item[data-v-089a0f4a]:nth-child(2n + 1){border-right:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item.border_none[data-v-089a0f4a]{border-bottom:0}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item .num[data-v-089a0f4a]{font-size:%?30?%;font-weight:500}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item .tip[data-v-089a0f4a]{color:#909399;font-size:%?24?%}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item[data-v-089a0f4a]{padding:%?28?% %?30?%;flex:1;min-width:calc(100% / 3);box-sizing:border-box}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item.border-left[data-v-089a0f4a]{border-left:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item.border-right[data-v-089a0f4a]{border-right:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item .num[data-v-089a0f4a]{font-size:%?30?%;font-weight:500}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item .tip[data-v-089a0f4a]{color:#909399;font-size:%?24?%}.withdrawal .withdrawal_item .withdrawal_content .flex_two .overhidden[data-v-089a0f4a]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.withdrawal .withdrawal_item .withdrawal_content .charts[data-v-089a0f4a]{padding:%?30?%}.safe-area[data-v-089a0f4a]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?40?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?40?%)}.ranking-wrap[data-v-089a0f4a]{padding:%?20?% 0}.ranking-item[data-v-089a0f4a]{display:flex;align-items:center}.ranking-item .goods-name[data-v-089a0f4a]{padding:0 %?20?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1;width:0}.ranking-item .ranking[data-v-089a0f4a]{width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center;font-size:%?24?%}.ranking-item .sale[data-v-089a0f4a]{padding:0 %?20?%;font-weight:700}',""]),t.exports=e},5165:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={loadingCover:a("59c1").default},r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"withdrawal safe-area"},[a("v-uni-view",{staticClass:"withdrawal_item"},[a("v-uni-view",{staticClass:"withdrawal_title"},[a("v-uni-view",{staticClass:"withdrawal_title_info"},[a("v-uni-text",{staticClass:"line color-base-bg margin-right"}),a("v-uni-text",[t._v("会员数据")])],1),a("v-uni-picker",{attrs:{value:t.pickerCurr,range:t.picker,"range-key":"date_text"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.pickerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"select color-tip"},[t._v(t._s(t.picker[t.pickerCurr].date_text)),a("v-uni-text",{staticClass:"iconfont iconiconangledown"})],1)],1)],1),a("v-uni-view",{staticClass:"withdrawal_content margin-top"},[a("v-uni-view",{staticClass:"flex_two"},[a("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_count",title:"新增会员数",curr:t.pickerCurr})}}},[a("v-uni-view",{staticClass:"tip overhidden"},[t._v("新增会员数")]),a("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.member_count))])],1),a("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"order_member_count",title:"下单会员数",curr:t.pickerCurr})}}},[a("v-uni-view",{staticClass:"tip overhidden"},[t._v("下单会员数")]),a("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.order_member_count))])],1),a("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_recharge_member_count",title:"储值会员数",curr:t.pickerCurr})}}},[a("v-uni-view",{staticClass:"tip overhidden"},[t._v("储值会员数")]),a("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.member_recharge_member_count))])],1),a("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_level_count",title:"开卡会员数",curr:t.pickerCurr})}}},[a("v-uni-view",{staticClass:"tip overhidden"},[t._v("开卡会员数")]),a("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.member_level_count))])],1),a("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"coupon_member_count",title:"领券会员数",curr:t.pickerCurr})}}},[a("v-uni-view",{staticClass:"tip overhidden"},[t._v("领券会员数")]),a("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t.statTotal.coupon_member_count))])],1)],1)],1)],1),a("loading-cover",{ref:"loadingCover"})],1)},n=[]},7897:function(t,e,a){"use strict";a.r(e);var i=a("5165"),r=a("8b28");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);a("c64b");var d=a("828b"),l=Object(d["a"])(r["default"],i["b"],i["c"],!1,null,"089a0f4a",null,!1,i["a"],void 0);e["default"]=l.exports},"8ae3":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("e966");var i={data:function(){return{picker:[{date_type:0,date_text:"今日实时"},{date_type:-1,date_text:"昨日"},{date_type:1,date_text:"近7天"},{date_type:2,date_text:"近30天"}],pickerCurr:0,statTotal:{}}},onShow:function(){this.$util.checkToken("/pages/statistics/transaction")&&this.pickerChange({detail:{value:this.pickerCurr}})},methods:{getStatTotal:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.$api.sendRequest({url:"/shopapi/statistics/getstattotal",data:e,success:function(e){e.code>=0&&(t.statTotal=e.data),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},pickerChange:function(t){this.pickerCurr=t.detail.value;var e={};switch(this.picker[this.pickerCurr].date_type){case-1:e.start_time=this.getTime(-1).startTime,e.end_time=this.getTime(-1).endTime;break;case 1:e.start_time=this.getTime(-7).startTime,e.end_time=this.getTime(0).endTime;break;case 2:e.start_time=this.getTime(-30).startTime,e.end_time=this.getTime(0).endTime;break}this.getStatTotal(e)},getTime:function(t){var e=new Date(new Date((new Date).toLocaleDateString()));e.setDate(e.getDate()+t);var a=parseInt(new Date(e).getTime()/1e3),i=a+86399;return{startTime:a,endTime:i}}}};e.default=i},"8b28":function(t,e,a){"use strict";a.r(e);var i=a("4ab5"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a},c64b:function(t,e,a){"use strict";var i=a("d04f"),r=a.n(i);r.a},d04f:function(t,e,a){var i=a("4c3e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=a("967d").default;r("1e7a0b18",i,!0,{sourceMap:!1,shadowMode:!1})}}]);