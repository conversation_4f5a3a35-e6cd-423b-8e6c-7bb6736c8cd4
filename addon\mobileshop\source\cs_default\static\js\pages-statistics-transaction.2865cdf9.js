(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-statistics-transaction"],{"155a":function(t,e,i){"use strict";var a=i("3f28"),n=i.n(a);n.a},"3f28":function(t,e,i){var a=i("ad90");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("420f0827",a,!0,{sourceMap:!1,shadowMode:!1})},a446:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966");var a={data:function(){return{picker:[{date_type:0,date_text:"今日实时"},{date_type:-1,date_text:"昨日"},{date_type:1,date_text:"近7天"},{date_type:2,date_text:"近30天"}],pickerCurr:0,statTotal:{}}},onShow:function(){this.$util.checkToken("/pages/statistics/transaction")&&this.pickerChange({detail:{value:this.pickerCurr}})},methods:{getStatTotal:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.$api.sendRequest({url:"/shopapi/statistics/getstattotal",data:e,success:function(e){e.code>=0&&(t.statTotal=e.data),t.$refs.loadingCover&&t.$refs.loadingCover.hide()}})},pickerChange:function(t){this.pickerCurr=t.detail.value;var e={};switch(this.picker[this.pickerCurr].date_type){case-1:e.start_time=this.getTime(-1).startTime,e.end_time=this.getTime(-1).endTime;break;case 1:e.start_time=this.getTime(-7).startTime,e.end_time=this.getTime(0).endTime;break;case 2:e.start_time=this.getTime(-30).startTime,e.end_time=this.getTime(0).endTime;break}this.getStatTotal(e)},getTime:function(t){var e=new Date(new Date((new Date).toLocaleDateString()));e.setDate(e.getDate()+t);var i=parseInt(new Date(e).getTime()/1e3),a=i+86399;return{startTime:i,endTime:a}}}};e.default=a},ad56:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={loadingCover:i("59c1").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"withdrawal safe-area"},[i("v-uni-view",{staticClass:"withdrawal_item"},[i("v-uni-view",{staticClass:"withdrawal_title"},[i("v-uni-view",{staticClass:"withdrawal_title_info"},[i("v-uni-text",{staticClass:"line color-base-bg margin-right"}),i("v-uni-text",[t._v("交易概况")])],1),i("v-uni-picker",{attrs:{value:t.pickerCurr,range:t.picker,"range-key":"date_text"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.pickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"select color-tip"},[t._v(t._s(t.picker[t.pickerCurr].date_text)),i("v-uni-text",{staticClass:"iconfont iconiconangledown"})],1)],1)],1),i("v-uni-view",{staticClass:"withdrawal_content margin-top"},[i("v-uni-view",{staticClass:"flex_two"},[i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"expected_earnings_total_money",title:"预计收入",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("预计收入(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.expected_earnings_total_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"earnings_total_money",title:"营业收入",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("营业收入(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.earnings_total_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"expenditure_total_money",title:"营业支出",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("营业支出(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.expenditure_total_money)))])],1)],1)],1)],1),i("v-uni-view",{staticClass:"withdrawal_item"},[i("v-uni-view",{staticClass:"withdrawal_title"},[i("v-uni-view",{staticClass:"withdrawal_title_info"},[i("v-uni-text",{staticClass:"line color-base-bg margin-right"}),i("v-uni-text",[t._v("营业收入")])],1),i("v-uni-view",{staticClass:"total color-base-text"},[t._v("￥"+t._s(t._f("moneyFormat")(t.statTotal.earnings_total_money)))])],1),i("v-uni-view",{staticClass:"withdrawal_content margin-top"},[i("v-uni-view",{staticClass:"flex_two"},[i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"order_pay_money",title:"商城订单",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("商城订单(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.order_pay_money)))])],1),t.addonIsExit.cashier?i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"cashier_order_pay_money",title:"收银订单",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("收银订单(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.cashier_order_pay_money)))])],1):t._e(),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_recharge_total_money",title:"会员充值",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员充值(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.member_recharge_total_money)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_level_total_money",title:"会员卡订单",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员卡订单(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.member_level_total_money)))])],1),t.addonIsExit.giftcard?i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_giftcard_total_money",title:"礼品卡订单",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("礼品卡订单(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.member_giftcard_total_money)))])],1):t._e()],1)],1)],1),i("v-uni-view",{staticClass:"withdrawal_item"},[i("v-uni-view",{staticClass:"withdrawal_title"},[i("v-uni-view",{staticClass:"withdrawal_title_info"},[i("v-uni-text",{staticClass:"line color-base-bg margin-right"}),i("v-uni-text",[t._v("营业支出")])],1),i("v-uni-view",{staticClass:"total color-base-text"},[t._v("￥"+t._s(t._f("moneyFormat")(t.statTotal.expenditure_total_money)))])],1),i("v-uni-view",{staticClass:"withdrawal_content margin-top"},[i("v-uni-view",{staticClass:"flex_two"},[i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"refund_total",title:"订单退款",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("订单退款(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.refund_total)))])],1),i("v-uni-view",{staticClass:"flex_three-item overhidden",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/statistics/transaction_detail",{field:"member_withdraw_total_money",title:"会员提现",curr:t.pickerCurr})}}},[i("v-uni-view",{staticClass:"tip overhidden"},[t._v("会员提现(元)")]),i("v-uni-view",{staticClass:"num overhidden"},[t._v(t._s(t._f("moneyFormat")(t.statTotal.member_withdraw_total_money)))])],1)],1)],1)],1),i("loading-cover",{ref:"loadingCover"})],1)},r=[]},ad90:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-68cb2331]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-68cb2331]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-68cb2331]{position:fixed;left:0;right:0;z-index:998}.withdrawal[data-v-68cb2331]{padding:1px 0}.withdrawal .withdrawal_item[data-v-68cb2331]{margin:0 %?30?%}.withdrawal .withdrawal_item .withdrawal_title[data-v-68cb2331]{display:flex;align-items:center;margin-top:%?40?%}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info[data-v-68cb2331]{font-size:%?32?%;font-weight:700;flex:1}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info .total[data-v-68cb2331]{color:#ff6a00}.withdrawal .withdrawal_item .withdrawal_title .withdrawal_title_info .line[data-v-68cb2331]{display:inline-block;height:%?28?%;width:%?4?%;border-radius:%?4?%}.withdrawal .withdrawal_item .withdrawal_title .select[data-v-68cb2331]{border:1px solid #ccc;height:%?46?%;line-height:%?46?%;border-radius:%?46?%;width:%?160?%;padding:0 %?20?%;text-align:center;font-size:%?24?%;margin-left:%?20?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.withdrawal .withdrawal_item .withdrawal_title .select uni-text[data-v-68cb2331]{vertical-align:middle}.withdrawal .withdrawal_item .withdrawal_title .total[data-v-68cb2331]{font-weight:bolder;font-size:%?32?%}.withdrawal .withdrawal_item .withdrawal_content[data-v-68cb2331]{background-color:#fff;border-radius:%?10?%}.withdrawal .withdrawal_item .withdrawal_content.margin-top[data-v-68cb2331]{margin-top:%?30?%!important}.withdrawal .withdrawal_item .withdrawal_content .flex_two[data-v-68cb2331]{display:flex;flex-wrap:wrap}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item[data-v-68cb2331]{padding:%?28?% %?30?%;width:calc(50% - %?60?% - 1px);border-bottom:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item[data-v-68cb2331]:nth-child(2n + 1){border-right:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item.border_none[data-v-68cb2331]{border-bottom:0}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item .num[data-v-68cb2331]{font-size:%?30?%;font-weight:500}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_two-item .tip[data-v-68cb2331]{color:#909399;font-size:%?24?%}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item[data-v-68cb2331]{padding:%?28?% %?30?%;flex:1;min-width:calc(100% / 3);box-sizing:border-box}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item.border-left[data-v-68cb2331]{border-left:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item.border-right[data-v-68cb2331]{border-right:1px solid #eee}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item .num[data-v-68cb2331]{font-size:%?30?%;font-weight:500}.withdrawal .withdrawal_item .withdrawal_content .flex_two .flex_three-item .tip[data-v-68cb2331]{color:#909399;font-size:%?24?%}.withdrawal .withdrawal_item .withdrawal_content .flex_two .overhidden[data-v-68cb2331]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.withdrawal .withdrawal_item .withdrawal_content .charts[data-v-68cb2331]{padding:%?30?%}.safe-area[data-v-68cb2331]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?40?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?40?%)}.ranking-wrap[data-v-68cb2331]{padding:%?20?% 0}.ranking-item[data-v-68cb2331]{display:flex;align-items:center}.ranking-item .goods-name[data-v-68cb2331]{padding:0 %?20?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1;width:0}.ranking-item .ranking[data-v-68cb2331]{width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center;font-size:%?24?%}.ranking-item .sale[data-v-68cb2331]{padding:0 %?20?%;font-weight:700}',""]),t.exports=e},c0a0:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("b03f")),r=a(i("a446")),l=a(i("5109")),d={components:{uCharts:n.default},mixins:[r.default,l.default]};e.default=d},d3ae:function(t,e,i){"use strict";i.r(e);var a=i("c0a0"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},d8ba:function(t,e,i){"use strict";i.r(e);var a=i("ad56"),n=i("d3ae");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("155a");var l=i("828b"),d=Object(l["a"])(n["default"],a["b"],a["c"],!1,null,"68cb2331",null,!1,a["a"],void 0);e["default"]=d.exports}}]);