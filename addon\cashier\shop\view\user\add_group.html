<style type="text/css">
    .tree-line {
        padding: 10px 0;
        background: #ededed;
        margin-bottom: 2px;
        line-height: 1.8;
        border: 1px solid #000;
    }

    .tree-line .layui-form {
        padding-left: 10px !important;
    }

    .tree-line .layui-form-checkbox {
        margin: 0 10px !important;
        vertical-align: middle;
    }

    .group-tree-block .layui-table tbody tr:hover {
        background-color: white;
    }
</style>

<div class="layui-form form-wrap" lay-filter="save">

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>角色名称：</label>
        <div class="layui-input-block">
            <input autocomplete="off" class="layui-input len-long" lay-verify="required" name="group_name" placeholder="请输入角色名称" required type="text">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">描述：</label>
        <div class="layui-input-block">
            <textarea class="layui-textarea len-long" maxlength="150" name="desc" placeholder="请输入角色的相关描述"></textarea>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>设置权限：</label>
        <div class="layui-input-block">
            <div class="layui-input-inline group-tree-block" id="tree_box"></div>
        </div>
    </div>

    <!-- 表单操作 -->
    <div class="form-row">
        <button class="layui-btn" lay-filter="save" lay-submit>保存</button>
        <button class="layui-btn layui-btn-primary" onclick="backCashierUserGroup()">返回</button>
    </div>
</div>

<script>
    var tree_data = JSON.parse('{:json_encode($tree_data, JSON_UNESCAPED_UNICODE)}'),
        form,
        repeat_flag = false; //防重复标识

    layui.use('form', function () {
        form = layui.form;
        form.render();

        form.on('submit(save)', function (data) {

            var obj = $("#tree_box input:checked"),
                group_array = [];

            for (var i = 0; i < obj.length; i++) {
                group_array.push(obj.eq(i).val());
            }

            data.field.menu_array = group_array.toString();

            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                type: "POST",
                dataType: "JSON",
                url: ns.url("cashier://shop/user/addgroup"),
                data: data.field,
                success: function (res) {
                    repeat_flag = false;

                    if (res.code == 0) {
                        layer.confirm('添加成功', {
                            title: '操作提示',
                            btn: ['返回列表', '继续添加'],
                            closeBtn: 0,
                            yes: function (index, layero) {
                                location.hash = ns.hash("cashier://shop/user/group")
                                layer.close(index);
                            },
                            btn2: function (index, layero) {
                                listenerHash(); // 刷新页面
                                layer.close(index);
                            }
                        })
                    } else {
                        layer.msg(res.message);
                    }
                }
            });
        });

        form.verify({
            title: function (value) {
                if (value.length == 0) {
                    return '请输入角色名称';
                }
            }
        });
    });

    function backCashierUserGroup() {
        location.hash = ns.hash("cashier://shop/user/group");
    }
</script>
<script src="SHOP_JS/tree.js"></script>

