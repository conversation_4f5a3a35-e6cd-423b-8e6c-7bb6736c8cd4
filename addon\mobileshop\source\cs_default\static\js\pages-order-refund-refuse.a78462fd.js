(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-refund-refuse"],{"1be6":function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return a}));var a={loadingCover:r("59c1").default},i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",[r("v-uni-view",{staticClass:"item-wrap"},[r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("退款方式")]),r("v-uni-text",{staticClass:"value"},[e._v(e._s(1==e.detail.refund_type?"仅退款":"退货退款"))])],1),r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("退款金额")]),r("v-uni-text",{staticClass:"value color-base-text money"},[e._v("￥"+e._s(e.detail.refund_apply_money)+e._s(e.detail.refund_delivery_money>0?"(含运费"+e.detail.refund_delivery_money+")":""))])],1),r("v-uni-view",{staticClass:"form-wrap reason"},[r("v-uni-text",{staticClass:"label"},[e._v("拒绝理由")]),r("v-uni-textarea",{staticClass:"uni-input",attrs:{placeholder:"请输入您的拒绝理由",maxlength:"200"},model:{value:e.refundRefuseReason,callback:function(t){e.refundRefuseReason=t},expression:"refundRefuseReason"}})],1)],1),r("v-uni-view",{staticClass:"tips color-base-text"},[e._v("注意: 建议你与买家协商后，再确定是否拒绝退款。如你拒绝退款后，买家可修改退款申请协议重新发起退款。")]),r("v-uni-view",{staticClass:"footer-wrap"},[r("v-uni-button",{attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.cancel()}}},[e._v("取消")]),r("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save()}}},[e._v("确认拒绝")])],1),r("loading-cover",{ref:"loadingCover"})],1)},o=[]},"2e8f":function(e,t,r){var a=r("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-18ffc56d]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-18ffc56d]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-18ffc56d]{position:fixed;left:0;right:0;z-index:998}.item-wrap[data-v-18ffc56d]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-18ffc56d]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap.border-none[data-v-18ffc56d]{border-bottom:none}.item-wrap .form-wrap[data-v-18ffc56d]:last-child{border-bottom:none}.item-wrap .form-wrap .label[data-v-18ffc56d]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap .value[data-v-18ffc56d]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap .value.money[data-v-18ffc56d]{font-weight:700}.item-wrap .form-wrap .value .radio-wrap[data-v-18ffc56d]{display:inline-block;vertical-align:middle;margin-right:%?20?%}.item-wrap .form-wrap .value .radio[data-v-18ffc56d]{margin-right:%?10?%}.item-wrap .form-wrap.reason[data-v-18ffc56d]{height:auto;display:block;position:relative;flex-direction:column}.item-wrap .form-wrap.reason uni-textarea[data-v-18ffc56d]{width:100%}.item-wrap .form-wrap.picker[data-v-18ffc56d]{border-bottom:1px solid #eee}.item-wrap .form-wrap .uni-input[data-v-18ffc56d]{vertical-align:middle;display:inline-block;-webkit-box-flex:1;-webkit-flex:1;flex:1;text-align:left}.item-wrap .form-wrap.more-wrap .selected[data-v-18ffc56d]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-18ffc56d]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-18ffc56d]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.more-wrap .action[data-v-18ffc56d]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.item-wrap .form-wrap .align-right[data-v-18ffc56d]{text-align:right}.item-wrap .form-tips[data-v-18ffc56d]{margin-top:%?-10?%;border-bottom:1px solid #eee;padding-bottom:%?10?%}.item-wrap .form-tips uni-view[data-v-18ffc56d]{color:#ccc;font-weight:400;font-size:12px;padding-left:%?24?%}.tips[data-v-18ffc56d]{font-weight:700;margin:%?20?% %?30?% 0}.footer-wrap[data-v-18ffc56d]{width:100%;padding:%?40?% 0;display:flex}.footer-wrap uni-button[data-v-18ffc56d]{flex:1}',""]),e.exports=t},"3d9e":function(e,t,r){"use strict";var a=r("bcfb"),i=r.n(a);i.a},5213:function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r("77e0"),i={data:function(){return{orderGoodsId:0,isIphoneX:!1,detail:{},orderInfo:{},actionCallback:null,repeatFlag:!1,refundRealMoney:"",refundTypeArray:["原路退款","线下退款","退款到余额"],refundType:0}},onLoad:function(e){this.orderGoodsId=e.order_goods_id||0},onShow:function(){var e=this;this.$util.checkToken("/pages/order/refund/detail?order_goods_id="+this.orderGoodsId)&&(this.getOrderDetail(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.actionCallback=function(){e.getOrderDetail()})},methods:{getOrderDetail:function(){var e=this;(0,a.getOrderRefundInfoById)(this.orderGoodsId).then((function(t){if(0==t.code){var r=t.data;if(e.detail=r.detail,e.detail.refund_images=r.detail.refund_images?r.detail.refund_images.split(","):"",e.refundRealMoney=r.detail.refund_apply_money,""==e.detail.refund_address){var a=uni.getStorageSync("shop_info")?JSON.parse(uni.getStorageSync("shop_info")):{};e.detail.refund_address="商家未设置联系地址",(a.full_address||a.address)&&(e.detail.refund_address=a.full_address+" "+a.address)}e.orderInfo=r.order_info,e.detail.sku_spec_format=e.detail.sku_spec_format?JSON.parse(e.detail.sku_spec_format):[],e.$refs.loadingCover&&e.$refs.loadingCover.hide()}else e.$util.showToast({title:t.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)}))},cancel:function(){uni.navigateBack({delta:1})},orderRefundRefuse:function(e){this.$util.redirectTo("/pages/order/refund/refuse",{order_goods_id:e})},orderRefundAgree:function(e){this.$util.redirectTo("/pages/order/refund/agree",{order_goods_id:e})},orderRefundTakeDelivery:function(e){this.$util.redirectTo("/pages/order/refund/take_delivery",{order_goods_id:e})},orderRefundTransfer:function(e){this.$util.redirectTo("/pages/order/refund/transfer",{order_goods_id:e})},orderRefundClose:function(e){var t=this;uni.showModal({title:"提示",content:"确定要关闭本次维权吗？",success:function(r){r.confirm&&(0,a.closeOrderRefund)(e).then((function(e){e.code>=0?(t.$util.showToast({title:"维权已关闭"}),t.actionCallback&&t.actionCallback()):t.$util.showToast({title:e.message})}))}})},refundTypeChange:function(e){this.refundType=e.detail.value},previewRefundImage:function(e){uni.previewImage({current:e,urls:this.detail.refund_images})}}};t.default=i},"77e0":function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.closeOrderRefund=function(e){return i.default.post("/shopapi/orderrefund/close",{data:{order_goods_id:e}})},t.getOrderRefundCondition=function(){return i.default.get("/shopapi/orderrefund/condition")},t.getOrderRefundInfoById=function(e){return i.default.post("/shopapi/orderrefund/detail",{data:{order_goods_id:e}})},t.getOrderRefundList=function(e){return i.default.post("/shopapi/orderrefund/lists",{data:e})};var i=a(r("9027"))},bcfb:function(e,t,r){var a=r("2e8f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=r("967d").default;i("ee2923e2",a,!0,{sourceMap:!1,shadowMode:!1})},e02f:function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(r("5213")),o={data:function(){return{refundRefuseReason:""}},mixins:[i.default],methods:{save:function(){var e=this;0!=this.refundRefuseReason.length?this.repeatFlag||(this.repeatFlag=!0,this.$api.sendRequest({url:"/shopapi/orderrefund/refuse",data:{order_goods_id:this.orderGoodsId,refund_refuse_reason:this.refundRefuseReason},success:function(t){0==t.code&&setTimeout((function(){e.cancel()}),1e3),e.repeatFlag=!1,e.$util.showToast({title:t.message})}})):this.$util.showToast({title:"请输入拒绝理由"})}}};t.default=o},ea73:function(e,t,r){"use strict";r.r(t);var a=r("1be6"),i=r("f78e");for(var o in i)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(o);r("3d9e");var d=r("828b"),n=Object(d["a"])(i["default"],a["b"],a["c"],!1,null,"18ffc56d",null,!1,a["a"],void 0);t["default"]=n.exports},f78e:function(e,t,r){"use strict";r.r(t);var a=r("e02f"),i=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a}}]);