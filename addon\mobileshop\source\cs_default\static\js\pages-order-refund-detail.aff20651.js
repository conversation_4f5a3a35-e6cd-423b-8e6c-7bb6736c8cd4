(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-refund-detail"],{"01ea":function(t,e,i){"use strict";i.r(e);var a=i("e18c"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"0a5d":function(t,e,i){"use strict";i.r(e);var a=i("3e42"),o=i("01ea");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("978a");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"1ab1c54f",null,!1,a["a"],void 0);e["default"]=r.exports},"13ae":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,i,a){return t=Number(t),e=Number(e),i=Number(i),a=Number(a),60*t*60*24+60*e*60+60*i+a},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,i=0,a=0,o=0;t>0?(e=Math.floor(t/86400),i=Math.floor(t/3600)-24*e,a=Math.floor(t/60)-24*e*60-60*i,o=Math.floor(t)-24*e*60*60-60*i*60-60*a):this.timeUp(),e<10&&(e="0"+e),i<10&&(i="0"+i),a<10&&(a="0"+a),o<10&&(o="0"+o),this.d=e,this.h=i,this.i=a,this.s=o}}};e.default=a},"3e42":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={loadingCover:i("59c1").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"order-detail-wrap"},[i("v-uni-view",{staticClass:"status-wrap color-base-bg",style:{backgroundImage:"url("+t.$util.img("public/uniapp/order/status-wrap-bg.png")+")"}},[t._v(t._s(t.detail.refund_status_name))]),i("v-uni-view",{staticClass:"block-wrap"},[i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-img"},[i("v-uni-image",{attrs:{src:t.$util.img(t.detail.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError()}}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[t._v(t._s(t.detail.goods_name))]),t.detail.sku_spec_format?i("v-uni-view",{staticClass:"spec-wrap"},[t._l(t.detail.sku_spec_format,(function(e,i){return[t._v(t._s(e.spec_value_name)+" "+t._s(i<t.detail.sku_spec_format.length-1?"; ":""))]}))],2):t._e(),i("v-uni-view",{staticClass:"more-wrap"},[i("v-uni-view",{staticClass:"goods-class"},[t._v(t._s(t.detail.goods_class_name))]),i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-view",{staticClass:"price"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.detail.price))])],1),i("v-uni-view",{staticClass:"num"},[t._v("x"+t._s(t.detail.num))])],1)],1)],1)],1)],1),i("v-uni-view",{staticClass:"block-wrap tit-auto"},[i("v-uni-view",{staticClass:"title"},[t._v("订单信息")]),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("订单类型：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderInfo.order_type_name))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("订单编号：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.order_no))]),i("v-uni-view",{staticClass:"copy color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.detail.order_no)}}},[t._v("复制")])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("买家名称：")]),i("v-uni-view",{staticClass:"box color-base-text"},[t._v(t._s(t.orderInfo.nickname))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("付款方式：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderInfo.pay_type_name))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("配送方式：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderInfo.delivery_type_name))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("联系电话：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderInfo.mobile))])],1),""!=t.orderInfo.promotion_type_name?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("营销活动：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderInfo.promotion_type_name))])],1):t._e()],1),i("v-uni-view",{staticClass:"block-wrap tit-auto"},[i("v-uni-view",{staticClass:"title"},[t._v("售后信息")]),t.detail.refund_apply_money>0?[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("维权类型：")]),i("v-uni-view",{staticClass:"box color-base-text"},[t._v(t._s(t.detail.refund_mode>1?"售后":"退款"))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款方式：")]),i("v-uni-view",{staticClass:"box color-base-text"},[t._v(t._s(t.detail.refund_type_name))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("申请金额：")]),i("v-uni-view",{staticClass:"box"},[t._v("￥"+t._s(t.detail.refund_apply_money))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("申请原因：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.refund_reason||"--"))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("申请说明：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.refund_remark||"--"))])],1),t.detail.refund_images.length>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款图片：")]),i("v-uni-view",{staticClass:"box image"},t._l(t.detail.refund_images,(function(e,a){return i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewRefundImage(a)}}},[i("v-uni-image",{attrs:{src:t.$util.img(e)}})],1)})),1)],1):t._e(),3==t.detail.refund_status?[i("v-uni-view",{staticClass:"order-cell-line"}),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款金额：")]),i("v-uni-view",{staticClass:"box color-base-text"},[t._v("￥"+t._s(t.detail.refund_real_money)+" （"+t._s(t.detail.shop_active_refund_money_type_name)+"）")])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款原因：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.refund_reason))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款说明：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.refund_remark))])],1)]:t._e()]:[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("无")]),i("v-uni-view",{staticClass:"box"})],1)]],2),i("v-uni-view",{staticClass:"block-wrap tit-auto"},[i("v-uni-view",{staticClass:"title"},[t._v("商家主动退款")]),1==t.detail.shop_active_refund?[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款编号：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.shop_active_refund_no))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款金额：")]),i("v-uni-view",{staticClass:"box color-base-text"},[t._v("￥"+t._s(t.detail.shop_active_refund_money)+" （"+t._s(t.detail.shop_active_refund_money_type_name)+"）")])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("退款说明：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.detail.shop_active_refund_remark))])],1)]:[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("无")]),i("v-uni-view",{staticClass:"box"})],1)]],2),2==t.detail.refund_type&&t.detail.refund_status>1&&""!=t.detail.refund_delivery_no?i("v-uni-view",{staticClass:"block-wrap tit-auto"},[i("v-uni-view",{staticClass:"title"},[t._v("退货物流")]),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("物流公司：")]),i("v-uni-view",{staticClass:"box align-right money"},[t._v(t._s(t.detail.refund_delivery_name))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("物流单号：")]),i("v-uni-view",{staticClass:"box align-right money"},[t._v(t._s(t.detail.refund_delivery_no))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("物流说明：")]),i("v-uni-view",{staticClass:"box align-right money"},[t._v(t._s(t.detail.refund_delivery_remark))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("是否入库：")]),i("v-uni-view",{staticClass:"box align-right money color-base-text"},[t._v(t._s(1==t.detail.is_refund_stock?"入库":"不入库"))])],1)],1):t._e(),i("v-uni-view",{staticClass:"block-wrap"},[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("商品金额")]),i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.detail.price))])],1)],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",[t._v("实付金额：")]),i("v-uni-text",{staticClass:"font-size-goods-tag color-base-text"},[t._v("￥")]),i("v-uni-text",{staticClass:"font-size-base color-base-text"},[t._v(t._s(t.detail.real_goods_money))])],1)],1)],1),i("v-uni-view",{staticClass:"block-wrap log"},[i("v-uni-view",{staticClass:"title color-base-text"},[t._v("售后日志")]),t._l(t.detail.refund_log_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item"},[1==e.action_way?i("v-uni-text",{staticClass:"tag"},[t._v("买家")]):2==e.action_way?i("v-uni-text",{staticClass:"tag color-base-bg"},[t._v("商家")]):i("v-uni-text",{staticClass:"tag platform"},[t._v("平台")]),i("v-uni-text",{staticClass:"action"},[t._v(t._s(e.action))]),i("v-uni-text",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(e.action_time)))])],1)}))],2),i("v-uni-view",{staticClass:"block-wrap tips"},[i("v-uni-view",{staticClass:"title color-base-text"},[t._v("提醒")]),i("v-uni-text",[t._v("如果未发货，请点击同意退款给买家")]),i("v-uni-text",[t._v("如果实际已发货，请主动与买家联系")]),i("v-uni-text",[t._v("如果订单整体退款后，优惠券和余额会退还给买家")])],1),t.detail.refund_action&&t.detail.refund_action.length?[i("v-uni-view",{staticClass:"placeholder-height"}),i("v-uni-view",{staticClass:"footer-wrap"},[i("v-uni-view",{staticClass:"container"},t._l(t.detail.refund_action,(function(e,a){return i("v-uni-button",{key:a,attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderAction(e.event,t.orderGoodsId)}}},[t._v(t._s(e.title))])})),1)],1)]:t._e(),i("loading-cover",{ref:"loadingCover"})],2)},s=[]},"4cea":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"4d89":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-e9e34d7a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-e9e34d7a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-e9e34d7a]{position:fixed;left:0;right:0;z-index:998}.uni-countdown[data-v-e9e34d7a]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-e9e34d7a]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-e9e34d7a]{line-height:%?50?%}.uni-countdown__number[data-v-e9e34d7a]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},5213:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("77e0"),o={data:function(){return{orderGoodsId:0,isIphoneX:!1,detail:{},orderInfo:{},actionCallback:null,repeatFlag:!1,refundRealMoney:"",refundTypeArray:["原路退款","线下退款","退款到余额"],refundType:0}},onLoad:function(t){this.orderGoodsId=t.order_goods_id||0},onShow:function(){var t=this;this.$util.checkToken("/pages/order/refund/detail?order_goods_id="+this.orderGoodsId)&&(this.getOrderDetail(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.actionCallback=function(){t.getOrderDetail()})},methods:{getOrderDetail:function(){var t=this;(0,a.getOrderRefundInfoById)(this.orderGoodsId).then((function(e){if(0==e.code){var i=e.data;if(t.detail=i.detail,t.detail.refund_images=i.detail.refund_images?i.detail.refund_images.split(","):"",t.refundRealMoney=i.detail.refund_apply_money,""==t.detail.refund_address){var a=uni.getStorageSync("shop_info")?JSON.parse(uni.getStorageSync("shop_info")):{};t.detail.refund_address="商家未设置联系地址",(a.full_address||a.address)&&(t.detail.refund_address=a.full_address+" "+a.address)}t.orderInfo=i.order_info,t.detail.sku_spec_format=t.detail.sku_spec_format?JSON.parse(t.detail.sku_spec_format):[],t.$refs.loadingCover&&t.$refs.loadingCover.hide()}else t.$util.showToast({title:e.message}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)}))},cancel:function(){uni.navigateBack({delta:1})},orderRefundRefuse:function(t){this.$util.redirectTo("/pages/order/refund/refuse",{order_goods_id:t})},orderRefundAgree:function(t){this.$util.redirectTo("/pages/order/refund/agree",{order_goods_id:t})},orderRefundTakeDelivery:function(t){this.$util.redirectTo("/pages/order/refund/take_delivery",{order_goods_id:t})},orderRefundTransfer:function(t){this.$util.redirectTo("/pages/order/refund/transfer",{order_goods_id:t})},orderRefundClose:function(t){var e=this;uni.showModal({title:"提示",content:"确定要关闭本次维权吗？",success:function(i){i.confirm&&(0,a.closeOrderRefund)(t).then((function(t){t.code>=0?(e.$util.showToast({title:"维权已关闭"}),e.actionCallback&&e.actionCallback()):e.$util.showToast({title:t.message})}))}})},refundTypeChange:function(t){this.refundType=t.detail.value},previewRefundImage:function(t){uni.previewImage({current:t,urls:this.detail.refund_images})}}};e.default=o},"553a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-1ab1c54f]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-1ab1c54f]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-1ab1c54f]{position:fixed;left:0;right:0;z-index:998}.order-detail-wrap[data-v-1ab1c54f]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?30?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?30?%)}.align-right[data-v-1ab1c54f]{text-align:right}.status-wrap[data-v-1ab1c54f]{background-size:100% 100%;padding:%?40?%;height:%?80?%;line-height:%?80?%;font-size:%?32?%;color:#fff}.block-wrap[data-v-1ab1c54f]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative}.block-wrap .goods-item[data-v-1ab1c54f]{background:#fff;margin-top:%?20?%;display:flex;position:relative;flex-flow:row wrap}.block-wrap .goods-item .goods-img[data-v-1ab1c54f]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.block-wrap .goods-item .goods-img uni-image[data-v-1ab1c54f]{width:100%;height:100%}.block-wrap .goods-item .info-wrap[data-v-1ab1c54f]{flex:1;display:flex;flex-direction:column;width:50%}.block-wrap .goods-item .info-wrap .name-wrap[data-v-1ab1c54f]{line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.block-wrap .goods-item .info-wrap .spec-wrap[data-v-1ab1c54f]{line-height:1;margin-top:%?10?%;margin-bottom:%?10?%;color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.block-wrap .goods-item .info-wrap .more-wrap[data-v-1ab1c54f]{display:flex}.block-wrap .goods-item .info-wrap .more-wrap .goods-class[data-v-1ab1c54f]{font-size:%?20?%;color:#909399}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap[data-v-1ab1c54f]{flex:1;text-align:right}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .unit[data-v-1ab1c54f]{font-size:%?20?%}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .price[data-v-1ab1c54f]{display:inline-block;line-height:1;flex:1}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .num[data-v-1ab1c54f]{color:#909399;font-size:%?20?%;line-height:1}.block-wrap .goods-item .info-wrap .delivery-status-name[data-v-1ab1c54f]{font-weight:700;text-align:right;line-height:1;margin-top:%?10?%}.block-wrap .title[data-v-1ab1c54f]{font-size:%?32?%}.block-wrap .order-cell[data-v-1ab1c54f]{display:flex;margin:%?20?% 0;align-items:flex-start;background:#fff;line-height:%?40?%}.block-wrap .order-cell[data-v-1ab1c54f]:first-child{margin-top:0}.block-wrap .order-cell .tit[data-v-1ab1c54f]{width:%?200?%;text-align:left}.block-wrap .order-cell .box[data-v-1ab1c54f]{flex:1;padding:0 %?20?%;line-height:inherit}.block-wrap .order-cell .box.money[data-v-1ab1c54f]{padding:0}.block-wrap .order-cell .box.bold[data-v-1ab1c54f]{font-weight:700}.block-wrap .order-cell .box .operator[data-v-1ab1c54f]{font-size:%?24?%;margin-right:%?6?%}.block-wrap .order-cell .box.image[data-v-1ab1c54f]{display:flex;flex-wrap:wrap}.block-wrap .order-cell .box.image uni-view[data-v-1ab1c54f]{width:%?120?%;height:%?120?%;display:flex;justify-content:center;align-items:center;margin-right:%?20?%}.block-wrap .order-cell .box.image uni-view[data-v-1ab1c54f]:nth-child(n+4){margin-top:%?20?%}.block-wrap .order-cell .box.image uni-view uni-image[data-v-1ab1c54f]{width:100%;height:100%}.block-wrap .order-cell-line[data-v-1ab1c54f]{height:%?2?%}.block-wrap.tit-auto .tit[data-v-1ab1c54f]{width:auto}.block-wrap.tips .title[data-v-1ab1c54f]{font-size:%?28?%}.block-wrap.tips uni-text[data-v-1ab1c54f]{font-size:%?24?%;display:block;margin-top:%?10?%}.block-wrap.log .title[data-v-1ab1c54f]{font-size:%?28?%}.block-wrap.log .item[data-v-1ab1c54f]{display:flex;align-items:center;margin-top:%?40?%}.block-wrap.log .item[data-v-1ab1c54f]:last-child{margin-bottom:%?20?%}.block-wrap.log .item .tag[data-v-1ab1c54f]{color:#fff;border-radius:50%;margin-right:%?20?%;padding:%?10?%;background-color:#909399}.block-wrap.log .item .tag .platform[data-v-1ab1c54f]{background-color:#4685fd}.block-wrap.log .item .action[data-v-1ab1c54f]{flex:1}.block-wrap.log .item uni-text[data-v-1ab1c54f]{font-size:%?24?%}.placeholder-height[data-v-1ab1c54f]{height:%?120?%}.footer-wrap[data-v-1ab1c54f]{position:fixed;bottom:0;padding:%?20?% 0 %?10?%;z-index:10;width:100%;text-align:right;background-color:#fff;border-top:1px solid #eee;padding-bottom:calc(constant(safe-area-inset-bottom) + %?10?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?10?%)}.footer-wrap .container[data-v-1ab1c54f]{margin:0 %?30?%}.footer-wrap .container uni-button[data-v-1ab1c54f]{margin-left:%?20?%!important}.footer-wrap .container uni-button[data-v-1ab1c54f]:first-child{margin-left:0!important}',""]),t.exports=e},5695:function(t,e,i){"use strict";i.r(e);var a=i("4cea"),o=i("df68");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("c4f9");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"e9e34d7a",null,!1,a["a"],void 0);e["default"]=r.exports},"77e0":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.closeOrderRefund=function(t){return o.default.post("/shopapi/orderrefund/close",{data:{order_goods_id:t}})},e.getOrderRefundCondition=function(){return o.default.get("/shopapi/orderrefund/condition")},e.getOrderRefundInfoById=function(t){return o.default.post("/shopapi/orderrefund/detail",{data:{order_goods_id:t}})},e.getOrderRefundList=function(t){return o.default.post("/shopapi/orderrefund/lists",{data:t})};var o=a(i("9027"))},"978a":function(t,e,i){"use strict";var a=i("b79f"),o=i.n(a);o.a},b65d:function(t,e,i){var a=i("4d89");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("431f734f",a,!0,{sourceMap:!1,shadowMode:!1})},b79f:function(t,e,i){var a=i("553a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("8e654ae6",a,!0,{sourceMap:!1,shadowMode:!1})},c4f9:function(t,e,i){"use strict";var a=i("b65d"),o=i.n(a);o.a},df68:function(t,e,i){"use strict";i.r(e);var a=i("13ae"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},e18c:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("5695")),s=a(i("5213")),n={data:function(){return{}},components:{uniCountDown:o.default},mixins:[s.default],methods:{imgError:function(t){this.detail.sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},orderAction:function(t,e){try{this[t](e)}catch(i){console.log("orderAction error：",i)}}}};e.default=n}}]);