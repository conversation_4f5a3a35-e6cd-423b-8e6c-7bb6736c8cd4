(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-storemanage-edit-edit"],{2302:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"weui-switch",class:{"weui-switch-on":t.checked,"color-base-border":t.checked},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.change()}}},[a("v-uni-view",{staticClass:"bgview",class:{"color-base-bg":t.checked}}),a("v-uni-view",{staticClass:"spotview"})],1)],1)},s=[]},3350:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"nsSwitch",props:{checked:{type:Boolean,default:!1}},methods:{change:function(){this.$emit("change")}}};e.default=i},3789:function(t,e,a){"use strict";a.r(e);var i=a("3350"),s=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=s.a},"584e":function(t,e,a){"use strict";a.d(e,"b",(function(){return s})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={nsSwitch:a("e1f1").default},s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"iphone-safe-area"},[a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("门店名称")]),a("v-uni-input",{staticClass:"value",attrs:{type:"text",placeholder:"请输入门店名称","placeholder-class":"placetext"},model:{value:t.shopInfo.store_name,callback:function(e){t.$set(t.shopInfo,"store_name",e)},expression:"shopInfo.store_name"}})],1),a("v-uni-view",{staticClass:"form-wrap goods-img"},[a("v-uni-text",{staticClass:"label"},[t._v("门店图片")]),a("v-uni-view",{staticClass:"img-list"},[a("v-uni-view",{staticClass:"add logo",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uplodImg("store_image")}}},[t.shopInfo.store_image?a("v-uni-image",{attrs:{src:t.$util.img(t.shopInfo.store_image),mode:"aspectFit"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError("store_image")},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.previewMedia("store_image")}}}):a("v-uni-text",{staticClass:"iconfont iconadd1"}),t.shopInfo.store_image?a("v-uni-view",{staticClass:"del-wrap iconfont iconclose",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.delImg("store_image")}}}):t._e()],1),a("v-uni-view",{staticClass:"tips"},[t._v("建议图片尺寸：100*100像素")])],1)],1),a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("门店电话")]),a("v-uni-input",{staticClass:"value",attrs:{type:"text",placeholder:"请输入门店电话","placeholder-class":"placetext"},model:{value:t.shopInfo.telphone,callback:function(e){t.$set(t.shopInfo,"telphone",e)},expression:"shopInfo.telphone"}})],1),a("v-uni-view",{staticClass:"form-wrap between"},[a("v-uni-view",{staticClass:"label"},[t._v("营业状态")]),a("v-uni-view",[a("ns-switch",{staticClass:"switch",attrs:{checked:t.isBusiness},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.isFree()}}})],1)],1),a("v-uni-view",{staticClass:"form-wrap between"},[a("v-uni-view",{staticClass:"label"},[t._v("是否启用自提")]),a("v-uni-view",[a("ns-switch",{staticClass:"switch",attrs:{checked:t.is_pickup},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.isFrees()}}})],1)],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("门店地址")]),a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请选择省市区",maxlength:"100","placeholder-class":"placetext"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAddress.apply(void 0,arguments)}},model:{value:t.shopInfo.full_address,callback:function(e){t.$set(t.shopInfo,"full_address",e)},expression:"shopInfo.full_address"}})],1),a("v-uni-view",{staticClass:"form-wrap between"},[a("v-uni-text",{staticClass:"label"},[t._v("营业时间")]),a("v-uni-view",{staticClass:"time-change"},[a("v-uni-picker",{staticClass:"padding-left padding-right",attrs:{mode:"time",value:t.shopInfo.start_time},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindStartDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input",class:t.startTime?"":"placetext"},[t._v(t._s(t.startTime||"开始时间"))])],1),a("v-uni-text",{class:t.startTime?"":"placetext"},[t._v("-")]),a("v-uni-picker",{staticClass:"padding-left padding-right",attrs:{mode:"time",value:t.shopInfo.end_time},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindEndDateChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input",class:t.endTime?"":"placetext"},[t._v(t._s(t.endTime||"结束时间"))])],1)],1)],1),a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("门店账号")]),1==t.type?a("v-uni-input",{staticClass:"value",attrs:{type:"text",value:t.shopInfo.username,placeholder:"请输入门店账号","placeholder-class":"placetext"}}):a("v-uni-input",{staticClass:"value",attrs:{type:"text",value:t.shopInfo.username,disabled:!0,"placeholder-class":"placetext"}})],1),1==t.type?a("v-uni-view",{staticClass:"form-wrap more-wrap"},[a("v-uni-text",{staticClass:"label"},[t._v("门店密码")]),a("v-uni-input",{staticClass:"value",attrs:{type:"text",placeholder:"请输入门店密码","placeholder-class":"placetext"}})],1):t._e()],1),a("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save()}}},[t._v("保存")])],1)},n=[]},"5ef8":function(t,e,a){"use strict";a.r(e);var i=a("584e"),s=a("69f0");for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(n);a("656b");var o=a("828b"),r=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"33f5e2a0",null,!1,i["a"],void 0);e["default"]=r.exports},"60a3":function(t,e,a){var i=a("ec44");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=a("967d").default;s("69b0a1fe",i,!0,{sourceMap:!1,shadowMode:!1})},"656b":function(t,e,a){"use strict";var i=a("af32"),s=a.n(i);s.a},"69f0":function(t,e,a){"use strict";a.r(e);var i=a("75d6"),s=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=s.a},"75d6":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=i(a("412b")),n={data:function(){return{shopInfo:{start_time:"",end_time:""},index:1,arr:["aa","bb","cc"],showTime:"",isBusiness:!1,is_pickup:!1,startTime:"",endTime:"",type:0}},onShow:function(){},onLoad:function(t){this.$util.checkToken("/pages/my/shop/config")&&(t.store_id&&this.dataDetail(t.store_id),this.type=t.type)},computed:{formData:function(){return{store_image:this.shopInfo.store_image,avatar:this.shopInfo.avatar,banner:this.shopInfo.banner,seo_description:this.shopInfo.seo_description,seo_keywords:this.shopInfo.seo_keywords}}},methods:{dataDetail:function(t){var e=this;this.$api.sendRequest({url:"/shopapi/store/detail",data:{store_id:t},success:function(t){if(t.code>=0){if(e.isBusiness=0!=t.data.info.status,e.is_pickup=0!=t.data.info.is_pickup,t.data.info.open_date){var a=t.data.info.open_date.split("-");e.startTime=a[0],e.endTime=a[1]}e.shopInfo=t.data.info}}})},isFree:function(){this.isBusiness=1!=this.isBusiness},isFrees:function(){this.is_pickup=1!=this.is_pickup},bindStartDateChange:function(t){this.startTime=t.detail.value},bindEndDateChange:function(t){this.endTime=t.detail.value},save:function(){var t=this;this.shopInfo.status=1==this.isBusiness?1:0,this.shopInfo.is_pickup=1==this.is_pickup?1:0,this.shopInfo.open_date=this.startTime+"-"+this.endTime,this.$api.sendRequest({url:"/shopapi/store/editStore",data:this.shopInfo,success:function(e){t.$util.showToast({title:e.message}),e.code>=0&&setTimeout((function(){t.$util.redirectTo("/pages/storemanage/storemanage",{},"redirectTo")}),1e3)}})},uplodImg:function(t){var e=this;this.$util.upload({number:1,path:"image"},(function(a){a&&(e.$util.showToast({title:"上传成功"}),"store_image"==t?e.shopInfo.store_image=a[0]:"avatar"==t?e.shopInfo.avatar=a[0]:"banner"==t&&(e.shopInfo.banner=a[0]))}))},delImg:function(t){"store_image"==t?this.shopInfo.store_image="":"avatar"==t?this.shopInfo.avatar="":"banner"==t&&(this.shopInfo.banner="")},previewMedia:function(t){var e=[this.$util.img(this.shopInfo[t])];uni.previewImage({current:0,urls:e})},imgError:function(t){this.shopInfo[t]=this.$util.img(this.$util.getDefaultImage().default_headimg),this.$forceUpdate()},getAddress:function(t){var e=this;this.$api.sendRequest({url:"/api/memberaddress/tranAddressInfo",data:{latlng:t},success:function(t){0==t.code?(e.shopInfo.full_address="",e.shopInfo.full_address+=void 0!=t.data.province?t.data.province:"",e.shopInfo.full_address+=void 0!=t.data.city?"-"+t.data.city:"",e.shopInfo.full_address+=void 0!=t.data.district?"-"+t.data.district:"",e.shopInfo.province=t.data.province_id,e.shopInfo.province_name=t.data.province,e.shopInfo.city=t.data.city_id,e.shopInfo.city_name=t.data.city,e.shopInfo.district=t.data.district_id,e.shopInfo.district_name=t.data.district):e.$util.showToast({title:"数据有误"})}})},selectAddress:function(){var t=s.default.h5Domain+"/pages/my/shop/contact";window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(t)+"&key="+s.default.mpKey+"&referer=myapp"}}};e.default=n},"88c2":function(t,e,a){"use strict";var i=a("60a3"),s=a.n(i);s.a},af32:function(t,e,a){var i=a("e8fd");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=a("967d").default;s("37ba7b9d",i,!0,{sourceMap:!1,shadowMode:!1})},e1f1:function(t,e,a){"use strict";a.r(e);var i=a("2302"),s=a("3789");for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(n);a("88c2");var o=a("828b"),r=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"369b0c0f",null,!1,i["a"],void 0);e["default"]=r.exports},e8fd:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-33f5e2a0]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-33f5e2a0]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-33f5e2a0]{position:fixed;left:0;right:0;z-index:998}.value[data-v-33f5e2a0]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}uni-button[data-v-33f5e2a0]{margin-top:%?40?%}.placetext[data-v-33f5e2a0]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#909399}.container-wrap[data-v-33f5e2a0]{margin-bottom:%?60?%}.item-wrap[data-v-33f5e2a0]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-33f5e2a0]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;min-height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-33f5e2a0]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-33f5e2a0]{font-weight:700}.item-wrap .form-wrap .label[data-v-33f5e2a0]{min-width:%?150?%;vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap .time-change[data-v-33f5e2a0]{display:flex;align-items:center;flex:1;justify-content:flex-end}.item-wrap .form-wrap uni-textarea[data-v-33f5e2a0],\r\n.item-wrap .form-wrap .picker[data-v-33f5e2a0],\r\n.item-wrap .form-wrap uni-input[data-v-33f5e2a0]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap .picker .iconfont[data-v-33f5e2a0]{vertical-align:middle}.item-wrap .form-wrap uni-textarea[data-v-33f5e2a0]{height:%?100?%;padding:%?20?%}.item-wrap .form-wrap .value[data-v-33f5e2a0]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap.more-wrap .selected[data-v-33f5e2a0]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-33f5e2a0]{color:#303133}.item-wrap .form-wrap.more-wrap .flex_1[data-v-33f5e2a0]{flex:1;text-align:right;padding-right:%?20?%}.item-wrap .form-wrap.more-wrap .flex_1 uni-input[data-v-33f5e2a0]{height:%?100?%;display:block}.item-wrap .form-wrap.more-wrap .iconfont[data-v-33f5e2a0]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.goods-img[data-v-33f5e2a0]{display:flex}.item-wrap .form-wrap.goods-img .label[data-v-33f5e2a0]{align-self:flex-start;margin-top:%?20?%}.item-wrap .form-wrap.goods-img .img-list[data-v-33f5e2a0]{padding-top:%?40?%;padding-bottom:%?40?%;padding-left:%?40?%}.item-wrap .form-wrap.goods-img .img-list .add[data-v-33f5e2a0]{position:relative;width:%?140?%;text-align:center;border:1px dashed #ccc;font-weight:700;color:#909399}.item-wrap .form-wrap.goods-img .img-list .add .iconfont[data-v-33f5e2a0]{font-size:%?40?%}.item-wrap .form-wrap.goods-img .img-list .add.logo[data-v-33f5e2a0]{height:%?84?%;line-height:%?84?%}.item-wrap .form-wrap.goods-img .img-list .add.avatar[data-v-33f5e2a0]{height:%?140?%;line-height:%?140?%}.item-wrap .form-wrap.goods-img .img-list .add.banner[data-v-33f5e2a0]{height:%?120?%;line-height:%?120?%}.item-wrap .form-wrap.goods-img .img-list .add uni-image[data-v-33f5e2a0]{width:100%;height:100%}.item-wrap .form-wrap.goods-img .img-list .add .del-wrap[data-v-33f5e2a0]{position:absolute;top:%?-16?%;right:%?-16?%;line-height:1;width:16px;height:16px;background-color:rgba(0,0,0,.5);border-radius:50%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;font-size:12px;color:#fff;font-weight:700}.item-wrap .form-wrap.goods-img .tips[data-v-33f5e2a0]{color:#909399;font-size:%?20?%;margin-top:%?20?%;word-wrap:break-word;word-break:break-all}.form-content[data-v-33f5e2a0]{display:flex;flex-direction:row}.form-content uni-view[data-v-33f5e2a0]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#909399}.between[data-v-33f5e2a0]{display:flex;justify-content:space-between}.footer-wrap[data-v-33f5e2a0]{width:100%;padding:%?40?% 0;z-index:10;padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}',""]),t.exports=e},ec44:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'.weui-switch[data-v-369b0c0f]{display:block;position:relative;width:%?94?%;height:%?45?%;outline:0;border-radius:%?30?%;border:1px solid;border-color:#dfdfdf;transition:background-color .1s,border .1s}.weui-switch .bgview[data-v-369b0c0f]{content:" ";position:absolute;top:0;left:0;width:%?94?%;height:%?45?%;border-radius:%?30?%;transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch .spotview[data-v-369b0c0f]{content:" ";position:absolute;top:%?2?%;left:%?4?%;width:%?40?%;height:%?40?%;border-radius:50%;background-color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-on[data-v-369b0c0f]{border-color:#6f6f6f}.weui-switch-on .bgview[data-v-369b0c0f]{border-color:#1aad19}.weui-switch-on .spotview[data-v-369b0c0f]{-webkit-transform:translateX(%?48?%);transform:translateX(%?48?%)}',""]),t.exports=e}}]);