<link rel="stylesheet" href="STATIC_CSS/promotion_detail.css">
<style>
.discount { justify-content: space-between;line-height: 30px;padding: 0px 15px;background-color: #F6FBFD;border: 1px dashed #BCE8F1; margin-bottom: 5px;}
</style>

<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
        <span class="card-title">基本信息</span>
	</div>
	<div class="layui-card-body">
		<div class="promotion-view">
			<div class="promotion-view-item">
				<label>活动名称：</label>
				<span>{$manjian_info.manjian_name}</span>
			</div>
			<div class="promotion-view-item">
				<label>开始时间：</label>
				<span>{:date('Y-m-d H:i:s',$manjian_info.start_time)}</span>
			</div>
			<div class="promotion-view-item">
				<label>结束时间：</label>
				<span>{:date('Y-m-d H:i:s',$manjian_info.end_time)}</span>
			</div>
			{if $manjian_info.manjian_type == 1}
			<div class="promotion-view-item">
				<label>活动商品：</label>
				<span>全部商品参与</span>
			</div>
			{/if}
		</div>
		<div class="promotion-view">
			<div class="promotion-view-item-line">
				<label class="promotion-view-item-custom-label">满减规则：</label>
				<div class="promotion-view-item-custom-box">
					<div class="discount-box">
					{foreach name="$manjian_info.rule" item="vo"}
					<div class="discount">
						<div class="discount-con">
							<p>单笔订单满
								{if $manjian_info.type == 0}
									<span class="red-color money-num">{:sprintf('%.2f', $vo.limit)}</span>元
								{else/}
									<span class="red-color money-num">{:number_format($vo.limit)}</span>件
								{/if}
								{if isset($vo.discount_money)}，减{$vo.discount_money}元 {/if}
								{if isset($vo.free_shipping)}，包邮 {/if}
								{if isset($vo.point)}，送{$vo.point}积分 {/if}
								{if isset($vo.coupon)}
									{foreach name="$vo.coupon_data" item="coupon"}
									，送优惠券<a href="{:href_url('coupon://shop/coupon/detail', ['coupon_type_id' => $coupon.coupon_type_id ])}" target="_blank">【{$coupon.coupon_name}】</a>
									{/foreach}
								{/if}
							</p>
						</div>
					</div>
					{/foreach}
				</div>
				</div>
			</div>
		</div>
	</div>
</div>
{if $manjian_info.manjian_type != 1}
	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
            <span class="card-title">活动商品</span>
		</div>
		<div class="layui-card-body">
			<div class='promotion-view-list'>
				<table id="promotion_list"></table>
			</div>
		</div>
	</div>
{/if}

<script type='text/html' id="promotion_list_item_box_html">
	<div class="promotion-list-item-title">
		<div class="promotion-list-item-title-icon">
			<img src="{{ d.goods_image ? ns.img(d.goods_image.split(",")[0],'small') : ''  }}" alt="">
		</div>
		<p class="promotion-list-item-title-name multi-line-hiding">{{ d.goods_name }}</p>
	</div>
</script>
<script>
	var promotion_list = {:json_encode($manjian_info.goods_list, JSON_UNESCAPED_UNICODE)};

	layui.use('table', function() {

		new Table({
			elem: '#promotion_list',
			page: false,
			limit: Number.MAX_VALUE,
			cols: [
				[{
					field: 'goods_name',
					title: '商品名称',
					unresize: 'false',
					width: '60%',
					templet: "#promotion_list_item_box_html"
				}, {
					field: 'price',
					title: '商品价格(元)',
					unresize: 'false',
					align: 'right',
					width: '40%',
					templet: function(data) {
						return '￥' + data.price;
					}
				}],
			],
			data: promotion_list,
		});
	});
</script>