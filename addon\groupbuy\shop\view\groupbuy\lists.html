<style>
	.single-filter-box button {margin-bottom: 20px;}
	.layui-layout-admin .body-content{padding-top: 15px !important;}
	.layui-layout-admin .single-filter-box button{margin-bottom: 0;}
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加团购</button>
</div>

<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goods_name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
				<div class="layui-inline">
					<label class="layui-form-label">活动时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
            </div>

            <div class="form-row">
                <button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab table-tab" lay-filter="groupbuy_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		<li data-status="1">未开始</li>
		<li data-status="2">进行中</li>
		<li data-status="3">已结束</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="groupbuy_list" lay-filter="groupbuy_list"></table>
	</div>
</div>

<!-- 商品 -->
<script type="text/html" id="goods">
	<div class="table-title">
		<div class="title-pic">
			{{#  if(d.goods_image){  }}
			<img layer-src="{{ns.img(d.goods_image.split(',')[0],'big')}}" src="{{ns.img(d.goods_image.split(',')[0],'small')}}"/>
			{{#  }  }}
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.goods_name}}">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{#  if(d.status == 1){  }}
	未开始
	{{#  }else if(d.status == 2){  }}
	进行中
	{{#  }else if(d.status == 3){  }}
	已结束
	{{#  }  }}
</script>

<!-- 推广 -->
{include file="app/shop/view/component/promote_show.html"}

<!-- 批量操作 -->
<script type="text/html" id="toolbarAction">
	<button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
	<button class="layui-btn layui-btn-primary" lay-event="close">批量关闭</button>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="operation-wrap" data-groupbuy-id="{{d.groupbuy_id}}">
		<div class="popup-qrcode-wrap" style="display: none"><img class="popup-qrcode-loadimg" src="__STATIC__/loading/loading.gif"/></div>
		<div class="table-btn">
			{{# if(d.status == 2){ }}
			<a class="layui-btn text-color" lay-event="select">推广</a>
			{{# } }}
		<a class="layui-btn" lay-event="edit">编辑</a>
		{{# if(d.status == 1){ }}
		<a class="layui-btn" lay-event="del">删除</a>
		{{# }else if(d.status == 2){ }}
		<a class="layui-btn" lay-event="close">结束</a>
		{{# }else if(d.status == 3){ }}
		<a class="layui-btn" lay-event="del">删除</a>
		{{# } }}
	</div>
	</div>
</script>

<script>
	var laytpl;
	layui.use(['form', 'element', 'laydate','laytpl'], function() {
		var table,
			form = layui.form,
			element = layui.element,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		laytpl = layui.laytpl,
		form.render();

		//开始时间
		laydate.render({
			elem: '#start_time', //指定元素
            type: 'datetime'
		});
		//结束时间
		laydate.render({
			elem: '#end_time', //指定元素
            type: 'datetime'
		});

		element.on('tab(groupbuy_tab)', function() {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': this.getAttribute('data-status')
				}
			});
		});

		table = new Table({
			elem: '#groupbuy_list',
			url: ns.url("groupbuy://shop/groupbuy/lists"),
			cols: [
				[{
					type: 'checkbox',
					width: '3%',
				},{
					title: '商品信息',
					unresize: 'false',
					width: '20%',
					templet: '#goods'
				}, {
					title: '商品原价',
					unresize: 'false',
					width: '12%',
					align: 'right',
					templet: function(data) {
						return '￥'+ data.price;
					}
				}, {
					field: 'groupbuy_price',
					title: '团购价格',
					unresize: 'false',
					width: '10%',
					sort:true,
					templet: function(data) {
						return '￥'+ data.groupbuy_price;
					}
				}, {
					field: 'buy_num',
					title: '起购量',
					unresize: 'false',
					width: '8%'
				}, {
					field: 'sell_num',
					title: '销量',
					unresize: 'false',
					sort:true,
					width: '8%'
				}, {
					title: '活动时间',
					unresize: 'false',
					width: '15%',
					templet: '#time'
				}, {
					title: '状态',
					unresize: 'false',
					width: '10%',
					templet: '#status'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align : 'right'
				}]
			],
			toolbar: '#toolbarAction'
		});

		table.on("sort",function (obj) {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					order:obj.field,
					sort:obj.type
				}
			});
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		//监听Tab切换
		element.on('tab(status)', function(data) {
			var status = $(this).attr("data-status");
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': status
				}
			});
		});

		// 监听工具栏操作
		table.toolbar(function (obj) {
			var data = obj.data;
			if(data.length <= 0) return;
			var groupbuyIdAll = [];
			for (var i in data){
				groupbuyIdAll.push(data[i].groupbuy_id);
			}

			switch (obj.event) {
				case 'delete':
					deleteGroupbuyAll(groupbuyIdAll)
					break;
				case 'close':
					closeGroupbuyAll(groupbuyIdAll)
					break;
			}
		})

		function deleteGroupbuyAll(data){
			layer.confirm('确定要删除团购活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("groupbuy://shop/groupbuy/deleteAll"),
					data: {
						groupbuy_id: data
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						table.reload({
							page: {
								curr: 1
							},
						});
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		function closeGroupbuyAll(data){
			layer.confirm('确定要结束团购活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("groupbuy://shop/groupbuy/finishAll"),
					data: {
						groupbuy_id: data
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						table.reload();
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.hash = ns.hash("groupbuy://shop/groupbuy/edit", {"groupbuy_id": data.groupbuy_id});
					break;
				case 'del': //删除
					deleteGroupbuy(data.groupbuy_id);
					break;
				case 'select': //推广
					groupbuyUrl(data);
					break;
				case 'close': // 结束
					closeGroupbuy(data.groupbuy_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteGroupbuy(groupbuy_id) {
			layer.confirm('确定要删除该团购活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("groupbuy://shop/groupbuy/delete"),
					data: {
						groupbuy_id: groupbuy_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload({
								page: {
									curr: 1
								},
							});
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		// 结束
		function closeGroupbuy(groupbuy_id) {

			layer.confirm('确定要结束该团购活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("groupbuy://shop/groupbuy/finish"),
					data: {
						groupbuy_id: groupbuy_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		function groupbuyUrl(data){
			new PromoteShow({
				url:ns.url("groupbuy://shop/groupbuy/groupbuyUrl"),
				param:{groupbuy_id:data.groupbuy_id},
			})
		}
	});

	function add() {
		location.hash = ns.hash("groupbuy://shop/groupbuy/add");
	}
</script>
