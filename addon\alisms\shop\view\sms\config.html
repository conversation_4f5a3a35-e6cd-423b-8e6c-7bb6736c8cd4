<div class="layui-form form-wrap">
    <div class="layui-form-item">
        <label class="layui-form-label">是否开启：</label>
        <div class="layui-input-block" id="isOpen">
			<input type="checkbox" name="status" lay-filter="isOpen" value="1" lay-skin="switch" {if condition="$info.is_use == 1"} checked {/if} />
        </div>
		<div class="word-aux">当前使用阿里云短信配置</div>
    </div>

	<div class="layui-form-item">
		<label class="layui-form-label">APP_KEY：</label>
		<div class="layui-input-block">
			<input type="text" name="access_key_id" placeholder="请输入内容APP_KEY" {if $info.value } value="{$info.value.access_key_id}" {/if} autocomplete="off" class="layui-input len-long">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">SECRET_KEY：</label>
		<div class="layui-input-block">
			<input type="text" name="access_key_secret" placeholder="请输入SECRET_KEY" {if $info.value } value="{$info.value.access_key_secret}" {/if} autocomplete="off" class="layui-input len-long">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">短信内容签名：</label>
		<div class="layui-input-block">
			<input type="text" name="smssign" placeholder="请输入短信内容签名" {if $info.value } value="{$info.value.smssign}" {/if} autocomplete="off" class="layui-input len-long">
		</div>
	</div>

    <!-- 表单操作 -->
    <div class="form-row">
        <button class="layui-btn" lay-submit lay-filter="save">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="backSms()">返回</button>
    </div>

</div>

<script>
    layui.use('form', function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
		form.render();

        form.on('submit(save)', function(data) {
            if (repeat_flag) return;
            repeat_flag = true;
			
            $.ajax({
                url: ns.url("alisms://shop/sms/config"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续编辑'],
							yes: function(index, layero) {
								location.hash = ns.hash("shop/message/sms")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
                }
            });
        });
    });

    function backSms() {
        location.hash = ns.hash("shop/message/sms");
    }
</script>
