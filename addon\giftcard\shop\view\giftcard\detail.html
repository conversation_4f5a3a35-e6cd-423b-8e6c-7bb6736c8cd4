<style>
    .card-common {margin-top: 15px;margin-bottom: 0;box-shadow: initial;}
    .card-common .layui-card-body{padding-top: 0;}
    .promotion-view{display:flex;flex-wrap:wrap}
    .promotion-view-item{width:33.3%;padding-right:10px;box-sizing:border-box;line-height:30px}
    .promotion-stat-view{display:flex;flex-wrap:wrap}
    .promotion-stat-view .promotion-stat-item{width:25%;padding:0 15px 10px 15px;box-sizing:border-box}
    .promotion-stat-view .promotion-stat-item .promotion-stat-item-title{color:#909399;font-size:14px;margin-top:5px}
    .promotion-stat-view .promotion-stat-item .promotion-stat-item-value{color:#303133;font-size:26px;margin-top:10px}
    .todo-list .promotion-stat-item{flex:1;width:0;cursor:pointer}
    .layui-layout-admin .layui-body .body-content {background: 0 0;padding: 0;}
    .gift-card-goods span{cursor: pointer; color: var(--base-color);}
    .layui-tab-title{margin-bottom: 15px;}
    .layui-layout-admin .single-filter-box.top {padding-top: 0 !important;}
    .add-way .add-way-item{display: flex;margin: 8px 0;align-items: center;}
    .add-way .add-way-item input{margin: 0 10px;}
    .add-way .add-way-item .layui-form-radio{margin-right: 0;}
    .card-goods .layui-table{margin-bottom: 0;margin-top: 0;}
    #card_right_type_goods #goods{border: 0;}
    .card-goods .layui-table .goods-title{display: flex;align-items: center;}
    .card-goods .layui-table .goods-title .goods-img{width: 55px;height: 55px;line-height: 55px;flex-shrink: 0;margin-right: 10px;}
    .card-goods .layui-table .goods-title .goods-img img{max-width: 100%;max-height: 100%;}
    .card-goods .layui-table-body{overflow: auto;max-height: 425px;margin-bottom: 15px;border-bottom: 1px solid #e6e6e6;}
    .card-goods .layui-table-body .layui-table{border: none;}
    .card-goods .layui-table-body tbody tr:last-of-type td{border: none;}
    .card-goods .layui-table-head tr th:last-of-type{text-align: right;}
    .card-goods .layui-table-body tr td:last-of-type{text-align: right;}
    .card-tab{margin-bottom: 15px;margin-top: 15px;}
    /* 商品列表 */
    .shop-information-table > p{padding-left: 5px;padding-bottom: 5px;}
    .shop-information-table table {width: 100%;border: 1px solid rgb(238,238,238);}
    .shop-information-table .table-body {max-height: 400px;overflow: auto;}
    .table-trOne{height: 48px;background:rgb(245,245,245) ;}
    .shop-information-table th{text-align: left;padding-left:28px;box-sizing: border-box;font-weight: 500;color:#333333;}
    .shop-information-table th:last-child{border:none;}
    .table-trTow{width:100%;height:60px;border-top:1px solid rgb(238,238,238);}
    .table-trTow>td{text-align: left;padding-left:28px;box-sizing: border-box;font-weight: 500;color:#333333;border-right:1px solid rgb(238,238,238);}
    .table-trTow>td:nth-child(5){color:var(--base-color)}
</style>

<div class="layui-card card-common card-brief head top">
	<div class="layui-card-header">
        <span class="card-title">基本信息</span>
	</div>
	<div class="layui-card-body">
		<div class="promotion-view">
			<div class="promotion-view-item">
				<label>礼品卡名称：</label>
				<span>{$detail.card_name}</span>
            </div>
            <div class="promotion-view-item grouping">
                <label>分组：</label>
                <span>{$detail.category_name}</span>
            </div>

            <div class="promotion-view-item grouping">
                <label>卡类型：</label>
                <span>{$detail.card_type_name}</span>
            </div>

            <div class="promotion-view-item">
                <label>权益类型：</label>
                <span>{$detail.card_right_type=='goods' ? '礼品卡' : '储值卡'}</span>
            </div>
            {if $detail.card_right_type=='balance'}
            <div class="promotion-view-item">
                <label>储值余额：</label>
                <span>{$detail.balance}元</span>
            </div>
            {/if}
            {if $detail.card_type=='virtual'}
            <div class="promotion-view-item">
                <label>销售价：</label>
                <span>{$detail.card_price}元</span>
            </div>
            {/if}
            <div class="promotion-view-item">
                <label>有效期：</label>
                <span>
                    {if $detail.validity_type=='forever'}
                    永久有效
                    {elseif $detail.validity_type=='date'/}
                    有效期至{:date('Y-m-d H:i:s',$detail.validity_time)}
                    {elseif $detail.validity_type=='day'/}
                    领取后{$detail.validity_day}天有效
                    {/if}
                </span>
            </div>
            {if $detail.card_type=='virtual'}
            <div class="promotion-view-item">
                <label>是否允许转赠：</label>
                <span>{if $detail.is_allow_transfer == 1}是{else/}否{/if}</span>
            </div>
            {/if}
        </div>
	</div>
</div>

<div class="layui-card card-common card-brief head top">
	<div class="layui-card-header">
        <span class="card-title">数据统计</span>
    </div>
	<div class="layui-card-body">
		<div class="promotion-stat-view todo-list">
            {if $detail.card_type=='virtual'}
            <div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">卡销量</div>
				<div class="promotion-stat-item-value">{$detail.sale_num}</div>
            </div>
            <div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">已使用</div>
				<div class="promotion-stat-item-value">{$detail.use_count}</div>
            </div>
            <div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">未使用</div>
				<div class="promotion-stat-item-value">{$detail.sale_num - $detail.use_count}</div>
            </div>
            {/if}
            {if $detail.card_type=='real'}
			<div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">制卡数</div>
				<div class="promotion-stat-item-value">{$detail.card_count}</div>
            </div>
            <div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">激活数</div>
				<div class="promotion-stat-item-value">{$detail.activate_count}</div>
            </div>
            <div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">已使用</div>
				<div class="promotion-stat-item-value">{$detail.use_count}</div>
            </div>
            <div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">未使用</div>
				<div class="promotion-stat-item-value">{$detail.activate_count - $detail.use_count}</div>
            </div>
            {/if}
		</div>
	</div>
</div>

<div class="layui-card card-common card-brief head top">
	<div class="layui-card-header">
        <span class="card-title">使用规则</span>
    </div>
	<div class="layui-card-body">
        {if $detail.card_right_type=='goods'}
            {if $detail.card_right_goods_type == 'all'}
            <p>礼品卡持卡人兑换时可从以下商品列表中任选{$detail.card_right_goods_count}件。</p>
            {else/}
            <p>礼品卡持卡人兑换时可从按照商品列表中商品数量进行兑换。</p>
            {/if}
        {else/}
            <p>礼品卡持卡人兑换时将储值卡的储值余额充值到持卡人账户余额中</p>
        {/if}
	</div>
</div>

<!-- 商品信息 -->
{if $detail['card_right_type'] == 'goods'}
<div class="layui-card card-common card-brief head top">
	<div class="layui-card-header">
        <span class="card-title">商品信息</span>
	</div>
	<div class="layui-card-body shop-information-table">
        <div class="table-head">
            <table lay-skin="line">
                <colgroup>
                    <col width="80%">
                    {if $detail.card_right_goods_type == 'all'}
                    <col width="20%">
                    {/if}
                    {if $detail.card_right_goods_type != 'all'}
                    <col width="10%">
                    <col width="10%">
                    {/if}
                    <col>
                </colgroup>
                <thead>
                    <tr class="table-trOne">
                        <th lay-data="{field:'product_name', width:200}">商品</th>
                        <th lay-data="{field:'price'}">价格</th>
                        {if $detail.card_right_goods_type != 'all'}
                        <th lay-data="{field:'sale_num'}">数量</th>
                        {/if}
                    </tr>
                </thead>
            </table>
        </div>
        <div class="table-body">
            <table lay-skin="line">
                <colgroup>
                    <col width="80%">
                    {if $detail.card_right_goods_type == 'all'}
                    <col width="20%">
                    {/if}
                    {if $detail.card_right_goods_type != 'all'}
                    <col width="10%">
                    <col width="10%">
                    {/if}
                    <col>
                </colgroup>
                <tbody>
                    {foreach $detail['goods_list'] as $list_k => $order_goods_item}
                    <tr class="table-trTow">
                        <td>{$order_goods_item.sku_info ? $order_goods_item.sku_info.sku_name : ''}</td>
                        <td>{$order_goods_item.sku_info ? $order_goods_item.sku_info.price : ''}</td>
                        {if $detail.card_right_goods_type != 'all'}
                        <td>{$order_goods_item.goods_num}</td>
                        {/if}
                    </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
	</div>
</div>
{/if}

<div class="layui-card card-common card-brief head top">
	<div class="layui-card-header">
        <span class="card-title">礼品卡列表</span>
	</div>
	<div class="layui-card-body">
        <div class="screen layui-collapse card-tab" lay-filter="card_tab">
            <div class="layui-colla-item">
                <form class="layui-colla-content layui-form layui-show">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">礼品卡编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="search_text" placeholder="请输入礼品卡编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="layui-tab table-tab" lay-filter="card_state_tab">
            <ul class="layui-tab-title">
                <li class="layui-this" data-status="all">全部</li>
                {foreach $status_list as $k => $v}
                <li data-status="{$k}">{$v}</li>
                {/foreach}
            </ul>
            <div class="layui-tab-content poster_list">
                <!-- 列表 -->
                <table id="poster_list" lay-filter="poster_list"></table>
            </div>
        </div>
	</div>
</div>

<script type='text/html' id="promotion_list_item_box_html">
	<div class="promotion-list-item-title">
		<div class="promotion-list-item-title-icon">
			<img src="{{ ns.img(d.sku_image) }}" alt="">
		</div>
		<p class="promotion-list-item-title-name multi-line-hiding">{{ d.sku_name }}</p>
	</div>
</script>
<script type="text/html" id="operation">
    <div class="table-btn">
        {{# if(d.status == 'to_activate'){  }}
        <a class="layui-btn" lay-event="delete">删除</a>
        {{# } }}
        {{# if(d.status != 'to_activate'){  }}
        <a class="layui-btn" lay-event="info">详情</a>
        {{# } }}
    </div>
</script>
<script>
var categoryList = {:json_encode($category_list)} ? {:json_encode($category_list)} : [];
var category_id = {$detail.category_id};
var giftcard_id = {$detail.giftcard_id};
categoryList.forEach(item => {
    if(item.category_id == category_id){
        $(".grouping span").text(item.name);
    }
});

var table,element,form,laytpl,repeat_flag=false;
layui.use(['form', 'element', 'laytpl','laydate'], function () {
    laytpl = layui.laytpl;
    element = layui.element;
    form = layui.form;
    form.render();

    element.on('tab(card_state_tab)', function () {
        table.reload({
            page: {
                curr: 1
            },
            where: {
                'status': this.getAttribute('data-status')
            }
        });
    });

    /**
     * 搜索功能
     */
    form.on('submit(search)', function (data) {
        table.reload({
            page: {curr: 1},
            where: data.field
        });
    });
  
    table = new Table({
        elem: '#poster_list',
        url: ns.url("giftcard://shop/card/lists", {'giftcard_id': giftcard_id}),
        cols: [
            {if $detail.card_type=='virtual'}
                [{
                field: 'card_no',
                title: '卡号',
                unresize: 'false',
                width: '20%',
            }, {
                field: 'member_nickname',
                title: '所属会员',
                unresize: 'false',
                width: '15%',
                templet: function (data) {
                    var html = `<a class="text-color" href="${ns.href('shop/member/editmember',{'member_id':data.member_id})}" target="_blank">${data.member_nickname || '--'}</a>`
                    return html;
                }
            }, {
                title: '获取时间',
                unresize: 'false',
                width: '15%',
                templet: function (data) {
                    return ns.time_to_date(data.create_time);
                }
            }, {
                field: 'status_name',
                title: '状态',
                unresize: 'false',
                width: '15%'
            }, {
                field: 'order_no',
                title: '订单',
                unresize: 'false',
                width: '15%',
                templet: function (data) {
                    var html = `<a class="text-color" href="${ns.href('giftcard://shop/order/detail',{'order_id':data.order_id})}" target="_blank">${data.order_no}</a>`
                    return html;
                }
            },{
                title: '操作',
                toolbar: '#operation',
                unresize: 'false',
                align : 'right'
            }]
            {else /}
            [{
                field: 'card_no',
                title: '卡号',
                unresize: 'false',
                width: '20%',
            },{
                field: 'card_cdk',
                title: '秘钥',
                unresize: 'false',
                width: '20%',
            }, {
                title: '生成时间',
                unresize: 'false',
                width: '15%',
                templet: function (data) {
                    return ns.time_to_date(data.create_time);
                }
            }, {
                field: 'member_nickname',
                title: '激活会员',
                unresize: 'false',
                width: '10%',
                templet: function (data) {
                    var html;
                    if(data.member_id)
                        html = `<a class="text-color" href="${ns.href('shop/member/editmember',{'member_id':data.member_id})}" target="_blank">${data.member_nickname}</a>`;
                    else
                        html = `<a>--</a>`;
                    return html;
                }
            }, {
                field: 'sort',
                unresize: 'false',
                title: '激活时间',
                width: '15%',
                templet: function (data) {
                    if(data.activate_time == 0)
                        return "--";
                    else
                        return ns.time_to_date(data.activate_time);
                }
            }, {
                field: 'status_name',
                title: '状态',
                unresize: 'false',
                width: '10%'
            },{
                title: '操作',
                toolbar: '#operation',
                unresize: 'false',
                align : 'right'
            }]
            {/if}
        ]
    });

    /**
     * 监听工具栏操作
     */
    table.tool(function (obj) {
        var data = obj.data;
        switch (obj.event) {
            case 'delete': //删除
                deleteCard(data.card_id);
                break;
            case 'info':
                window.open(ns.href("giftcard://shop/card/detail",{card_id:data.card_id}))
                break;
        }
    });

    /**
     * 删除
     */
    function deleteCard(id) {
        layer.confirm('确定要删除该卡项记录吗?', function (index) {
            if (repeat_flag) return;
            repeat_flag = true;
			layer.close(index);

            $.ajax({
                url: ns.url("giftcard://shop/card/delete"),
                data: {
                    card_id: id
                },
                dataType: 'JSON',
                type: 'POST',
                success: function (res) {
                    layer.msg(res.message);
                    repeat_flag = false;
                    if (res.code == 0) {
                        table.reload({
                            page: {
                                curr: 1
                            },
                        });
                    }
                }
            });
        }, function () {
            layer.close();
            repeat_flag = false;
        });
    }
})
</script>