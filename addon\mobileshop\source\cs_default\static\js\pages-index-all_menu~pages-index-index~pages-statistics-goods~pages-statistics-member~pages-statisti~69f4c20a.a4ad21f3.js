(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-all_menu~pages-index-index~pages-statistics-goods~pages-statistics-member~pages-statisti~69f4c20a"],{"049f":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.canvasId?i("v-uni-canvas",{staticClass:"canvas",style:{width:(t.show?t.cWidth:0)*t.pixelRatio+"px",height:(t.show?t.cHeight:0)*t.pixelRatio+"px",overflow:"hidden",transform:"scale("+1/t.pixelRatio+")","margin-left":-t.cWidth*(t.pixelRatio-1)/2+"px","margin-top":-t.cHeight*(t.pixelRatio-1)/2+"px"},attrs:{id:t.canvasId,canvasId:t.canvasId},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)}}}):t._e()},o=[]},1851:function(t,e,i){"use strict";var a=i("8bdb"),o=i("84d6"),n=i("1cb5");a({target:"Array",proto:!0},{fill:o}),n("fill")},"31c6":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("aa9c");var o=a(i("604b")),n={showCharts:function(t,e,i){return new o.default({$this:i,canvasId:t,width:e.width||i.cWidth||uni.upx2px(750)*(e.pixelRatio||i.pixelRatio||1),height:e.height||i.cHeight||uni.upx2px(500)*(e.pixelRatio||i.pixelRatio||1),type:e.type,pixelRatio:e.pixelRatio||i.pixelRatio||1,rotate:e.rotate||!1,rotateLock:e.rotateLock||!1,fontSize:e.fontSize||11,background:e.background||"#FFFFFF",enableScroll:e.enableScroll||!1,enableMarkLine:e.enableMarkLine||!1,animation:e.animation||!1,legend:e.legend,dataLabel:e.dataLabel||!0,dataPointShape:e.dataPointShape||!0,disablePieStroke:e.disablePieStroke||!1,categories:e.categories||("gauge"==e.type?[{value:.2,color:"#2fc25b"},{value:.8,color:"#f04864"},{value:1,color:"#1890ff"}]:[]),series:e.series||[],title:e.title||{name:"arcbar"==e.type||"gauge"==e.type?Math.round(100*e.series[0].data)+"%":"",fontSize:e.titlefontSize||11,color:e.titlecolor||("arcbar"==e.type||"gauge"==e.type?e.series[0].color:"#333333"),offsetX:e.titleoffsetX||0,offsetY:e.titleoffsetY||0},subtitle:e.subtitle||{name:"arcbar"==e.type||"gauge"==e.type?e.series[0].name:"",offsetX:e.subtitleoffsetX||0,offsetY:e.subtitleoffsetY||0,fontSize:e.subtitlefontSize||11,color:e.subtitlecolor||"#666666"},xAxis:e.xAxis||{rotateLabel:e.rotateLabel||!0,itemCount:e.itemCount||5,labelCount:e.labelCount,scrollShow:e.scrollShow||!0,scrollAlign:e.scrollAlign||"right",scrollBackgroundColor:e.scrollBackgroundColor||"#EFEBEF",scrollColor:e.scrollColor||"#A6A6A6",disabled:e.xAxisdisabled||!1,disableGrid:e.xAxisdisableGrid||!1,type:e.xAxistype||"calibration",gridColor:e.xAxisgridColor||"#cccccc",gridType:e.xAxisgridType||"solid",dashLength:e.xAxisdashLength||4,fontColor:e.xAxisfontColor||"#666666"},yAxis:e.yAxis||{format:e.yAxisformat||function(t){return t.toFixed(e.fixed||0)+(e.unit||"")},min:e.yAxismin,max:e.yAxismax,title:e.yAxistitle||"",disabled:e.yAxisdisabled||!1,disableGrid:e.yAxisdisableGrid||!1,splitNumber:e.yAxissplitNumber||5,gridType:e.yAxisgridType||"dash",dashLength:e.yAxisdashLength||4,gridColor:e.yAxisgridColor||"#cccccc",fontColor:e.yAxisfontColor||"#666666",titleFontColor:e.yAxistitleFontColor||"#333333"},extra:e.extra||{arcbar:e.arcbar||{type:e.extratype||"default",width:(e.extraWidth||i.arcbarWidth||uni.upx2px(12))*(e.pixelRatio||i.pixelRatio||1),backgroundColor:e.backgroundColor||"#ffe3e8",startAngle:e.startAngle||1.25,endAngle:e.endAngle||.75},gauge:e.gauge||{type:e.extratype||"default",width:(e.extraWidth||uni.upx2px(30))*(e.pixelRatio||i.pixelRatio||1),labelColor:e.labelColor||"#666666",startAngle:e.startAngle||.75,endAngle:e.endAngle||.25,startNumber:e.startNumber||0,endNumber:e.endNumber||100,splitLine:e.splitLine||{fixRadius:e.fixRadius||0,splitNumber:e.splitNumber||10,width:e.splitLinewidth||uni.upx2px(30)*(e.pixelRatio||i.pixelRatio||1),color:e.splitLinecolor||"#FFFFFF",childNumber:e.childNumber||5,childWidth:e.childWidth||.4*uni.upx2px(30)*(e.pixelRatio||i.pixelRatio||1)},pointer:e.pointer||{width:e.pointerwidth||.8*uni.upx2px(30)*(e.pixelRatio||i.pixelRatio||1),color:e.pointercolor||"auto"}},radar:e.radar||{max:e.max||200,labelColor:e.labelColor||"#666666",gridColor:e.gridColor||"#cccccc"},column:e.column||{type:e.extratype||"group",width:(e.width||i.cWidth||uni.upx2px(750))*(e.pixelRatio||i.pixelRatio||1)*.45/("column"==e.type?e.categories.length:5),meter:e.meter||{border:e.border||3,fillColor:e.fillColor||"#E5FDC3"}},pie:e.pie||{activeOpacity:e.activeOpacity||.5,offsetAngle:e.offsetAngle||0,lableWidth:e.lableWidth||15,ringWidth:e.ringWidth||30*(e.pixelRatio||i.pixelRatio||1)},rose:e.rose||{type:e.extratype||"area",minRadius:e.minRadius||50,activeOpacity:e.activeOpacity||.5,offsetAngle:e.offsetAngle||0,lableWidth:e.lableWidth||15},line:e.line||{type:e.extratype||"straight",width:e.extrawidth||2},area:e.area||{type:e.extratype||"straight",opacity:e.opacity||.2,addLine:e.addLine||!1,width:e.extrawidth||2},candle:e.candle||{},bar:e.bar||{type:e.extratype||"group",width:e.extrawidth},markLine:e.markLine||{},tooltip:e.tooltip||{},legendTextColor:e.legendTextColor||"#333333",touchMoveLimit:e.touchMoveLimit||20}})},start:function(t,e){e.scrollStart(t)},move:function(t,e){e.scroll(t)},end:function(t,e){e.scrollEnd(t)},tip:function(t,e){e.showToolTip(t,{format:function(t,e){return(e||"")+" "+t.name+":"+(t.data.value||t.data)}})},touchY:function(t,e){var i=t.changedTouches?t.changedTouches[0].y:t.mp.changedTouches[0].y;return t.changedTouches?t.changedTouches[0].y=i<0?i+e:i:t.mp.changedTouches[0].y=i<0?i+e:i,t},sumArr:function(t){var e=this;t.forEach((function(t){t.table.ts?(t.opts=t.table.ts,e.sumObj(t)):(t.opts=t.table[0],e.sumArray(t))}))},sumObj:function(t){"pie"==t.chartType?(t.table.ts&&t.table.ts.series.forEach((function(e){void 0!=t.table.ts.sum&&(t.table.ts.sum+=e.data)})),t.table.ts&&t.table.ts.series.forEach((function(e){t.table.ts.zb&&t.table.ts.zb.push((100*e.data/t.table.ts.sum).toFixed(2))})),t.table.mj&&t.table.mj.series.forEach((function(e){void 0!=t.table.mj.sum&&(t.table.mj.sum+=e.data)})),t.table.je&&t.table.je.series.forEach((function(e){void 0!=t.table.je.sum&&(t.table.je.sum+=e.data)}))):"column"==t.chartType&&(t.table.ts&&t.table.ts.series[0].data.forEach((function(e,i){void 0!=t.table.ts.sum&&(t.table.ts.sum+=e.value||e),void 0!=t.table.ts.sum1&&(t.table.ts.sum1+=t.table.ts.series[1].data[i].value||t.table.ts.series[1].data[i]),void 0!=t.table.ts.sum2&&(t.table.ts.sum2+=t.table.ts.series[2].data[i].value||t.table.ts.series[2].data[i]),t.table.ts.ce&&t.table.ts.ce.push(e.value?1*t.table.ts.series[1].data[i].value-1*e.value:1*t.table.ts.series[1].data[i]-1*e),t.table.ts.zb&&t.table.ts.zb.push((e.value?100*e.value/t.table.ts.series[1].data[i].value:100*e/t.table.ts.series[1].data[i]).toFixed(2))})),t.table.ts.ce&&t.table.ts.ce.push(t.table.ts.sum1-t.table.ts.sum),t.table.ts.zb&&t.table.ts.zb.push((100*t.table.ts.sum/t.table.ts.sum1).toFixed(2)),t.table.mj&&t.table.mj.series[0].data.forEach((function(e){void 0!=t.table.mj.sum&&(t.table.mj.sum+=e.value||e)})),t.table.je&&t.table.je.series[0].data.forEach((function(e){void 0!=t.table.je.sum&&(t.table.je.sum+=e.value||e)})))},sumArray:function(t){"pie"==t.chartType?(t.table[0]&&t.table[0].series.forEach((function(e){void 0!=t.table[0].sum&&(t.table[0].sum+=e.data)})),t.table[0]&&t.table[0].series.forEach((function(e){t.table[0].zb&&t.table[0].zb.push((100*e.data/t.table[0].sum).toFixed(2))})),t.table[1]&&t.table[1].series.forEach((function(e){void 0!=t.table[1].sum&&(t.table[1].sum+=e.data)})),t.table[2]&&t.table[2].series.forEach((function(e){void 0!=t.table[2].sum&&(t.table[2].sum+=e.data)})),t.table[3]&&t.table[3].series.forEach((function(e){void 0!=t.table[3].sum&&(t.table[3].sum+=e.data)})),t.table[3]&&(t.table[3].sum=t.table[3].sum/t.table[0].series.length)):"column"==t.chartType&&(t.table[0]&&t.table[0].series[0].data.forEach((function(e,i){void 0!=t.table[0].sum&&(t.table[0].sum+=e.value||e),void 0!=t.table[0].sum1&&(t.table[0].sum1+=t.table[0].series[1].data[i].value||t.table[0].series[1].data[i]),void 0!=t.table[0].sum2&&(t.table[0].sum2+=t.table[0].series[2].data[i].value||t.table[0].series[2].data[i]),t.table[0].ce&&t.table[0].ce.push(e.value?1*t.table[0].series[1].data[i].value-1*e.value:1*t.table[0].series[1].data[i]-1*e),t.table[0].zb&&t.table[0].zb.push((e.value?100*e.value/t.table[0].series[1].data[i].value:100*e/t.table[0].series[1].data[i]).toFixed(2))})),t.table[0].ce&&t.table[0].ce.push(t.table[0].sum1-t.table[0].sum),t.table[0].zb&&t.table[0].zb.push((100*t.table[0].sum/t.table[0].sum1).toFixed(2)),t.table[1]&&t.table[1].series[0].data.forEach((function(e){void 0!=t.table[1].sum&&(t.table[1].sum+=e.value||e)})),t.table[2]&&t.table[2].series[0].data.forEach((function(e){void 0!=t.table[2].sum&&(t.table[2].sum+=e.value||e)})))}},r=n;e.default=r},"604b":function(t,e,i){"use strict";var a=i("bdbb").default;i("7a76"),i("c9b5"),i("5c47"),i("a1c1"),i("e966"),i("aa9c"),i("fd3c"),i("0506"),i("473f"),i("bf0f"),i("c223"),i("8f71"),i("2797"),i("dc69"),i("64aa"),i("1851"),i("f7a5");var o={yAxisWidth:15,yAxisSplit:5,xAxisHeight:20,xAxisLineHeight:15,legendHeight:15,yAxisTitleWidth:15,padding:10,pixelRatio:1,rotate:!1,columePadding:3,fontSize:13,dataPointShape:["circle","circle","circle","circle"],colors:["#1890ff","#2fc25b","#facc14","#f04864","#8543e0","#90ed7d","#5caf20","#30b0d7","#ff6d58","#1A79B9","#f97723","#f3792c"],pieChartLinePadding:15,pieChartTextPadding:5,xAxisTextPadding:3,titleColor:"#333333",titleFontSize:20,subtitleColor:"#999999",subtitleFontSize:15,toolTipPadding:3,toolTipBackground:"#000000",toolTipOpacity:.7,toolTipLineHeight:20,radarGridCount:3,radarLabelTextMargin:15,gaugeLabelTextMargin:15};function n(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var i=Object(t),a=1;a<arguments.length;a++){var o=arguments[a];if(null!=o)for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(i[n]=o[n])}return i}var r={toFixed:function(t,e){return e=e||2,this.isFloat(t)&&(t=t.toFixed(e)),t},isFloat:function(t){return t%1!==0},approximatelyEqual:function(t,e){return Math.abs(t-e)<1e-10},isSameSign:function(t,e){return Math.abs(t)===t&&Math.abs(e)===e||Math.abs(t)!==t&&Math.abs(e)!==e},isSameXCoordinateArea:function(t,e){return this.isSameSign(t.x,e.x)},isCollision:function(t,e){t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height;var i=e.start.x>t.end.x||e.end.x<t.start.x||e.end.y>t.start.y||e.start.y<t.end.y;return!i}};function l(t,e){var i=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,i,a){return e+e+i+i+a+a})),a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i),o=parseInt(a[1],16),n=parseInt(a[2],16),r=parseInt(a[3],16);return"rgba("+o+","+n+","+r+","+e+")"}function s(t,e,i){if(isNaN(t))throw new Error("[wxCharts] unvalid series data!");i=i||10,e=e||"upper";var a=1;while(i<1)i*=10,a*=10;t="upper"===e?Math.ceil(t*a):Math.floor(t*a);while(t%i!==0)"upper"===e?t++:t--;return t/a}function h(t,e,i){function a(t){while(t<0)t+=2*Math.PI;while(t>2*Math.PI)t-=2*Math.PI;return t}return t=a(t),e=a(e),i=a(i),e>i&&(i+=2*Math.PI,t<e&&(t+=2*Math.PI)),t>=e&&t<=i}function c(t,e){function i(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}var a=.2,o=.2,n=null,r=null,l=null,s=null;if(e<1?(n=t[0].x+(t[1].x-t[0].x)*a,r=t[0].y+(t[1].y-t[0].y)*a):(n=t[e].x+(t[e+1].x-t[e-1].x)*a,r=t[e].y+(t[e+1].y-t[e-1].y)*a),e>t.length-3){var h=t.length-1;l=t[h].x-(t[h].x-t[h-1].x)*o,s=t[h].y-(t[h].y-t[h-1].y)*o}else l=t[e+1].x-(t[e+2].x-t[e].x)*o,s=t[e+1].y-(t[e+2].y-t[e].y)*o;return i(t,e+1)&&(s=t[e+1].y),i(t,e)&&(r=t[e].y),{ctrA:{x:n,y:r},ctrB:{x:l,y:s}}}function d(t,e,i){return{x:i.x+t,y:i.y-e}}function p(t,e){if(e)while(r.isCollision(t,e))t.start.x>0?t.start.y--:t.start.x<0||t.start.y>0?t.start.y++:t.start.y--;return t}function u(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.fontSize;t=String(t);t=t.split("");for(var i=0,a=0;a<t.length;a++){var n=t[a];/[a-zA-Z]/.test(n)?i+=7:/[0-9]/.test(n)?i+=5.5:/\./.test(n)?i+=2.7:/-/.test(n)?i+=3.25:/[\u4e00-\u9fa5]/.test(n)?i+=10:/\(|\)/.test(n)?i+=3.73:/\s/.test(n)?i+=2.5:/%/.test(n)?i+=8:i+=10}return i*e/10}function x(t){return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data)}),[])}function f(t,e,i){var a,o;return t.clientX?e.rotate?(o=e.height-t.clientX*e.pixelRatio,a=(t.pageY-i.currentTarget.offsetTop-e.height/e.pixelRatio/2*(e.pixelRatio-1))*e.pixelRatio):(a=t.clientX*e.pixelRatio,o=(t.pageY-i.currentTarget.offsetTop-e.height/e.pixelRatio/2*(e.pixelRatio-1))*e.pixelRatio):e.rotate?(o=e.height-t.x*e.pixelRatio,a=t.y*e.pixelRatio):(a=t.x*e.pixelRatio,o=t.y*e.pixelRatio),{x:a,y:o}}function g(t,e){for(var i=[],a=0;a<t.length;a++){var o=t[a];if(null!==o.data[e]&&"undefined"!==typeof o.data[e]){var n={};n.color=o.color,n.type=o.type,n.style=o.style,n.shape=o.shape,n.disableLegend=o.disableLegend,n.name=o.name,n.data=o.format?o.format(o.data[e]):o.data[e],i.push(n)}}return i}function y(t){var e=t.map((function(t){return u(t)}));return Math.max.apply(null,e)}function v(t){for(var e=2*Math.PI/t,i=[],a=0;a<t;a++)i.push(e*a);return i.map((function(t){return-1*t+Math.PI/2}))}function b(t,e,i,a,o){for(var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=t.map((function(t){return{text:n.format?n.format(t):t.name+" : "+t.data,color:t.color,time:o.series[0].time[i]}})),l=[],s={x:0,y:0},h=0;h<e.length;h++){var c=e[h];"undefined"!==typeof c[i]&&null!==c[i]&&l.push(c[i])}for(var d=0;d<l.length;d++){var p=l[d];s.x=Math.round(p.x),s.y+=p.y}return s.y/=l.length,{textList:r,offset:s}}function m(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},n=t.map((function(t){return{text:o.format?o.format(t,a[i]):t.name+": "+t.data,color:t.color,disableLegend:!!t.disableLegend}}));n=n.filter((function(t){if(!0!==t.disableLegend)return t}));for(var r=[],l={x:0,y:0},s=0;s<e.length;s++){var h=e[s];"undefined"!==typeof h[i]&&null!==h[i]&&r.push(h[i])}for(var c=0;c<r.length;c++){var d=r[c];l.x=Math.round(d.x),l.y+=d.y}return l.y/=r.length,{textList:n,offset:l}}function A(t,e,i,a,o,n){var r=n.color.upFill,l=n.color.downFill,s=[r,r,l,r],h=[],c={text:o[a],color:null};h.push(c),e.map((function(e){0==a&&e.data[1]-e.data[0]<0?s[1]=l:(e.data[0]<t[a-1][1]&&(s[0]=l),e.data[1]<e.data[0]&&(s[1]=l),e.data[2]>t[a-1][1]&&(s[2]=r),e.data[3]<t[a-1][1]&&(s[3]=l));var i={text:"开盘："+e.data[0],color:s[0]},o={text:"收盘："+e.data[1],color:s[1]},n={text:"最低："+e.data[2],color:s[2]},c={text:"最高："+e.data[3],color:s[3]};h.push(i,o,n,c)}));for(var d=[],p={x:0,y:0},u=0;u<i.length;u++){var x=i[u];"undefined"!==typeof x[a]&&null!==x[a]&&d.push(x[a])}return p.x=Math.round(d[0][0].x),{textList:h,offset:p}}function T(t,e,i){return t.x<e.width-i.padding&&t.x>i.padding+i.yAxisWidth+i.yAxisTitleWidth&&t.y>i.padding&&t.y<e.height-i.legendHeight-i.xAxisHeight-i.padding}function P(t,e,i){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<=Math.pow(i,2)}function S(t){var e=[],i=[];return t.forEach((function(t,a){null!==t?i.push(t):(i.length&&e.push(i),i=[])})),i.length&&e.push(i),e}function _(t,e,i){if(!1===e.legend)return{legendList:[],legendHeight:0};for(var a=5*e.pixelRatio,o=8*e.pixelRatio,n=15*e.pixelRatio,r=[],l=0,s=[],h=0;h<t.length;h++){var c=t[h],d=3*a+n+u(c.name||"undefined");l+d>e.width?(r.push(s),l=d,s=[c]):(l+=d,s.push(c))}return s.length&&r.push(s),{legendList:r,legendHeight:r.length*(i.fontSize+o)+a}}function L(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=o.extra.radar||{};r.max=r.max||0;for(var l=Math.max(r.max,Math.max.apply(null,x(a))),s=[],h=function(o){var r=a[o],h={};h.color=r.color,h.data=[],r.data.forEach((function(a,o){var r={};r.angle=t[o],r.proportion=a/l,r.position=d(i*r.proportion*n*Math.cos(r.angle),i*r.proportion*n*Math.sin(r.angle),e),h.data.push(r)})),s.push(h)},c=0;c<a.length;c++)h(c);return s}function w(t,e){for(var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,a=0,o=0,n=0;n<t.length;n++){var r=t[n];r.data=null===r.data?0:r.data,a+=r.data}for(var l=0;l<t.length;l++){var s=t[l];s.data=null===s.data?0:s.data,s._proportion_=0===a?1/t.length*i:s.data/a*i,s._radius_=e}for(var h=0;h<t.length;h++){var c=t[h];c._start_=o,o+=2*c._proportion_*Math.PI}return t}function M(t,e,i,a){for(var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=0,r=0,l=[],s=0;s<t.length;s++){var h=t[s];h.data=null===h.data?0:h.data,n+=h.data,l.push(h.data)}for(var c=l.pop(),d=l.shift(),p=a-i,u=0;u<t.length;u++){var x=t[u];x.data=null===x.data?0:x.data,x._proportion_=0===n||"area"==e?1/t.length*o:x.data/n*o,x._radius_=i+p*((x.data-c)/(d-c))}for(var f=0;f<t.length;f++){var g=t[f];g._start_=r,r+=2*g._proportion_*Math.PI}return t}function F(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(var a=0;a<t.length;a++){var o=t[a];o.data=null===o.data?0:o.data;var n=void 0;n="default"==e.type?e.startAngle-e.endAngle+1:2,o._proportion_=n*o.data*i+e.startAngle,o._proportion_>=2&&(o._proportion_=o._proportion_%2)}return t}function R(t,e,i){for(var a=e-i+1,o=e,n=0;n<t.length;n++)t[n].value=null===t[n].value?0:t[n].value,t[n]._startAngle_=o,t[n]._endAngle_=a*t[n].value+e,t[n]._endAngle_>=2&&(t[n]._endAngle_=t[n]._endAngle_%2),o=t[n]._endAngle_;return t}function C(t,e,i){for(var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=0;o<t.length;o++){var n=t[o];if(n.data=null===n.data?0:n.data,"auto"==i.pointer.color){for(var r=0;r<e.length;r++)if(n.data<=e[r].value){n.color=e[r].color;break}}else n.color=i.pointer.color;var l=i.startAngle-i.endAngle+1;n._endAngle_=l*n.data+i.startAngle,n._oldAngle_=i.oldAngle,i.oldAngle<i.endAngle&&(n._oldAngle_+=2),n.data>=i.oldData?n._proportion_=(n._endAngle_-n._oldAngle_)*a+i.oldAngle:n._proportion_=n._oldAngle_-(n._oldAngle_-n._endAngle_)*a,n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return t}function k(t,e,i,a,o,n){return t.map((function(t){return null===t?null:(t.width=(e-2*o.columePadding)/i,n.extra.column&&n.extra.column.width&&+n.extra.column.width>0?t.width=Math.min(t.width,+n.extra.column.width):t.width=Math.min(t.width,25),t.x+=(a+.5-i/2)*t.width,t)}))}function E(t,e,i,a,o,n,r){return t.map((function(t){return null===t?null:(t.width=e-2*o.columePadding,n.extra.column&&n.extra.column.width&&+n.extra.column.width>0?t.width=Math.min(t.width,+n.extra.column.width):t.width=Math.min(t.width,25),a>0&&(t.width-=2*r),t)}))}function z(t,e,i,a,o,n,r){return t.map((function(t,i){return null===t?null:(t.width=e-2*o.columePadding,n.extra.column&&n.extra.column.width&&+n.extra.column.width>0?t.width=Math.min(t.width,+n.extra.column.width):t.width=Math.min(t.width,25),t)}))}function I(t,e,i){var a=i.yAxisWidth+i.yAxisTitleWidth,o=e.width-2*i.padding-a,n=e.enableScroll?Math.min(e.xAxis.itemCount,t.length):t.length,r=o/n,l=[],s=i.padding+a,h=e.width-i.padding;return t.forEach((function(t,e){l.push(s+e*r)})),!0===e.enableScroll?l.push(s+t.length*r):l.push(h),{xAxisPoints:l,startX:s,endX:h,eachSpacing:r}}function D(t,e,i,a,o,n,r){var l=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,s=[],h=n.height-2*r.padding-r.xAxisHeight-r.legendHeight;return t.forEach((function(t,c){if(null===t)s.push(null);else{var d=[];t.forEach((function(t,s){var p={};p.x=a[c]+Math.round(o/2);var u=t.value||t,x=h*(u-e)/(i-e);x*=l,p.y=n.height-r.xAxisHeight-r.legendHeight-Math.round(x)-r.padding,d.push(p)})),s.push(d)}})),s}function H(t,e,i,o,n,r,l){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,h=[],c=r.height-2*l.padding-l.xAxisHeight-l.legendHeight;return t.forEach((function(t,d){if(null===t)h.push(null);else{var p={};p.color=t.color,p.x=o[d]+Math.round(n/2);var u=t;"object"===a(t)&&null!==t&&(u=t.value);var x=c*(u-e)/(i-e);x*=s,p.y=r.height-l.xAxisHeight-l.legendHeight-Math.round(x)-l.padding,h.push(p)}})),h}function W(t,e,i,a,o,n,r,l,s){var h=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,c=[],d=n.height-2*r.padding-r.xAxisHeight-r.legendHeight;return t.forEach((function(t,p){if(null===t)c.push(null);else{var u={};if(u.color=t.color,u.x=a[p]+Math.round(o/2),l>0){for(var x=0,f=0;f<=l;f++)x+=s[f].data[p];var g=x-t,y=d*(x-e)/(i-e),v=d*(g-e)/(i-e)}else x=t,y=d*(x-e)/(i-e),v=0;var b=v;y*=h,b*=h,u.y=n.height-r.xAxisHeight-r.legendHeight-Math.round(y)-r.padding,u.y0=n.height-r.xAxisHeight-r.legendHeight-Math.round(b)-r.padding,c.push(u)}})),c}function O(t,e,i,o){var n;n="stack"==o?function(t){for(var e=new Array(t[0].data.length),i=0;i<e.length;i++)e[i]=0;for(var a=0;a<t.length;a++)for(i=0;i<e.length;i++)e[i]+=t[a].data[i];return t.reduce((function(t,i){return(t.data?t.data:t).concat(i.data).concat(e)}),[])}(t):x(t);var r=[];n=n.filter((function(t){return"object"===a(t)&&null!==t?t.constructor==Array?null!==t:null!==t.value:null!==t})),n.map((function(t){"object"===a(t)?t.constructor==Array?t.map((function(t){r.push(t)})):r.push(t.value):r.push(t)}));var l=0,h=0;if(r.length>0&&(l=Math.min.apply(this,r),h=Math.max.apply(this,r)),"number"===typeof e.yAxis.min&&(l=Math.min(e.yAxis.min,l)),"number"===typeof e.yAxis.max&&(h=Math.max(e.yAxis.max,h)),l===h){var c=h||10;h+=c}for(var d=function(t,e){var i=0,a=e-t;return i=a>=1e4?1e3:a>=1e3?100:a>=100?10:a>=10?5:a>=1?1:a>=.1?.1:.01,{minRange:s(t,"lower",i),maxRange:s(e,"upper",i)}}(l,h),p=d.minRange,u=d.maxRange,f=[],g=(u-p)/i.yAxisSplit,y=0;y<=i.yAxisSplit;y++)f.push(p+g*y);return f.reverse()}function B(t,e,i){var a=n({},e.extra.column||{type:""}),o=O(t,e,i,a.type),l=i.yAxisWidth,s=o.map((function(t){return t=r.toFixed(t,2),t=e.yAxis.format?e.yAxis.format(Number(t)):t,l=Math.max(l,u(t)+5),t}));return!0===e.yAxis.disabled&&(l=0),{rangesFormat:s,ranges:o,yAxisWidth:l}}function N(t,e){!0!==e.rotateLock?(t.translate(e.height,0),t.rotate(90*Math.PI/180)):!0!==e._rotate_&&(t.translate(e.height,0),t.rotate(90*Math.PI/180),e._rotate_=!0)}function X(t,e,i,a,o){a.beginPath(),a.setStrokeStyle("#ffffff"),a.setLineWidth(1*o.pixelRatio),a.setFillStyle(e),"diamond"===i?t.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))})):"circle"===i?t.forEach((function(t,e){null!==t&&(a.moveTo(t.x*****o.pixelRatio,t.y),a.arc(t.x,t.y,4*o.pixelRatio,0,2*Math.PI,!1))})):"rect"===i?t.forEach((function(t,e){null!==t&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))})):"triangle"===i&&t.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))})),a.closePath(),a.fill(),a.stroke()}function j(t,e,i){var a=t.title.fontSize||e.titleFontSize,o=t.subtitle.fontSize||e.subtitleFontSize,n=t.title.name||"",r=t.subtitle.name||"",l=t.title.color||e.titleColor,s=t.subtitle.color||e.subtitleColor,h=n?a:0,c=r?o:0;if(r){var d=u(r,o),p=(t.width-d)/2+(t.subtitle.offsetX||0),x=(t.height-e.legendHeight+o)/2+(t.subtitle.offsetY||0);n&&(x-=(h+5)/2),i.beginPath(),i.setFontSize(o),i.setFillStyle(s),i.fillText(r,p,x),i.closePath(),i.stroke()}if(n){var f=u(n,a),g=(t.width-f)/2+(t.title.offsetX||0),y=(t.height-e.legendHeight+a)/2+(t.title.offsetY||0);r&&(y+=(c+5)/2),i.beginPath(),i.setFontSize(a),i.setFillStyle(l),i.fillText(n,g,y),i.closePath(),i.stroke()}}function G(t,e,i,o){var n=e.data,r=void 0==e.textColor?"#666666":e.textColor;t.forEach((function(t,l){if(null!==t){o.beginPath(),o.setFontSize(i.fontSize),o.setFillStyle(r);var s=n[l];"object"===a(n[l])&&null!==n[l]&&(s=n[l].value);var h=e.format?e.format(s):s;o.fillText(h,t.x-u(h)/2,t.y-2),o.closePath(),o.stroke()}}))}function Y(t,e,i,a,o,n){e-=t.width/2+o.gaugeLabelTextMargin;for(var r=t.startAngle-t.endAngle+1,l=r/t.splitLine.splitNumber,s=t.endNumber-t.startNumber,h=s/t.splitLine.splitNumber,c=t.startAngle,d=t.startNumber,p=0;p<t.splitLine.splitNumber+1;p++){var x={x:e*Math.cos(c*Math.PI),y:e*Math.sin(c*Math.PI)};x.x+=i.x-u(d)/2,x.y+=i.y;var f=x.x,g=x.y;n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(t.labelColor||"#666666"),n.fillText(d,f,g+o.fontSize/2),n.closePath(),n.stroke(),c+=l,c>=2&&(c%=2),d+=h}}function $(t,e,i,a,o,n){var l=a.extra.radar||{};e+=o.radarLabelTextMargin,t.forEach((function(t,s){var h={x:e*Math.cos(t),y:e*Math.sin(t)},c=d(h.x,h.y,i),p=c.x,x=c.y;r.approximatelyEqual(h.x,0)?p-=u(a.categories[s]||"")/2:h.x<0&&(p-=u(a.categories[s]||"")),n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(l.labelColor||"#666666"),n.fillText(a.categories[s]||"",p,x+o.fontSize/2),n.closePath(),n.stroke()}))}function q(t,e,i,a,o,n){for(var l=i.pieChartLinePadding,s=[],h=null,c=t.map((function(t){var e=2*Math.PI-(t._start_+2*Math.PI*t._proportion_/2),i=t.format?t.format(+t._proportion_.toFixed(2)):r.toFixed(100*t._proportion_)+"%",a=t.color,o=t._radius_;return{arc:e,text:i,color:a,radius:o}})),x=0;x<c.length;x++){var f=c[x],g=Math.cos(f.arc)*(f.radius+l),y=Math.sin(f.arc)*(f.radius+l),v=Math.cos(f.arc)*f.radius,b=Math.sin(f.arc)*f.radius,m=g>=0?g+i.pieChartTextPadding:g-i.pieChartTextPadding,A=y,T=u(f.text),P=A;h&&r.isSameXCoordinateArea(h.start,{x:m})&&(P=m>0?Math.min(A,h.start.y):g<0||A>0?Math.max(A,h.start.y):Math.min(A,h.start.y)),m<0&&(m-=T);var S={lineStart:{x:v,y:b},lineEnd:{x:g,y:y},start:{x:m,y:P},width:T,height:i.fontSize,text:f.text,color:f.color};h=p(S,h),s.push(h)}for(var _=0;_<s.length;_++){var L=s[_],w=d(L.lineStart.x,L.lineStart.y,n),M=d(L.lineEnd.x,L.lineEnd.y,n),F=d(L.start.x,L.start.y,n);a.setLineWidth(1*e.pixelRatio),a.setFontSize(i.fontSize),a.beginPath(),a.setStrokeStyle(L.color),a.setFillStyle(L.color),a.moveTo(w.x,w.y);var R=L.start.x<0?F.x+L.width:F.x,C=L.start.x<0?F.x-5:F.x+5;a.quadraticCurveTo(M.x,M.y,R,F.y),a.moveTo(w.x,w.y),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(F.x+L.width,F.y),a.arc(R,F.y,2,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle("#666666"),a.fillText(L.text,C,F.y+3),a.closePath(),a.stroke(),a.closePath()}}function J(t,e,i,a){var o=e.extra.tooltip||{};o.gridType=void 0==o.gridType?"solid":o.gridType,o.dashLength=void 0==o.dashLength?4:o.dashLength;var n=i.padding,r=e.height-i.padding-i.xAxisHeight-i.legendHeight;if("dash"==o.gridType&&a.setLineDash([o.dashLength,o.dashLength]),a.beginPath(),a.setStrokeStyle(o.gridColor||"#cccccc"),a.setLineWidth(1*e.pixelRatio),a.moveTo(t,n),a.lineTo(t,r),a.closePath(),a.stroke(),a.setLineDash([]),o.xAxisLabel){var s=e.categories[e.tooltip.index];a.setFontSize(i.fontSize);var h=a.measureText(s).width,c=t-i.toolTipPadding-.5*h,d=r;a.beginPath(),a.setFillStyle(l(o.labelBgColor||i.toolTipBackground,o.labelBgOpacity||i.toolTipOpacity)),a.setStrokeStyle(o.labelBgColor||i.toolTipBackground),a.setLineWidth(1*e.pixelRatio),a.rect(c,d,h+2*i.toolTipPadding,i.fontSize+2*i.toolTipPadding),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(o.labelFontColor||i.fontColor),a.fillText(s,c+2*i.toolTipPadding,d+i.toolTipPadding+i.fontSize),a.closePath(),a.stroke()}}function Z(t,e,i,a,o){var n=t.extra.tooltip||{};n.gridType=void 0==n.gridType?"solid":n.gridType,n.dashLength=void 0==n.dashLength?4:n.dashLength;var r=e.padding+e.yAxisWidth+e.yAxisTitleWidth,s=t.width-e.padding;if("dash"==n.gridType&&i.setLineDash([n.dashLength,n.dashLength]),i.beginPath(),i.setStrokeStyle(n.gridColor||"#cccccc"),i.setLineWidth(1*t.pixelRatio),i.moveTo(r,t.tooltip.offset.y),i.lineTo(s,t.tooltip.offset.y),i.closePath(),i.stroke(),i.setLineDash([]),n.yAxisLabel){var h=function(t,e,i,a,o){var n=O(e,i,a),r=i.height-2*a.padding-a.xAxisHeight-a.legendHeight,l=n[0],s=n[n.length-1],h=a.padding,c=a.padding+r,d=l-(l-s)*(t-h)/(c-h);return d=i.yAxis.format?i.yAxis.format(Number(d)):d,d}(t.tooltip.offset.y,t.series,t,e);i.setFontSize(e.fontSize);var c=i.measureText(h).width,d=r-2*e.toolTipPadding-c,p=t.tooltip.offset.y;i.beginPath(),i.setFillStyle(l(n.labelBgColor||e.toolTipBackground,n.labelBgOpacity||e.toolTipOpacity)),i.setStrokeStyle(n.labelBgColor||e.toolTipBackground),i.setLineWidth(1*t.pixelRatio),i.rect(d,p-.5*e.fontSize-e.toolTipPadding,c+2*e.toolTipPadding,e.fontSize+2*e.toolTipPadding),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(e.fontSize),i.setFillStyle(n.labelFontColor||e.fontColor),i.fillText(h,d+e.toolTipPadding,p+.5*e.fontSize),i.closePath(),i.stroke()}}function K(t,e,i,a,o){var n=e.extra.tooltip||{activeBgColor:"#000000",activeBgOpacity:.08};n.activeBgColor=n.activeBgColor?n.activeBgColor:"#000000",n.activeBgOpacity=n.activeBgOpacity?n.activeBgOpacity:.08;var r=i.padding,s=e.height-i.padding-i.xAxisHeight-i.legendHeight;a.beginPath(),a.setFillStyle(l(n.activeBgColor,n.activeBgOpacity)),a.rect(t-o/2,r,o,s-r),a.closePath(),a.fill()}function Q(t,e,i,a,o,r){var s=t.extra.tooltip||{};s.horizentalLine&&t.tooltip&&1===a&&("line"==t.type||"area"==t.type||"column"==t.type||"candle"==t.type||"mix"==t.type)&&Z(t,e,i),i.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&i.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===a&&function(t,e,i,a,o,r,s){var h=i.extra.tooltip||{bgColor:"#000000",bgOpacity:.7,fontColor:"#FFFFFF"};h.bgColor=h.bgColor?h.bgColor:"#000000",h.bgOpacity=h.bgOpacity?h.bgOpacity:.7,h.fontColor=h.fontColor?h.fontColor:"#FFFFFF";var c=4*i.pixelRatio,d=5*i.pixelRatio,p=8*i.pixelRatio,x=!1;"line"!=i.type&&"area"!=i.type&&"candle"!=i.type&&"mix"!=i.type||J(i.tooltip.offset.x,i,a,o),e=n({x:0,y:0},e),e.y-=8*i.pixelRatio;var f=t.map((function(t){return u(t.text)})),g=c+d+4*a.toolTipPadding+Math.max.apply(null,f),y=2*a.toolTipPadding+(t.length+1)*a.toolTipLineHeight;if(e.x-Math.abs(i._scrollDistance_)+p+g>i.width&&(x=!0),o.beginPath(),o.setFillStyle(l(h.bgColor||a.toolTipBackground,h.bgOpacity||a.toolTipOpacity)),x?(o.moveTo(e.x,e.y+10*i.pixelRatio),o.lineTo(e.x-p,e.y+10*i.pixelRatio-5*i.pixelRatio),o.lineTo(e.x-p,e.y),o.lineTo(e.x-p-Math.round(g),e.y),o.lineTo(e.x-p-Math.round(g),e.y+y),o.lineTo(e.x-p,e.y+y),o.lineTo(e.x-p,e.y+10*i.pixelRatio+5*i.pixelRatio),o.lineTo(e.x,e.y+10*i.pixelRatio)):(o.moveTo(e.x,e.y+10*i.pixelRatio),o.lineTo(e.x+p,e.y+10*i.pixelRatio-5*i.pixelRatio),o.lineTo(e.x+p,e.y),o.lineTo(e.x+p+Math.round(g),e.y),o.lineTo(e.x+p+Math.round(g),e.y+y),o.lineTo(e.x+p,e.y+y),o.lineTo(e.x+p,e.y+10*i.pixelRatio+5*i.pixelRatio),o.lineTo(e.x,e.y+10*i.pixelRatio)),o.closePath(),o.fill(),t.forEach((function(t,i){if(null!==t.color){o.beginPath(),o.setFillStyle(t.color);var n=e.x+p+2*a.toolTipPadding,r=e.y+(a.toolTipLineHeight-a.fontSize)/2+a.toolTipLineHeight*i+a.toolTipPadding+1;x&&(n=e.x-g-p+2*a.toolTipPadding),o.fillRect(n,r,c,a.fontSize),o.closePath()}})),o.beginPath(),o.setFontSize(a.fontSize),o.setFillStyle(h.fontColor),o.closePath(),o.stroke(),t.length>0){var v=e.x+p+2*a.toolTipPadding+c+d;x&&(v=e.x-g-p+2*a.toolTipPadding+ +c+d);var b=e.y+(a.toolTipLineHeight-a.fontSize)/2+a.toolTipLineHeight+a.toolTipPadding;o.beginPath(),o.setFontSize(a.fontSize),o.setFillStyle(h.fontColor),o.fillText(t[0].time,v,b-a.fontSize),o.closePath(),o.stroke()}t.forEach((function(t,i){var n=e.x+p+2*a.toolTipPadding+c+d;x&&(n=e.x-g-p+2*a.toolTipPadding+ +c+d);var r=e.y+(a.toolTipLineHeight-a.fontSize)/2+a.toolTipLineHeight*(i+1);o.beginPath(),o.setFontSize(a.fontSize),o.setFillStyle(h.fontColor),o.fillText(t.text,n,r+a.fontSize),o.closePath(),o.stroke()}))}(t.tooltip.textList,t.tooltip.offset,t,e,i),i.restore()}function U(t,e,i,a){var o=I(t,e,i),n=o.xAxisPoints,r=o.startX,l=o.endX,s=o.eachSpacing,h=e.height-i.padding-i.xAxisHeight-i.legendHeight,c=i.padding;if(e.enableScroll&&e.xAxis.scrollShow){var d=e.height-i.padding-i.legendHeight+6*e.pixelRatio,p=l-r,x=s*(n.length-1),f=p*p/x,g=0;e._scrollDistance_&&(g=-e._scrollDistance_*p/x),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pixelRatio),a.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),a.moveTo(r,d),a.lineTo(l,d),a.stroke(),a.closePath(),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pixelRatio),a.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),a.moveTo(r+g,d),a.lineTo(r+g+f,d),a.stroke(),a.setLineCap("butt"),a.closePath()}if(a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&a.translate(e._scrollDistance_,0),a.beginPath(),a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pixelRatio),"dash"==e.xAxis.gridType&&a.setLineDash([e.xAxis.dashLength,e.xAxis.dashLength]),!0!==e.xAxis.disableGrid&&("calibration"===e.xAxis.type?n.forEach((function(t,i){i>0&&(a.moveTo(t-s/2,h),a.lineTo(t-s/2,h+4*e.pixelRatio))})):n.forEach((function(t,e){a.moveTo(t,h),a.lineTo(t,c)}))),a.closePath(),a.stroke(),a.setLineDash([]),!0!==e.xAxis.disabled){var y=e.width-2*i.padding-i.yAxisWidth-i.yAxisTitleWidth,v=t.length;0===i._xAxisTextAngle_?e.xAxis.labelCount&&(v=e.xAxis.itemCount?Math.ceil(t.length/e.xAxis.itemCount*e.xAxis.labelCount):e.xAxis.labelCount,v-=1):v=Math.min(t.length,Math.ceil(y/i.fontSize/1.5));for(var b=Math.ceil(t.length/v),m=[],A=t.length,T=0;T<A;T++)T%b!==0?m.push(""):m.push(t[T]);m[A-1]=t[A-1],0===i._xAxisTextAngle_?m.forEach((function(t,o){var r=s/2-u(t)/2;a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(e.xAxis.fontColor||"#666666"),a.fillText(t,n[o]+r,h+i.fontSize+5),a.closePath(),a.stroke()})):m.forEach((function(t,o){a.save(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(e.xAxis.fontColor||"#666666");var r=u(t),l=s/2-r,c=function(t,e,i){var a=t,o=i-e,n=a+(i-o-a)/Math.sqrt(2);n*=-1;var r=(i-o)*(Math.sqrt(2)-1)-(i-o-a)/Math.sqrt(2);return{transX:n,transY:r}}(n[o]+s/2,h+i.fontSize/2+5,e.height),d=c.transX,p=c.transY;a.rotate(-1*i._xAxisTextAngle_),a.translate(d,p),a.fillText(t,n[o]+l,h+i.fontSize+5),a.closePath(),a.stroke(),a.restore()}))}a.restore()}function V(t,e,i,a){if(!0!==e.yAxis.disableGrid){for(var o=e.height-2*i.padding-i.xAxisHeight-i.legendHeight,n=Math.floor(o/i.yAxisSplit),r=i.yAxisWidth+i.yAxisTitleWidth,l=i.padding+r,s=I(t,e,i),h=s.xAxisPoints,c=s.eachSpacing,d=c*(h.length-1),p=l+d,u=[],x=0;x<i.yAxisSplit;x++)u.push(i.padding+n*x);u.push(i.padding+n*i.yAxisSplit+2),a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&a.translate(e._scrollDistance_,0),"dash"==e.yAxis.gridType&&a.setLineDash([e.yAxis.dashLength,e.yAxis.dashLength]),a.beginPath(),a.setStrokeStyle(e.yAxis.gridColors||"#cccccc"),a.setLineWidth(1*e.pixelRatio),u.forEach((function(t,e){a.moveTo(l,t),a.lineTo(p,t)})),a.closePath(),a.stroke(),a.setLineDash([]),a.restore()}}function tt(t,e,i,a){if(!0!==e.yAxis.disabled){var o=B(t,e,i),n=o.rangesFormat,r=i.yAxisWidth+i.yAxisTitleWidth,l=e.height-2*i.padding-i.xAxisHeight-i.legendHeight,s=Math.floor(l/i.yAxisSplit),h=i.padding+r,c=e.width-i.padding,d=e.height-i.padding-i.xAxisHeight-i.legendHeight+i.xAxisTextPadding;a.beginPath(),a.setFillStyle(e.background||"#ffffff"),e._scrollDistance_<0&&a.fillRect(0,0,h,d+i.xAxisHeight),a.fillRect(c,0,e.width,d+i.xAxisHeight),a.closePath(),a.stroke();for(var p=[],x=0;x<=i.yAxisSplit;x++)p.push(i.padding+s*x);n.forEach((function(t,o){var n=p[o]?p[o]:d;a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(e.yAxis.fontColor||"#666666"),a.fillText(t,i.padding+i.yAxisTitleWidth,n+i.fontSize/2),a.closePath(),a.stroke()})),e.yAxis.title&&function(t,e,i,a){var o=i.xAxisHeight+(e.height-i.xAxisHeight-u(t))/2;a.save(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(e.yAxis.titleFontColor||"#333333"),a.translate(0,e.height),a.rotate(-90*Math.PI/180),a.fillText(t,o,i.padding+.5*i.fontSize),a.closePath(),a.stroke(),a.restore()}(e.yAxis.title,e,i,a)}}function et(t,e,i,a){if(!1!==e.legend){var o=_(t,e,i),n=o.legendList,r=5*e.pixelRatio,l=10*e.pixelRatio,s=15*e.pixelRatio;n.forEach((function(t,o){for(var n=0,h=0;h<t.length;h++){var c=t[h];c.name=c.name||"undefined",n+=3*r+u(c.name)+s}var d=(e.width-n)/2+r,p=e.height-i.padding-i.legendHeight+o*(i.fontSize+l)+r+l+10;a.setFontSize(i.fontSize);for(var x=0;x<t.length;x++){var f=t[x];switch(e.type){case"line":a.beginPath(),a.setLineWidth(1*e.pixelRatio),a.setStrokeStyle(f.color),a.setFillStyle(f.color),a.moveTo(d+7.5*e.pixelRatio-10,p+5*e.pixelRatio),a.lineTo(d+7.5*e.pixelRatio+10,p+5*e.pixelRatio),a.arc(d+7.5*e.pixelRatio,p+5*e.pixelRatio,3*e.pixelRatio,0,2*Math.PI),a.closePath(),a.fill(),a.stroke();break;case"pie":a.beginPath(),a.setLineWidth(1*e.pixelRatio),a.setStrokeStyle(f.color),a.setFillStyle(f.color),a.moveTo(d+7.5*e.pixelRatio,p+5*e.pixelRatio),a.arc(d+7.5*e.pixelRatio,p+5*e.pixelRatio,6*e.pixelRatio,0,2*Math.PI),a.closePath(),a.fill(),a.stroke();break;case"ring":case"rose":a.beginPath(),a.setLineWidth(1*e.pixelRatio),a.setStrokeStyle(f.color),a.setFillStyle(f.color),a.moveTo(d+7.5*e.pixelRatio,p+5*e.pixelRatio),a.arc(d+7.5*e.pixelRatio,p+5*e.pixelRatio,6*e.pixelRatio,0,2*Math.PI),a.closePath(),a.fill(),a.stroke();break;case"gauge":break;case"arcbar":break;default:a.beginPath(),a.setLineWidth(1*e.pixelRatio),a.setStrokeStyle(f.color),a.setFillStyle(f.color),a.moveTo(d+7.5*e.pixelRatio-10,p+5*e.pixelRatio),a.lineTo(d+7.5*e.pixelRatio+10,p+5*e.pixelRatio),a.arc(d+7.5*e.pixelRatio,p+5*e.pixelRatio,3*e.pixelRatio,0,2*Math.PI),a.closePath(),a.fill(),a.stroke(),a.stroke()}d+=r+s,a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(e.extra.legendTextColor||"#333333"),a.fillText(f.name,d,p+6*e.pixelRatio+3*e.pixelRatio),a.closePath(),a.stroke(),d+=u(f.name)+2*r}}))}}function it(t,e){e.draw()}var at={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};function ot(t){this.isStop=!1,t.duration="undefined"===typeof t.duration?1e3:t.duration,t.timing=t.timing||"linear";var e=function(){return"undefined"!==typeof requestAnimationFrame?requestAnimationFrame:"undefined"!==typeof setTimeout?function(t,e){setTimeout((function(){var e=+new Date;t(e)}),e)}:function(t){t(null)}}(),i=null,a=function(o){if(null===o||!0===this.isStop)return t.onProcess&&t.onProcess(1),void(t.onAnimationFinish&&t.onAnimationFinish());if(null===i&&(i=o),o-i<t.duration){var n=(o-i)/t.duration,r=at[t.timing];n=r(n),t.onProcess&&t.onProcess(n),e(a,17)}else t.onProcess&&t.onProcess(1),t.onAnimationFinish&&t.onAnimationFinish()};a=a.bind(this),e(a,17)}function nt(t,e,i,a){var o=this,s=e.series,h=e.categories;s=function(t,e){var i=0;return t.map((function(t){return t.color||(t.color=e.colors[i],i=(i+1)%e.colors.length),t}))}(s,i),s=function(t,e){return t.map((function(t){return t.type||(t.type=e.type),t}))}(s,e);var p=null;if("candle"==t){var x=n({},e.extra.candle.average);x.show&&(p=function(t,e,i,a){for(var o=[],n=0;n<t.length;n++){for(var r={data:[],name:e[n],color:i[n]},l=0,s=a.length;l<s;l++)if(l<t[n])r.data.push(null);else{for(var h=0,c=0;c<t[n];c++)h+=a[l-c][1];r.data.push(+(h/t[n]).toFixed(3))}o.push(r)}return o}(x.day,x.name,x.color,s[0].data),e.seriesMA=p)}var f=_(s,e,i),g=f.legendHeight;i.legendHeight=g;var b=B(s,e,i),m=b.yAxisWidth;if(i.yAxisWidth=m,h&&h.length){var A=function(t,e,i){var a={angle:0,xAxisHeight:i.xAxisHeight},o=I(t,e,i),n=o.eachSpacing,r=t.map((function(t){return u(t)})),l=Math.max.apply(this,r);return 1==e.xAxis.rotateLabel&&l+2*i.xAxisTextPadding>n&&(a.angle=45*Math.PI/180,a.xAxisHeight=2*i.xAxisTextPadding+l*Math.sin(a.angle)),a}(h,e,i),T=A.xAxisHeight,P=A.angle;i.xAxisHeight=T,i._xAxisTextAngle_=P}"pie"!==t&&"ring"!==t&&"rose"!==t||(i._pieTextMaxLength_=!1===e.dataLabel?0:function(t){t=w(t);for(var e=0,i=0;i<t.length;i++){var a=t[i],o=a.format?a.format(+a._proportion_.toFixed(2)):r.toFixed(100*a._proportion_)+"%";e=Math.max(e,u(o))}return e}(s));var O=e.animation?1e3:0;switch(this.animationInstance&&this.animationInstance.stop(),a.clearRect(0,0,e.width,e.height),t){case"line":this.animationInstance=new ot({timing:"easeIn",duration:O,onProcess:function(t){e.rotate&&N(a,e),V(h,e,i,a),U(h,e,i,a);var n=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.line||{type:"straight",width:2};n.type=n.type?n.type:"straight",n.width=n.width?n.width:2;var r=B(t,e,i),l=r.ranges,s=I(e.categories,e,i),h=s.xAxisPoints,d=s.eachSpacing,p=l.pop(),u=l.shift(),x=[];return a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&a.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&J(e.tooltip.offset.x,e,i,a),t.forEach((function(t,r){var l=t.data,s=H(l,p,u,h,d,e,i,o);x.push(s);var f=S(s);if(f.forEach((function(i,o){a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(n.width*e.pixelRatio),1===i.length?(a.moveTo(i[0].x,i[0].y),a.arc(i[0].x,i[0].y,1,0,2*Math.PI)):(a.moveTo(i[0].x,i[0].y),"curve"===n.type?i.forEach((function(t,e){if(e>0){var o=c(i,e-1);a.lineTo(o.ctrA.x,o.ctrA.y),a.bezierCurveTo(o.ctrA.x,o.ctrA.y,o.ctrB.x,o.ctrB.y,t.x,t.y)}})):i.forEach((function(t,e){e>0&&a.lineTo(t.x,t.y)})),a.moveTo(i[0].x,i[0].y)),a.closePath(),a.stroke()})),!1!==e.dataPointShape){var g=i.dataPointShape[r%i.dataPointShape.length];X(s,t.color,g,a,e)}})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,n){var r=t.data,l=H(r,p,u,h,d,e,i,o);G(l,t,i,a)})),a.restore(),{xAxisPoints:h,calPoints:x,eachSpacing:d}}(s,e,i,a,t),r=n.xAxisPoints,l=n.calPoints,d=n.eachSpacing;o.chartData.xAxisPoints=r,o.chartData.calPoints=l,o.chartData.eachSpacing=d,et(e.series,e,i,a),tt(s,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"mix":this.animationInstance=new ot({timing:"easeIn",duration:O,onProcess:function(t){e.rotate&&N(a,e),V(h,e,i,a),U(h,e,i,a);var n=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=B(t,e,i),r=n.ranges,l=I(e.categories,e,i),s=l.xAxisPoints,h=l.eachSpacing,d=r.pop(),p=r.shift(),u=e.height-i.padding-i.xAxisHeight-i.legendHeight,x=[],f=0,g=0;if(t.forEach((function(t,e){"column"==t.type&&(g+=1)})),a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&a.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&J(e.tooltip.offset.x,e,i,a),t.forEach((function(t,n){var r=t.data,l=H(r,d,p,s,h,e,i,o);if(x.push(l),"column"==t.type&&(l=k(l,h,g,f,i,e),l.forEach((function(o,n){if(null!==o){a.beginPath(),a.setFillStyle(o.color||t.color);var r=o.x-o.width/2+1,l=e.height-o.y-i.padding-i.xAxisHeight-i.legendHeight;a.moveTo(r,o.y),a.rect(r,o.y,o.width-2,l),a.closePath(),a.fill()}})),f+=1),"area"==t.type)for(var y=S(l),v=function(i){var o=y[i];if(a.beginPath(),a.setStrokeStyle(t.color),a.setFillStyle(t.color),a.setGlobalAlpha(.2),a.setLineWidth(2*e.pixelRatio),o.length>1){m=o[0];var n=o[o.length-1];a.moveTo(m.x,m.y),"curve"===t.style?o.forEach((function(t,e){if(e>0){var i=c(o,e-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,t.x,t.y)}})):o.forEach((function(t,e){e>0&&a.lineTo(t.x,t.y)})),a.lineTo(n.x,u),a.lineTo(m.x,u),a.lineTo(m.x,m.y)}else{var r=o[0];a.moveTo(r.x-h/2,r.y),a.lineTo(r.x+h/2,r.y),a.lineTo(r.x+h/2,u),a.lineTo(r.x-h/2,u),a.moveTo(r.x-h/2,r.y)}a.closePath(),a.fill(),a.setGlobalAlpha(1)},b=0;b<y.length;b++){var m;v(b)}if("line"==t.type){var A=S(l);A.forEach((function(i,o){a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(2*e.pixelRatio),1===i.length?(a.moveTo(i[0].x,i[0].y),a.arc(i[0].x,i[0].y,1,0,2*Math.PI)):(a.moveTo(i[0].x,i[0].y),"curve"==t.style?i.forEach((function(t,e){if(e>0){var o=c(i,e-1);a.bezierCurveTo(o.ctrA.x,o.ctrA.y,o.ctrB.x,o.ctrB.y,t.x,t.y)}})):i.forEach((function(t,e){e>0&&a.lineTo(t.x,t.y)})),a.moveTo(i[0].x,i[0].y)),a.closePath(),a.stroke()}))}if("point"==t.type){A=S(l);A.forEach((function(i,o){a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(2*e.pixelRatio),a.moveTo(i[0].x,i[0].y),a.arc(i[0].x,i[0].y,1,0,2*Math.PI),a.closePath(),a.stroke()}))}if(!1!==e.dataPointShape&&"column"!==t.type){var T=i.dataPointShape[n%i.dataPointShape.length];X(l,t.color,T,a,e)}})),!1!==e.dataLabel&&1===o){f=0;t.forEach((function(t,n){var r=t.data,l=H(r,d,p,s,h,e,i,o);"column"!==t.type?G(l,t,i,a):(l=k(l,h,g,f,i,e),G(l,t,i,a),f+=1)}))}return a.restore(),{xAxisPoints:s,calPoints:x,eachSpacing:h}}(s,e,i,a,t),r=n.xAxisPoints,l=n.calPoints,d=n.eachSpacing;o.chartData.xAxisPoints=r,o.chartData.calPoints=l,o.chartData.eachSpacing=d,et(e.series,e,i,a),tt(s,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"column":this.animationInstance=new ot({timing:"easeIn",duration:O,onProcess:function(t){e.rotate&&N(a,e),V(h,e,i,a),U(h,e,i,a);var n=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.column||{type:{},meter:{}};n.type=void 0==n.type?"group":n.type,n.meter=n.meter||{},n.meter.border=void 0==n.meter.border?4:n.meter.border,n.meter.fillColor=void 0==n.meter.fillColor?"#FFFFFF":n.meter.fillColor;var r=B(t,e,i),l=r.ranges,s=I(e.categories,e,i),h=s.xAxisPoints,c=s.eachSpacing,d=l.pop(),p=l.shift(),u=[];return a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&a.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&K(e.tooltip.offset.x,e,i,a,c),t.forEach((function(r,l){var s=r.data;switch(n.type){case"group":var x=H(s,d,p,h,c,e,i,o),f=W(s,d,p,h,c,e,i,l,t,o);u.push(f),x=k(x,c,t.length,l,i,e),x.forEach((function(t,o){if(null!==t){a.beginPath(),a.setFillStyle(t.color||r.color);var n=t.x-t.width/2+1,l=e.height-t.y-i.padding-i.xAxisHeight-i.legendHeight;a.moveTo(n,t.y),a.fillRect(n,t.y,t.width-2,l),a.closePath(),a.fill()}}));break;case"stack":x=W(s,d,p,h,c,e,i,l,t,o);u.push(x),x=z(x,c,t.length,0,i,e),x.forEach((function(t,o){if(null!==t){a.beginPath(),a.setFillStyle(t.color||r.color);var n=t.x-t.width/2+1,s=e.height-t.y-i.padding-i.xAxisHeight-i.legendHeight,h=e.height-t.y0-i.padding-i.xAxisHeight-i.legendHeight;l>0&&(s-=h),a.moveTo(n,t.y),a.fillRect(n,t.y,t.width-2,s),a.closePath(),a.fill()}}));break;case"meter":x=H(s,d,p,h,c,e,i,o);u.push(x),x=E(x,c,t.length,l,i,e,n.meter.border),0==l?x.forEach((function(t,o){if(null!==t){a.beginPath(),a.setFillStyle(n.meter.fillColor);var l=t.x-t.width/2+1,s=e.height-t.y-i.padding-i.xAxisHeight-i.legendHeight;a.moveTo(l,t.y),a.fillRect(l,t.y,t.width-2,s),a.closePath(),a.fill(),a.beginPath(),a.setStrokeStyle(r.color),a.setLineWidth(n.meter.border*e.pixelRatio),a.moveTo(l+.5*n.meter.border,t.y+s),a.lineTo(l+.5*n.meter.border,t.y+.5*n.meter.border),a.lineTo(l+t.width-n.meter.border,t.y+.5*n.meter.border),a.lineTo(l+t.width-n.meter.border,t.y+s),a.stroke()}})):x.forEach((function(t,o){if(null!==t){a.beginPath(),a.setFillStyle(t.color||r.color);var n=t.x-t.width/2+1,l=e.height-t.y-i.padding-i.xAxisHeight-i.legendHeight;a.moveTo(n,t.y),a.rect(n,t.y,t.width-2,l),a.closePath(),a.fill()}}));break}})),!1!==e.dataLabel&&1===o&&t.forEach((function(r,l){var s=r.data;switch(n.type){case"group":var u=H(s,d,p,h,c,e,i,o);u=k(u,c,t.length,l,i,e),G(u,r,i,a);break;case"stack":u=W(s,d,p,h,c,e,i,l,t,o);G(u,r,i,a);break;case"meter":u=H(s,d,p,h,c,e,i,o);G(u,r,i,a);break}})),a.restore(),{xAxisPoints:h,calPoints:u,eachSpacing:c}}(s,e,i,a,t),r=n.xAxisPoints,l=n.calPoints,c=n.eachSpacing;o.chartData.xAxisPoints=r,o.chartData.calPoints=l,o.chartData.eachSpacing=c,et(e.series,e,i,a),tt(s,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"area":this.animationInstance=new ot({timing:"easeIn",duration:O,onProcess:function(t){e.rotate&&N(a,e),V(h,e,i,a),U(h,e,i,a);var n=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.area||{type:"straight",opacity:.5,addLine:!0,width:2};n.type=n.type?n.type:"straight",n.opacity=n.opacity?n.opacity:.2,n.addLine=1==n.addLine,n.width=n.width?n.width:2;var r=B(t,e,i),s=r.ranges,h=I(e.categories,e,i),d=h.xAxisPoints,p=h.eachSpacing,u=s.pop(),x=s.shift(),f=e.height-i.padding-i.xAxisHeight-i.legendHeight,g=[];return a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&a.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&J(e.tooltip.offset.x,e,i,a),t.forEach((function(t,r){var s=t.data,h=H(s,u,x,d,p,e,i,o);g.push(h);for(var y=S(h),v=function(i){var o=y[i];if(a.beginPath(),a.setStrokeStyle(l(t.color,n.opacity)),a.setFillStyle(l(t.color,n.opacity)),a.strokeStyle="blue",a.lineWidth=10,m=a.createLinearGradient(0,0,0,200),m.addColorStop(0,l(t.color,n.opacity)),m.addColorStop(1,l(t.color,0)),a.fillStyle=m,a.setLineWidth(n.width*e.pixelRatio),o.length>1){var s=o[0],h=o[o.length-1];a.moveTo(s.x,s.y),"curve"===n.type?o.forEach((function(t,e){if(e>0){var i=c(o,e-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,t.x,t.y)}})):o.forEach((function(t,e){e>0&&a.lineTo(t.x,t.y)})),a.lineTo(h.x,f),a.lineTo(s.x,f),a.lineTo(s.x,s.y)}else{var d=o[0];a.moveTo(d.x-p/2,d.y),a.lineTo(d.x+p/2,d.y),a.lineTo(d.x+p/2,f),a.lineTo(d.x-p/2,f),a.moveTo(d.x-p/2,d.y)}a.closePath(),a.fill(),a.strokeStyle=e.series[r].color?e.series[r].color:"blue",n.addLine&&(a.beginPath(),a.setLineWidth(n.width*e.pixelRatio),1===o.length?(a.moveTo(o[0].x,o[0].y),a.arc(o[0].x,o[0].y,1,0,2*Math.PI)):(a.moveTo(o[0].x,o[0].y),"curve"===n.type?o.forEach((function(t,e){if(e>0){var i=c(o,e-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,t.x,t.y)}})):o.forEach((function(t,e){e>0&&a.lineTo(t.x,t.y)})),a.moveTo(o[0].x,o[0].y)),a.closePath(),a.stroke())},b=0;b<y.length;b++){var m;v(b)}if(!1!==e.dataPointShape){var A=i.dataPointShape[r%i.dataPointShape.length];X(h,t.color,A,a,e)}})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,n){var r=t.data,l=H(r,u,x,d,p,e,i,o);G(l,t,i,a)})),a.restore(),{xAxisPoints:d,calPoints:g,eachSpacing:p}}(s,e,i,a,t),r=n.xAxisPoints,d=n.calPoints,p=n.eachSpacing;o.chartData.xAxisPoints=r,o.chartData.calPoints=d,o.chartData.eachSpacing=p,et(e.series,e,i,a),tt(s,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"ring":case"pie":this.animationInstance=new ot({timing:"easeInOut",duration:O,onProcess:function(t){e.rotate&&N(a,e),o.chartData.pieData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.pie||{},r={x:e.width/2,y:(e.height-i.legendHeight)/2},s=Math.min(r.x-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,r.y-i.pieChartLinePadding-i.pieChartTextPadding);e.dataLabel?s-=10:s-=2*i.padding,t=w(t,s,o);var h=i.pieChartLinePadding/2;if(t=t.map((function(t){return t._start_+=(n.offsetAngle||0)*Math.PI/180,t})),t.forEach((function(t,i){e.tooltip&&e.tooltip.index==i&&(a.beginPath(),a.setFillStyle(l(t.color,e.extra.pie.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,t._radius_+h,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(2*e.pixelRatio),a.lineJoin="round",a.setStrokeStyle("#ffffff"),a.setFillStyle(t.color),a.moveTo(r.x,r.y),a.arc(r.x,r.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill(),!0!==e.disablePieStroke&&a.stroke()})),"ring"===e.type){var c=.6*s;"number"===typeof e.extra.pie.ringWidth&&e.extra.pie.ringWidth>0&&(c=Math.max(0,s-e.extra.pie.ringWidth)),a.beginPath(),a.setFillStyle(e.background||"#ffffff"),a.moveTo(r.x,r.y),a.arc(r.x,r.y,c,0,2*Math.PI),a.closePath(),a.fill()}if(!1!==e.dataLabel&&1===o){for(var d=!1,p=0,u=t.length;p<u;p++)if(t[p].data>0){d=!0;break}d&&q(t,e,i,a,0,r)}return 1===o&&"ring"===e.type&&j(e,i,a),{center:r,radius:s,series:t}}(s,e,i,a,t),et(e.series,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"rose":this.animationInstance=new ot({timing:"easeInOut",duration:O,onProcess:function(t){e.rotate&&N(a,e),o.chartData.pieData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.rose||{};n.type=n.type||"area";var r={x:e.width/2,y:(e.height-i.legendHeight)/2},s=Math.min(r.x-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,r.y-i.pieChartLinePadding-i.pieChartTextPadding);e.dataLabel?s-=10:s-=2*i.padding;var h=n.minRadius||.5*s;t=M(t,n.type,h,s,o);var c=i.pieChartLinePadding/2;if(t=t.map((function(t){return t._start_+=(n.offsetAngle||0)*Math.PI/180,t})),t.forEach((function(t,i){e.tooltip&&e.tooltip.index==i&&(a.beginPath(),a.setFillStyle(l(t.color,n.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,c+t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(2*e.pixelRatio),a.lineJoin="round",a.setStrokeStyle("#ffffff"),a.setFillStyle(t.color),a.moveTo(r.x,r.y),a.arc(r.x,r.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill(),!0!==e.disablePieStroke&&a.stroke()})),!1!==e.dataLabel&&1===o){for(var d=!1,p=0,u=t.length;p<u;p++)if(t[p].data>0){d=!0;break}d&&q(t,e,i,a,0,r)}return{center:r,radius:s,series:t}}(s,e,i,a,t),et(e.series,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"radar":this.animationInstance=new ot({timing:"easeInOut",duration:O,onProcess:function(t){e.rotate&&N(a,e),o.chartData.radarData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.radar||{},r=v(e.categories.length),l={x:e.width/2,y:(e.height-i.legendHeight)/2},s=Math.min(l.x-(y(e.categories)+i.radarLabelTextMargin),l.y-i.radarLabelTextMargin);s-=i.padding,a.beginPath(),a.setLineWidth(1*e.pixelRatio),a.setStrokeStyle(n.gridColor||"#cccccc"),r.forEach((function(t){var e=d(s*Math.cos(t),s*Math.sin(t),l);a.moveTo(l.x,l.y),a.lineTo(e.x,e.y)})),a.stroke(),a.closePath();for(var h=function(t){var o={};a.beginPath(),a.setLineWidth(1*e.pixelRatio),a.setStrokeStyle(n.gridColor||"#cccccc"),r.forEach((function(e,n){var r=d(s/i.radarGridCount*t*Math.cos(e),s/i.radarGridCount*t*Math.sin(e),l);0===n?(o=r,a.moveTo(r.x,r.y)):a.lineTo(r.x,r.y)})),a.lineTo(o.x,o.y),a.stroke(),a.closePath()},c=1;c<=i.radarGridCount;c++)h(c);var p=L(r,l,s,t,e,o);return p.forEach((function(t,o){if(a.beginPath(),a.setFillStyle(t.color),a.setGlobalAlpha(.3),t.data.forEach((function(t,e){0===e?a.moveTo(t.position.x,t.position.y):a.lineTo(t.position.x,t.position.y)})),a.closePath(),a.fill(),a.setGlobalAlpha(1),!1!==e.dataPointShape){var n=i.dataPointShape[o%i.dataPointShape.length],r=t.data.map((function(t){return t.position}));X(r,t.color,n,a,e)}})),$(r,s,l,e,i,a),{center:l,radius:s,angleList:r}}(s,e,i,a,t),et(e.series,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new ot({timing:"easeInOut",duration:O,onProcess:function(t){e.rotate&&N(a,e),o.chartData.arcbarData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.extra.arcbar||{};n.startAngle=n.startAngle?n.startAngle:.75,n.endAngle=n.endAngle?n.endAngle:.25,n.type=n.type?n.type:"default",t=F(t,n,o);var r={x:e.width/2,y:e.height/2},l=Math.min(r.x,r.y);"number"===typeof n.width&&n.width>0?n.width=n.width:n.width=12*e.pixelRatio,l-=i.padding+n.width/2,a.setLineWidth(n.width),a.setStrokeStyle(n.backgroundColor||"#E9E9E9"),a.setLineCap("round"),a.beginPath(),"default"==n.type?a.arc(r.x,r.y,l,n.startAngle*Math.PI,n.endAngle*Math.PI,!1):a.arc(r.x,r.y,l,0,2*Math.PI,!1),a.stroke();for(var s=0;s<t.length;s++){var h=t[s];a.setLineWidth(n.width),a.setStrokeStyle(h.color),a.setLineCap("round"),a.beginPath(),a.arc(r.x,r.y,l,n.startAngle*Math.PI,h._proportion_*Math.PI,!1),a.stroke()}return j(e,i,a),{center:r,radius:l,series:t}}(s,e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new ot({timing:"easeInOut",duration:O,onProcess:function(t){e.rotate&&N(a,e),o.chartData.gaugeData=function(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=i.extra.gauge||{};r.startAngle=r.startAngle?r.startAngle:.75,void 0==r.oldAngle&&(r.oldAngle=r.startAngle),void 0==r.oldData&&(r.oldData=0),r.endAngle=r.endAngle?r.endAngle:.25,t=R(t,r.startAngle,r.endAngle);var l={x:i.width/2,y:i.height/2},s=Math.min(l.x,l.y);"number"===typeof r.width&&r.width>0?r.width=r.width:r.width=15*i.pixelRatio,s-=a.padding+r.width/2;var h=s-r.width;o.setLineWidth(r.width),o.setLineCap("butt");for(var c=0;c<t.length;c++){var d=t[c];o.beginPath(),o.setStrokeStyle(d.color),o.arc(l.x,l.y,s,d._startAngle_*Math.PI,d._endAngle_*Math.PI,!1),o.stroke()}o.save();var p=r.startAngle-r.endAngle+1;r.splitLine.fixRadius=r.splitLine.fixRadius?r.splitLine.fixRadius:0,r.splitLine.splitNumber=r.splitLine.splitNumber?r.splitLine.splitNumber:10,r.splitLine.width=r.splitLine.width?r.splitLine.width:15*i.pixelRatio,r.splitLine.color=r.splitLine.color?r.splitLine.color:"#FFFFFF",r.splitLine.childNumber=r.splitLine.childNumber?r.splitLine.childNumber:5,r.splitLine.childWidth=r.splitLine.childWidth?r.splitLine.childWidth:5*i.pixelRatio;var u=p/r.splitLine.splitNumber,x=p/r.splitLine.splitNumber/r.splitLine.childNumber,f=-s-.5*r.width-r.splitLine.fixRadius,g=-s-.5*r.width-r.splitLine.fixRadius+r.splitLine.width,y=-s-.5*r.width-r.splitLine.fixRadius+r.splitLine.childWidth;o.translate(l.x,l.y),o.rotate((r.startAngle-1)*Math.PI);for(var v=0;v<r.splitLine.splitNumber+1;v++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(2*i.pixelRatio),o.moveTo(f,0),o.lineTo(g,0),o.stroke(),o.rotate(u*Math.PI);o.restore(),o.save(),o.translate(l.x,l.y),o.rotate((r.startAngle-1)*Math.PI);for(var b=0;b<r.splitLine.splitNumber*r.splitLine.childNumber+1;b++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(1*i.pixelRatio),o.moveTo(f,0),o.lineTo(y,0),o.stroke(),o.rotate(x*Math.PI);o.restore(),r.pointer.width=r.pointer.width?r.pointer.width:15*i.pixelRatio,void 0==r.pointer.color||"auto"==r.pointer.color||r.pointer.color,r.pointer.color,e=C(e,t,r,n);for(var m=0;m<e.length;m++){var A=e[m];o.save(),o.translate(l.x,l.y),o.rotate((A._proportion_-1)*Math.PI),o.beginPath(),o.setFillStyle(A.color),o.moveTo(r.pointer.width,0),o.lineTo(0,-r.pointer.width/2),o.lineTo(-h,0),o.lineTo(0,r.pointer.width/2),o.lineTo(r.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,r.pointer.width/6,0,2*Math.PI,!1),o.fill(),o.restore()}return!1!==i.dataLabel&&Y(r,s,l,0,a,o),j(i,a,o),1===n&&"gauge"===i.type&&(r.oldAngle=e[0]._proportion_,r.oldData=e[0].data),{center:l,radius:s,innerRadius:h,categories:t,totalAngle:p}}(h,s,e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break;case"candle":this.animationInstance=new ot({timing:"easeIn",duration:O,onProcess:function(t){e.rotate&&N(a,e),V(h,e,i,a),U(h,e,i,a);var n=function(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=i.extra.candle||{color:{},average:{}};r.color.upLine=r.color.upLine?r.color.upLine:"#f04864",r.color.upFill=r.color.upFill?r.color.upFill:"#f04864",r.color.downLine=r.color.downLine?r.color.downLine:"#2fc25b",r.color.downFill=r.color.downFill?r.color.downFill:"#2fc25b",r.average.show=!0===r.average.show,r.average.name=r.average.name?r.average.name:[],r.average.day=r.average.day?r.average.day:[],r.average.color=r.average.color?r.average.color:["#1890ff","#2fc25b","#facc14","#f04864","#8543e0","#90ed7d"],i.extra.candle=r;var l=B(t,i,a),s=l.ranges,h=I(i.categories,i,a),d=h.xAxisPoints,p=h.eachSpacing,u=s.pop(),x=s.shift(),f=[];return o.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&o.translate(i._scrollDistance_,0),r.average.show&&e.forEach((function(t,e){var r=t.data,l=H(r,u,x,d,p,i,a,n),s=S(l);s.forEach((function(e,i){o.beginPath(),o.setStrokeStyle(t.color),o.setLineWidth(1),1===e.length?(o.moveTo(e[0].x,e[0].y),o.arc(e[0].x,e[0].y,1,0,2*Math.PI)):(o.moveTo(e[0].x,e[0].y),e.forEach((function(t,i){if(i>0){var a=c(e,i-1);o.bezierCurveTo(a.ctrA.x,a.ctrA.y,a.ctrB.x,a.ctrB.y,t.x,t.y)}})),o.moveTo(e[0].x,e[0].y)),o.closePath(),o.stroke()}))})),t.forEach((function(t,e){var l=t.data,s=D(l,u,x,d,p,i,a,n);f.push(s);var h=S(s);h=h[0],h.forEach((function(t,e){o.beginPath(),l[e][1]-l[e][0]>0?(o.setStrokeStyle(r.color.upLine),o.setFillStyle(r.color.upFill),o.setLineWidth(1*i.pixelRatio),o.moveTo(t[3].x,t[3].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[1].x-p/4,t[1].y),o.lineTo(t[0].x-p/4,t[0].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[2].x,t[2].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[0].x+p/4,t[0].y),o.lineTo(t[1].x+p/4,t[1].y),o.lineTo(t[1].x,t[1].y),o.moveTo(t[3].x,t[3].y)):(o.setStrokeStyle(r.color.downLine),o.setFillStyle(r.color.downFill),o.setLineWidth(1*i.pixelRatio),o.moveTo(t[3].x,t[3].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[0].x-p/4,t[0].y),o.lineTo(t[1].x-p/4,t[1].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[2].x,t[2].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[1].x+p/4,t[1].y),o.lineTo(t[0].x+p/4,t[0].y),o.lineTo(t[0].x,t[0].y),o.moveTo(t[3].x,t[3].y)),o.closePath(),o.fill(),o.stroke()}))})),o.restore(),{xAxisPoints:d,calPoints:f,eachSpacing:p}}(s,p,e,i,a,t),r=n.xAxisPoints,l=n.calPoints,d=n.eachSpacing;o.chartData.xAxisPoints=r,o.chartData.calPoints=l,o.chartData.eachSpacing=d,et(p||e.series,e,i,a),tt(s,e,i,a),Q(e,i,a,t),it(0,a)},onAnimationFinish:function(){o.event.trigger("renderComplete")}});break}}function rt(){this.events={}}ot.prototype.stop=function(){this.isStop=!0},rt.prototype.addEventListener=function(t,e){this.events[t]=this.events[t]||[],this.events[t].push(e)},rt.prototype.trigger=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];var a=e[0],o=e.slice(1);this.events[a]&&this.events[a].forEach((function(t){try{t.apply(null,o)}catch(e){console.error(e)}}))};var lt=function(t){t.fontSize=t.fontSize?t.fontSize*t.pixelRatio:13*t.pixelRatio,t.title=t.title||{},t.subtitle=t.subtitle||{},t.yAxis=t.yAxis||{},t.yAxis.gridType=t.yAxis.gridType?t.yAxis.gridType:"solid",t.yAxis.dashLength=t.yAxis.dashLength?t.yAxis.dashLength:4*t.pixelRatio,t.xAxis=t.xAxis||{},t.xAxis.rotateLabel=!!t.xAxis.rotateLabel,t.xAxis.type=t.xAxis.type?t.xAxis.type:"calibration",t.xAxis.gridType=t.xAxis.gridType?t.xAxis.gridType:"solid",t.xAxis.dashLength=t.xAxis.dashLength?t.xAxis.dashLength:4*t.pixelRatio,t.xAxis.scrollAlign=t.xAxis.scrollAlign?t.xAxis.scrollAlign:"left",t.extra=t.extra||{},t.legend=!1!==t.legend,t.rotate=!!t.rotate,t.animation=!1!==t.animation;var e=n({},o);if(e.yAxisTitleWidth=!0!==t.yAxis.disabled&&t.yAxis.title?e.yAxisTitleWidth:0,"pie"!=t.type&&"ring"!=t.type||(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.pie.lableWidth*t.pixelRatio||e.pieChartLinePadding*t.pixelRatio),e.pieChartTextPadding=!1===t.dataLabel?0:e.pieChartTextPadding*t.pixelRatio,e.yAxisSplit=t.yAxis.splitNumber?t.yAxis.splitNumber:o.yAxisSplit,e.rotate=t.rotate,t.rotate){var i=t.width,a=t.height;t.width=a,t.height=i}if(e.yAxisWidth=o.yAxisWidth*t.pixelRatio,e.xAxisHeight=o.xAxisHeight*t.pixelRatio,t.enableScroll&&t.xAxis.scrollShow&&(e.xAxisHeight+=6*t.pixelRatio),e.xAxisLineHeight=o.xAxisLineHeight*t.pixelRatio,e.legendHeight=o.legendHeight*t.pixelRatio,e.padding=o.padding*t.pixelRatio,e.fontSize=t.fontSize,e.titleFontSize=o.titleFontSize*t.pixelRatio,e.subtitleFontSize=o.subtitleFontSize*t.pixelRatio,e.toolTipPadding=o.toolTipPadding*t.pixelRatio,e.toolTipLineHeight=o.toolTipLineHeight*t.pixelRatio,e.columePadding=o.columePadding*t.pixelRatio,o.pixelRatio=t.pixelRatio,o.fontSize=t.fontSize,o.rotate=t.rotate,this.opts=t,this.config=e,t.$this=t.$this?t.$this:this,this.context=uni.createCanvasContext(t.canvasId,t.$this),this.chartData={},this.event=new rt,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},t.enableScroll&&"right"==t.xAxis.scrollAlign){var r=B(t.series,t,e),l=r.yAxisWidth;e.yAxisWidth=l;var s,h=I(t.categories,t,e),c=h.xAxisPoints,d=h.startX,p=h.endX,u=h.eachSpacing,x=u*(c.length-1),f=p-d;s=f-x,this.scrollOption={currentOffset:s,startTouchX:s,distance:0,lastMoveTime:0},t._scrollDistance_=s}nt.call(this,t.type,t,e,this.context)};lt.prototype.updateData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=n({},this.opts,t),this.opts.yAxis.format=t.unit?function(e){return e.toFixed(t.fixed||0)+(t.unit||"")}:this.opts.yAxis.format;var e=t.scrollPosition||"current";switch(e){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":var i=B(this.opts.series,this.opts,this.config),a=i.yAxisWidth;this.config.yAxisWidth=a;var o=0,r=I(this.opts.categories,this.opts,this.config),l=r.xAxisPoints,s=r.startX,h=r.endX,c=r.eachSpacing,d=c*(l.length-1),p=h-s;o=p-d,this.scrollOption={currentOffset:o,startTouchX:o,distance:0,lastMoveTime:0},this.opts._scrollDistance_=o;break}var u=void 0==t.animation?this.opts.animation:t.animation;this.opts.animation=u,this.opts.title=n({},this.opts.title,t.title||{}),this.opts.subtitle=n({},this.opts.subtitle,t.subtitle||{}),nt.call(this,this.opts.type,this.opts,this.config,this.context)},lt.prototype.zoom=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0===this.opts.enableScroll){var e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;var i=B(this.opts.series,this.opts,this.config),a=i.yAxisWidth;this.config.yAxisWidth=a;var o=0,n=I(this.opts.categories,this.opts,this.config),r=n.xAxisPoints,l=n.startX,s=n.endX,h=n.eachSpacing,c=h*e,d=s-l,p=d-h*(r.length-1);o=d/2-c,o>0&&(o=0),o<p&&(o=p),this.scrollOption={currentOffset:o,startTouchX:o,distance:0,lastMoveTime:0},this.opts._scrollDistance_=o,nt.call(this,this.opts.type,this.opts,this.config,this.context)}else console.log("请启用滚动条后使用！")},lt.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},lt.prototype.addEventListener=function(t,e){this.event.addEventListener(t,e)},lt.prototype.getCurrentDataIndex=function(t){var e=t.mp.changedTouches[0]||t.changedTouches[0];if(e){var i=f(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type?function(t,e){var i=-1;if(P(t,e.center,e.radius)){var a=Math.atan2(e.center.y-t.y,t.x-e.center.x);a=-a;for(var o=0,n=e.series.length;o<n;o++){var r=e.series[o];if(h(a,r._start_,r._start_+2*r._proportion_*Math.PI)){i=o;break}}}return i}({x:i.x,y:i.y},this.chartData.pieData):"radar"===this.opts.type?function(t,e,i){var a=2*Math.PI/i,o=-1;if(P(t,e.center,e.radius)){var n=function(t){return t<0&&(t+=2*Math.PI),t>2*Math.PI&&(t-=2*Math.PI),t},r=Math.atan2(e.center.y-t.y,t.x-e.center.x);r*=-1,r<0&&(r+=2*Math.PI);var l=e.angleList.map((function(t){return t=n(-1*t),t}));l.forEach((function(t,e){var i=n(t-a/2),l=n(t+a/2);l<i&&(l+=2*Math.PI),(r>=i&&r<=l||r+2*Math.PI>=i&&r+2*Math.PI<=l)&&(o=e)}))}return o}({x:i.x,y:i.y},this.chartData.radarData,this.opts.categories.length):function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=-1;return T(t,i,a)&&e.forEach((function(e,i){t.x+o>e&&(n=i)})),n}({x:i.x,y:i.y},this.chartData.xAxisPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},lt.prototype.showToolTip=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=t.mp.changedTouches[0]||t.changedTouches[0],a=f(i,this.opts,t);if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type){var o=this.getCurrentDataIndex(t),r=this.scrollOption.currentOffset,l=n({},this.opts,{_scrollDistance_:r,animation:!1});if(o>-1){var s=g(this.opts.series,o);if(0!==s.length){var h=b(s,this.chartData.calPoints,o,this.opts.categories,this.opts),c=h.textList,d=h.offset;d.y=a.y,l.tooltip={textList:c,offset:d,option:e,index:o}}}nt.call(this,l.type,l,this.config,this.context)}if("mix"===this.opts.type){o=this.getCurrentDataIndex(t),r=this.scrollOption.currentOffset,l=n({},this.opts,{_scrollDistance_:r,animation:!1});if(o>-1){s=g(this.opts.series,o);if(0!==s.length){var p=m(s,this.chartData.calPoints,o,this.opts.categories,e);c=p.textList,d=p.offset;d.y=a.y,l.tooltip={textList:c,offset:d,option:e,index:o}}}nt.call(this,l.type,l,this.config,this.context)}if("candle"===this.opts.type){o=this.getCurrentDataIndex(t),r=this.scrollOption.currentOffset,l=n({},this.opts,{_scrollDistance_:r,animation:!1});if(o>-1){s=g(this.opts.series,o);if(0!==s.length){h=A(this.opts.series[0].data,s,this.chartData.calPoints,o,this.opts.categories,this.opts.extra.candle,e),c=h.textList,d=h.offset;d.y=a.y,l.tooltip={textList:c,offset:d,option:e,index:o}}}nt.call(this,l.type,l,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type){o=this.getCurrentDataIndex(t),r=this.scrollOption.currentOffset,l=n({},this.opts,{_scrollDistance_:r,animation:!1});if(o>-1){s=this.opts.series[o],c=[{text:e.format?e.format(s):s.name+": "+s.data,color:s.color}],d={x:a.x,y:a.y};l.tooltip={textList:c,offset:d,option:e,index:o}}nt.call(this,l.type,l,this.config,this.context)}if("radar"===this.opts.type){o=this.getCurrentDataIndex(t),r=this.scrollOption.currentOffset,l=n({},this.opts,{_scrollDistance_:r,animation:!1});if(o>-1){s=g(this.opts.series,o);if(0!==s.length){c=s.map((function(t){return{text:e.format?e.format(t):t.name+": "+t.data,color:t.color}})),d={x:a.x,y:a.y};l.tooltip={textList:c,offset:d,option:e,index:o}}}nt.call(this,l.type,l,this.config,this.context)}},lt.prototype.translate=function(t){this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0};var e=n({},this.opts,{_scrollDistance_:t,animation:!1});nt.call(this,this.opts.type,e,this.config,this.context)},lt.prototype.scrollStart=function(t){var e=t.mp.changedTouches[0]||t.changedTouches[0],i=f(e,this.opts,t);e&&!0===this.opts.enableScroll&&(e.x?this.scrollOption.startTouchX=i.x:this.scrollOption.startTouchX=i.clientX)},lt.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());var e=this.opts.extra.touchMoveLimit||20,i=Date.now(),a=i-this.scrollOption.lastMoveTime;if(!(a<Math.floor(1e3/e))){this.scrollOption.lastMoveTime=i;var o=t.mp.changedTouches[0]||t.changedTouches[0],r=f(o,this.opts,t);if(o&&!0===this.opts.enableScroll){var l;l=o.x?r.x-this.scrollOption.startTouchX:r.clientX-this.scrollOption.startTouchX;var s=this.scrollOption.currentOffset,h=function(t,e,i,a){var o=a.width-i.padding-e.xAxisPoints[0],n=e.eachSpacing*a.categories.length,r=t;return t>=0?r=0:Math.abs(t)>=n-o&&(r=o-n),r}(s+l,this.chartData,this.config,this.opts);this.scrollOption.distance=l=h-s;var c=n({},this.opts,{_scrollDistance_:s+l,animation:!1});return nt.call(this,c.type,c,this.config,this.context),s+l}}},lt.prototype.scrollEnd=function(t){if(!0===this.opts.enableScroll){var e=this.scrollOption,i=e.currentOffset,a=e.distance;this.scrollOption.currentOffset=i+a,this.scrollOption.distance=0}},t.exports=lt},"6bc3":function(t,e,i){"use strict";i.r(e);var a=i("fce9"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},b03f:function(t,e,i){"use strict";i.r(e);var a=i("049f"),o=i("6bc3");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("e64b");var r=i("828b"),l=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"dae8f946",null,!1,a["a"],void 0);e["default"]=l.exports},d629:function(t,e,i){var a=i("f1ef");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("04f565f2",a,!0,{sourceMap:!1,shadowMode:!1})},e64b:function(t,e,i){"use strict";var a=i("d629"),o=i.n(a);o.a},f1ef:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".charts[data-v-dae8f946]{width:100%;height:100%;flex:1;background-color:#fff}.canvas[data-v-dae8f946]{margin:auto!important}",""]),t.exports=e},fce9:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var o=a(i("31c6")),n={},r={props:{chartType:{required:!0,type:String,default:"column"},extraType:{type:String,default:"group"},opts:{required:!0,type:Object,default:function(){return null}},canvasId:{type:String,default:"u-canvas"},cWidth:{type:Number,default:375},cHeight:{type:Number,default:250},pixelRatio:{type:Number,default:1},show:{type:Boolean,default:!0},scrollTop:{type:Number,default:0}},mounted:function(){this.init()},methods:{init:function(){this.opts.type=this.chartType,this.extraType&&(this.opts.extra&&this.opts.extra[this.chartType]?this.opts.extra[this.chartType].type=this.extraType:this.opts.extra?this.opts.extra[this.chartType]={type:this.extraType}:this.opts.extra=JSON.parse('{"'+this.chartType+'":{"type":"'+this.extraType+'"}}')),n[this.canvasId]=o.default.showCharts(this.canvasId,this.opts,this)},changeData:function(t,e,i,a){i&&(e.type=i),a&&(e.extra&&e.extra[i]?e.extra[i].type=a:e.extra?e.extra[i]={type:a}:e.extra=JSON.parse('{"'+i+'":{"type":"'+a+'"}}')),n[t].updateData(e)},touchStart:function(t){t=this.touchY(t,this.scrollTop),n[this.canvasId].showToolTip(t,{format:function(t,e){return(e||"")+" "+t.name+":"+(t.data.value||t.data)}}),n[this.canvasId].scrollStart(t)},touchMove:function(t){t=this.touchY(t,this.scrollTop),n[this.canvasId].scroll(t)},touchEnd:function(t){t=this.touchY(t,this.scrollTop),n[this.canvasId].scrollEnd(t)},error:function(t){console.log(t)},touchY:function(t,e){var i=t.changedTouches?t.changedTouches[0].y:t.mp.changedTouches[0].y;return t.changedTouches?t.changedTouches[0].y=i<0?i+e:i:t.mp.changedTouches[0].y=i<0?i+e:i,t}}};e.default=r}}]);