(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-detail-basis"],{"13ae":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"UniCountDown",props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},backgroundColorClass:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderColorClass:{type:String,default:""},color:{type:String,default:"#000000"},colorClass:{type:String,default:""},splitorColor:{type:String,default:"#000000"},splitorColorClass:{type:String,default:""},day:{type:[Number,String],default:0},hour:{type:[Number,String],default:0},minute:{type:[Number,String],default:0},second:{type:[Number,String],default:0}},data:function(){return{timer:null,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},mounted:function(t){var e=this;this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},watch:{day:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},hour:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},minute:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)},second:function(t){var e=this;this.timeUp(),this.seconds=this.toSeconds(this.day,this.hour,this.minute,this.second),this.countDown(),this.timer=setInterval((function(){e.seconds--,e.seconds<0?e.timeUp():e.countDown()}),1e3)}},beforeDestroy:function(){clearInterval(this.timer)},methods:{toSeconds:function(t,e,i,a){return t=Number(t),e=Number(e),i=Number(i),a=Number(a),60*t*60*24+60*e*60+60*i+a},timeUp:function(){clearInterval(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,i=0,a=0,o=0;t>0?(e=Math.floor(t/86400),i=Math.floor(t/3600)-24*e,a=Math.floor(t/60)-24*e*60-60*i,o=Math.floor(t)-24*e*60*60-60*i*60-60*a):this.timeUp(),e<10&&(e="0"+e),i<10&&(i="0"+i),a<10&&(a="0"+a),o<10&&(o="0"+o),this.d=e,this.h=i,this.i=a,this.s=o}}};e.default=a},37597:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={nsOrderRemark:i("4003").default,loadingCover:i("59c1").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"order-detail-wrap"},[i("v-uni-view",{staticClass:"status-wrap color-base-bg",style:{backgroundImage:"url("+t.$util.img("public/uniapp/order/status-wrap-bg.png")+")"}},[i("v-uni-view",{staticClass:"container"},[i("v-uni-image",{attrs:{src:t.$util.img(t.orderDetail.order_status_action.icon)}}),i("v-uni-view",{staticClass:"status-name"},[t._v(t._s(t.orderDetail.order_status_name)),t.orderDetail.promotion_status_name?[t._v("("+t._s(t.orderDetail.promotion_status_name)+")")]:t._e()],2)],1)],1),"online"==t.orderDetail.order_scene?i("v-uni-view",{staticClass:"address-wrap"},[i("v-uni-view",{staticClass:"icon"},[i("v-uni-view",{staticClass:"iconfont iconlocation"})],1),i("v-uni-view",{staticClass:"address-info"},[i("v-uni-view",{staticClass:"info"},[t._v(t._s(t.orderDetail.name)+" "+t._s(t.orderDetail.mobile))]),i("v-uni-view",{staticClass:"detail"},[t._v("收货地址："+t._s(t.orderDetail.full_address)+" "+t._s(t.orderDetail.address))])],1)],1):t._e(),i("v-uni-view",{staticClass:"block-wrap"},t._l(t.orderDetail.order_goods,(function(e,a){return i("v-uni-view",{key:a,staticClass:"goods-item-wrap"},[i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-img"},[i("v-uni-image",{attrs:{src:t.$util.img(e.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(a)}}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[t._v(t._s(e.goods_name))]),e.sku_spec_format?i("v-uni-view",{staticClass:"spec-wrap"},[t._l(e.sku_spec_format,(function(i,a){return[t._v(t._s(i.spec_value_name)+" "+t._s(a<e.sku_spec_format.length-1?"; ":""))]}))],2):t._e(),i("v-uni-view",{staticClass:"more-wrap"},[i("v-uni-view",{staticClass:"goods-class"},[t._v(t._s(e.goods_class_name)),1==t.orderDetail.order_type&&1==t.orderDetail.order_status&&1==e.delivery_status?i("v-uni-text",{staticClass:"color-base-text delivery-status"},[t._v("已发货")]):t._e()],1),1==e.is_present?i("v-uni-text",{staticClass:"present-label color-base-bg"},[t._v("赠品")]):t._e(),i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-view",{staticClass:"price"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(e.price))])],1),i("v-uni-view",{staticClass:"num"},[t._v("x"+t._s(e.num))])],1)],1)],1),0!=e.refund_status?i("v-uni-view",{staticClass:"action-wrap",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.goRefund(e.order_goods_id)}}},[i("v-uni-button",{attrs:{type:"primary",size:"mini"}},[t._v(t._s(e.refund_status_name))])],1):1==t.orderDetail.is_enable_refund&&"blindbox"!=t.orderDetail.promotion_type&&0==e.shop_active_refund?i("v-uni-view",{staticClass:"action-wrap"},[i("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.shopActiveRefund(e.order_goods_id)}}},[t._v("主动退款")])],1):t._e()],1),e.form?i("v-uni-view",{staticClass:"goods-form"},t._l(e.form,(function(e,a){return i("v-uni-view",{key:a,staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v(t._s(e.value.title)+"：")]),"Img"==e.controller?i("v-uni-view",{staticClass:"box img-box"},t._l(e.img_lists,(function(e,a){return i("v-uni-view",{key:a,staticClass:"img"},[i("v-uni-image",{attrs:{src:t.$util.img(e),mode:"widthFix"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.previewMedia(t.$util.img(e))}}})],1)})),1):i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.val))]),i("v-uni-text",{staticClass:"copy color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.copy(e.val)}}},[t._v("复制")])],1)],1)})),1):t._e()],1)})),1),t.orderDetail.form?i("v-uni-view",{staticClass:"block-wrap order-form"},t._l(t.orderDetail.form,(function(e,a){return i("v-uni-view",{key:a,staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v(t._s(e.value.title)+"：")]),"Img"==e.controller?i("v-uni-view",{staticClass:"box img-box"},t._l(e.img_lists,(function(e,a){return i("v-uni-view",{key:a,staticClass:"img"},[i("v-uni-image",{attrs:{src:t.$util.img(e),mode:"widthFix"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.previewMedia(t.$util.img(e))}}})],1)})),1):i("v-uni-view",{staticClass:"box"},[i("v-uni-text",{staticClass:"color-title"},[t._v(t._s(e.val))]),i("v-uni-text",{staticClass:"copy color-base-text",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$util.copy(e.val)}}},[t._v("复制")])],1)],1)})),1):t._e(),i("v-uni-view",{staticClass:"block-wrap"},[i("v-uni-view",{staticClass:"title"},[t._v("订单信息")]),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("订单编号：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.order_no))]),i("v-uni-view",{staticClass:"copy color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.copy(t.orderDetail.order_no)}}},[t._v("复制")])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("订单类型：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.order_type_name))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("订单来源：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.order_from_name))])],1),t.orderDetail.store_id?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("来源门店：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.store_name||"--"))])],1):t._e(),t.orderDetail.pay_status>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("付款方式：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.pay_type_name))])],1):t._e(),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("买家：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.member_id?t.orderDetail.nickname:"散客"))]),t.orderDetail.member_id?i("v-uni-view",{staticClass:"copy color-base-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$util.redirectTo("/pages/member/detail",{member_id:t.orderDetail.member_id})}}},[t._v("查看")]):t._e()],1),t.orderDetail.delivery_type_name?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("配送方式：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.delivery_type_name))])],1):t._e(),""!=t.orderDetail.promotion_type_name?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("营销活动：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.promotion_type_name))])],1):t._e(),""!=t.orderDetail.buyer_message?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("买家留言：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.buyer_message))])],1):t._e(),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("创建时间：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.create_time)))])],1),t.orderDetail.close_time>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("关闭时间：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.close_time)))])],1):t._e(),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("支付时间：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.$util.timeStampTurnTime(t.orderDetail.pay_time)))])],1),t.orderDetail.remark?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("卖家备注：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.remark))])],1):t._e()],1),1==t.orderDetail.is_invoice?[i("v-uni-view",{staticClass:"block-wrap"},[i("v-uni-view",{staticClass:"title"},[t._v("发票信息")]),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("发票类型：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(1==t.orderDetail["invoice_type"]?"纸质":"电子")+t._s(1==t.orderDetail["is_tax_invoice"]?"专票":"普票"))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("发票抬头：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_title))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("抬头类型：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(1==t.orderDetail.invoice_title_type?"个人":"企业"))])],1),2==t.orderDetail.invoice_title_type?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("纳税人识别号：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.taxpayer_number))])],1):t._e(),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("发票内容：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_content))])],1),1==t.orderDetail.invoice_title_type?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("邮寄地址：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_full_address))])],1):i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("接收邮件：")]),i("v-uni-view",{staticClass:"box"},[t._v(t._s(t.orderDetail.invoice_email))])],1)],1),i("v-uni-view",{staticClass:"block-wrap"},[i("v-uni-view",{staticClass:"title"},[t._v("发票费用")]),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("发票费用：")]),i("v-uni-view",{staticClass:"box align-right money"},[t._v("￥"+t._s(t.orderDetail.invoice_money))])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("发票税率：")]),i("v-uni-view",{staticClass:"box align-right money"},[t._v(t._s(t.orderDetail.invoice_rate)+"%")])],1),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("邮寄费用：")]),i("v-uni-view",{staticClass:"box align-right money"},[t._v("￥"+t._s(t.orderDetail.invoice_delivery_money))])],1)],1)]:t._e(),i("v-uni-view",{staticClass:"block-wrap"},[i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("商品金额")]),i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",{staticClass:"color-title"},[t._v(t._s(t.orderDetail.goods_money))])],1)],1),t.orderDetail.delivery_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("运费")]),i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.delivery_money))])],1)],1):t._e(),t.orderDetail.member_card_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("会员卡")]),i("v-uni-view",{staticClass:"box align-right"},[i("v-uni-text",{staticClass:"color-base-text"},[i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.member_card_money))])],1)],1)],1):t._e(),t.orderDetail.invoice_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("税费"),i("v-uni-text",{staticClass:"color-base-text"},[t._v("("+t._s(t.orderDetail.invoice_rate)+"%)")])],1),i("v-uni-view",{staticClass:"box align-right"},[i("v-uni-text",{staticClass:"color-base-text"},[i("v-uni-text",{staticClass:"operator"},[t._v("+")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.invoice_money))])],1)],1)],1):t._e(),t.orderDetail.invoice_delivery_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("发票邮寄费")]),i("v-uni-view",{staticClass:"box align-right"},[i("v-uni-text",{staticClass:"color-base-text"},[i("v-uni-text",{staticClass:"operator"},[t._v("+")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.invoice_delivery_money))])],1)],1)],1):t._e(),t.orderDetail.coupon_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("优惠券")]),i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",{staticClass:"operator"},[t._v("-")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.coupon_money))])],1)],1):t._e(),t.orderDetail.promotion_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("优惠")]),i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",{staticClass:"operator"},[t._v("-")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.promotion_money))])],1)],1):t._e(),0!=t.orderDetail.adjust_money?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("订单调价")]),i("v-uni-view",{staticClass:"box align-right money bold"},[t.orderDetail.adjust_money<0?i("v-uni-text",{staticClass:"operator"},[t._v("-")]):i("v-uni-text",{staticClass:"operator"},[t._v("+")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t._f("abs")(t.orderDetail.adjust_money)))])],1)],1):t._e(),t.orderDetail.balance_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("使用余额")]),i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",{staticClass:"operator"},[t._v("-")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.balance_money))])],1)],1):t._e(),t.orderDetail.point_money>0?i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-text",{staticClass:"tit"},[t._v("积分抵扣")]),i("v-uni-view",{staticClass:"box align-right"},[i("v-uni-text",{staticClass:"color-base-text"},[i("v-uni-text",{staticClass:"operator"},[t._v("-")]),i("v-uni-text",{staticClass:"font-size-goods-tag"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(t.orderDetail.point_money))])],1)],1)],1):t._e(),i("v-uni-view",{staticClass:"order-cell"},[i("v-uni-view",{staticClass:"box align-right money bold"},[i("v-uni-text",[t._v("共"+t._s(t.orderDetail.goods_num)+"件商品，实付金额：")]),i("v-uni-text",{staticClass:"font-size-goods-tag color-base-text"},[t._v("￥")]),i("v-uni-text",{staticClass:"font-size-base color-base-text"},[t._v(t._s(t.orderDetail.pay_money))])],1)],1)],1),t.log.length?i("v-uni-view",{staticClass:"block-wrap log"},[i("v-uni-view",{staticClass:"title color-base-text"},[t._v("订单日志")]),t._l(t.log,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item"},[i("v-uni-view",{staticClass:"action"},[i("v-uni-view",{staticClass:"title"},[i("v-uni-text",{staticClass:"font-size-base"},[t._v("操作："+t._s(e.action))]),i("v-uni-text",{staticClass:"color-sub time"},[t._v(t._s(t.$util.timeStampTurnTime(e.action_time,"Y-m-d")))])],1),i("v-uni-view",[i("v-uni-text",{staticClass:"color-tip"},[t._v("操作人："+t._s(e.nick_name))]),i("br"),i("v-uni-text",{staticClass:" color-tip"},[t._v("操作时间："+t._s(t.$util.timeStampTurnTime(e.action_time)))]),i("br"),i("v-uni-text",{staticClass:" color-tip"},[t._v("订单状态："+t._s(e.order_status_name))])],1)],1)],1)}))],2):t._e(),i("v-uni-view",{staticClass:"block-wrap tips"},[i("v-uni-view",{staticClass:"title color-base-text"},[t._v("提醒")]),i("v-uni-text",[t._v("请及时关注你发出的包裹状态，确保能配送至买家手中")]),i("v-uni-text",[t._v("如果买家表示未收到货或者货物有问题，请及时联系买家积极处理，友好协商")])],1),i("v-uni-view",{staticClass:"placeholder-height"}),i("v-uni-view",{staticClass:"footer-wrap"},[i("v-uni-view",{staticClass:"container"},[i("v-uni-button",{attrs:{type:"primary",size:"mini",plain:!0},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.orderRemark()}}},[t._v("备注")]),t._l(t.orderDetail.order_status_action.action,(function(e,a){return i("v-uni-button",{key:a,attrs:{type:"primary",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.orderAction(e.action,t.orderId)}}},[t._v(t._s(e.title))])})),0==t.orderDetail.order_status?i("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.offlinePay(t.orderId)}}},[t._v("线下支付")]):t._e()],2)],1),i("ns-order-remark",{ref:"orderRemark",attrs:{order:t.tempOrder}}),i("loading-cover",{ref:"loadingCover"})],2)},s=[]},"4cea":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"uni-countdown"},[t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.d))]):t._e(),t.showDay&&t.d>0?i("v-uni-view",{staticClass:"uni-countdown__splitor day",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s((t.showColon,"天")))]):t._e(),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.h))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"时"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.i))]),i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v(t._s(t.showColon?":":"分"))]),i("v-uni-view",{staticClass:"uni-countdown__number ",class:[t.backgroundColorClass,t.colorClass,t.borderColorClass],style:{borderColor:t.borderColor,color:t.color,background:t.backgroundColor}},[t._v(t._s(t.s))]),t.showColon?t._e():i("v-uni-view",{staticClass:"uni-countdown__splitor",class:t.splitorColorClass,style:{color:t.splitorColor}},[t._v("秒")])],1)},o=[]},"4d89":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-e9e34d7a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-e9e34d7a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-e9e34d7a]{position:fixed;left:0;right:0;z-index:998}.uni-countdown[data-v-e9e34d7a]{padding:%?2?% 0;display:inline-flex;flex-wrap:nowrap;justify-content:center}.uni-countdown__splitor[data-v-e9e34d7a]{justify-content:center;line-height:%?44?%;padding:0 %?5?%;font-size:%?24?%}.uni-countdown__splitor.day[data-v-e9e34d7a]{line-height:%?50?%}.uni-countdown__number[data-v-e9e34d7a]{line-height:%?44?%;justify-content:center;height:%?44?%;border-radius:%?6?%;margin:0 %?5?%;font-size:%?28?%;border:1px solid #000;font-size:%?24?%;padding:0 %?10?%}',""]),t.exports=e},5695:function(t,e,i){"use strict";i.r(e);var a=i("4cea"),o=i("df68");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("c4f9");var r=i("828b"),n=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"e9e34d7a",null,!1,a["a"],void 0);e["default"]=n.exports},"6b38":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".time[data-v-0ce2b6b0] .uni-countdown{margin:0}.time[data-v-0ce2b6b0] .uni-countdown__number{border-radius:0;padding:0 %?4?%;margin:0;border:none;font-size:14px;line-height:1}.time[data-v-0ce2b6b0] .uni-countdown__splitor{font-size:14px;line-height:1}",""]),t.exports=e},"75f6":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bf0f"),i("2797"),i("d4b5"),i("e838");var a=i("6638"),o={data:function(){return{isIphoneX:!1,orderId:0,template:"",orderDetail:{order_status_action:{icon:""},virtual_goods:{}},tempOrder:{remark:""},log:[]}},onLoad:function(t){this.orderId=t.order_id||0,this.template=t.template||"basis"},onShow:function(){var t=this,e="/pages/order/detail/"+this.template+"?order_id="+this.orderId+"&template="+this.template;this.$util.checkToken(e)&&(this.getOrderDetail(),this.isIphoneX=this.$util.uniappIsIPhoneX(),this.actionCallback=function(){t.getOrderDetail()},this.getLogList())},methods:{getOrderDetail:function(){var t=this;(0,a.getOrderDetailInfoById)(this.orderId).then((function(e){0==e.code?(t.orderDetail=e.data,t.orderDetail.order_status_action=JSON.parse(t.orderDetail.order_status_action),t.orderDetail.order_goods.forEach((function(t){t.sku_spec_format=t.sku_spec_format?JSON.parse(t.sku_spec_format):[]})),t.$refs.loadingCover&&t.$refs.loadingCover.hide()):(t.$util.showToast({title:e.message}),setTimeout((function(){t.$util.redirectTo("/pages/order/list",{},"redirectTo")}),1e3))}))},getLogList:function(){var t=this;(0,a.getOrderLog)(this.orderId).then((function(e){e.code>=0&&e.data&&(t.log=e.data)}))},imgError:function(t){this.orderDetail.order_goods[t].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},orderRemark:function(){var t=this;this.tempOrder=JSON.parse(JSON.stringify(this.orderDetail)),this.$refs.orderRemark.show((function(){t.getOrderDetail()}))},orderAction:function(t,e){try{this[t](e)}catch(i){console.log("orderAction error：",i)}},trace:function(){this.$util.redirectTo("/pages/order/logistics",{order_id:this.orderId})}},filters:{abs:function(t){return Math.abs(parseFloat(t)).toFixed(2)}}};e.default=o},"7d2b":function(t,e,i){"use strict";i.r(e);var a=i("ca09"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},8650:function(t,e,i){"use strict";var a=i("d756"),o=i.n(a);o.a},"903d":function(t,e,i){"use strict";i.r(e);var a=i("37597"),o=i("7d2b");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("958b"),i("8650");var r=i("828b"),n=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"0ce2b6b0",null,!1,a["a"],void 0);e["default"]=n.exports},"958b":function(t,e,i){"use strict";var a=i("f65e"),o=i.n(a);o.a},ac00:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-0ce2b6b0]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-0ce2b6b0]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-0ce2b6b0]{position:fixed;left:0;right:0;z-index:998}.order-detail-wrap[data-v-0ce2b6b0]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?30?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?30?%)}.align-right[data-v-0ce2b6b0]{text-align:right}.status-wrap[data-v-0ce2b6b0]{background-size:100% 100%;padding:%?40?%;height:%?180?%}.status-wrap uni-image[data-v-0ce2b6b0]{width:%?104?%;height:%?86?%;margin-right:%?20?%;margin-top:%?20?%}.status-wrap .container[data-v-0ce2b6b0]{display:flex;align-items:center}.status-wrap .container .status-name uni-view[data-v-0ce2b6b0]{font-size:%?32?%;color:#fff;line-height:1;margin-top:%?20?%;text-align:left}.status-wrap .container .status-name uni-view.time[data-v-0ce2b6b0]{margin-top:%?20?%;font-size:%?28?%}.address-wrap[data-v-0ce2b6b0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative;min-height:%?100?%;margin-top:%?-69?%}.address-wrap .icon[data-v-0ce2b6b0]{position:absolute;top:%?10?%;margin-right:%?20?%}.address-wrap .icon .iconfont[data-v-0ce2b6b0]{line-height:%?50?%;font-size:%?28?%}.address-wrap .address-info[data-v-0ce2b6b0]{padding-left:%?40?%}.address-wrap .address-info .info[data-v-0ce2b6b0]{display:flex;line-height:1}.address-wrap .address-info .detail[data-v-0ce2b6b0]{line-height:1.3;margin-top:%?20?%}.block-wrap[data-v-0ce2b6b0]{margin:%?20?% %?30?%;padding:%?20?% %?30?%;border-radius:%?10?%;background:#fff;position:relative}.block-wrap .goods-item-wrap[data-v-0ce2b6b0]{border-bottom:%?2?% solid #f5f5f5;padding-bottom:%?20?%}.block-wrap .goods-item-wrap[data-v-0ce2b6b0]:last-child{border-bottom:0}.block-wrap .goods-item[data-v-0ce2b6b0]{background:#fff;margin-top:%?20?%;display:flex;position:relative;flex-flow:row wrap}.block-wrap .goods-item .goods-img[data-v-0ce2b6b0]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.block-wrap .goods-item .goods-img uni-image[data-v-0ce2b6b0]{width:100%;height:100%}.block-wrap .goods-item .info-wrap[data-v-0ce2b6b0]{flex:1;display:flex;flex-direction:column;width:50%}.block-wrap .goods-item .info-wrap .name-wrap[data-v-0ce2b6b0]{line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.block-wrap .goods-item .info-wrap .spec-wrap[data-v-0ce2b6b0]{line-height:1;margin-top:%?10?%;margin-bottom:%?10?%;color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.block-wrap .goods-item .info-wrap .more-wrap[data-v-0ce2b6b0]{display:flex}.block-wrap .goods-item .info-wrap .more-wrap .goods-class[data-v-0ce2b6b0]{font-size:%?20?%;color:#909399}.block-wrap .goods-item .info-wrap .more-wrap .goods-class .delivery-status[data-v-0ce2b6b0]{padding-left:%?6?%}.block-wrap .goods-item .info-wrap .more-wrap .present-label[data-v-0ce2b6b0]{padding:0 %?6?%;display:inline-block;color:#fff;font-size:%?24?%;border-radius:%?10?%;width:-webkit-fit-content;width:fit-content;position:absolute;margin-left:%?110?%}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap[data-v-0ce2b6b0]{flex:1;text-align:right}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .unit[data-v-0ce2b6b0]{font-size:%?20?%}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .price[data-v-0ce2b6b0]{display:inline-block;line-height:1;flex:1}.block-wrap .goods-item .info-wrap .more-wrap .price-wrap .num[data-v-0ce2b6b0]{color:#909399;font-size:%?20?%;line-height:1}.block-wrap .goods-item .info-wrap .delivery-status-name[data-v-0ce2b6b0]{font-weight:700;text-align:right;line-height:1;margin-top:%?10?%}.block-wrap .goods-item .action-wrap[data-v-0ce2b6b0]{width:100%;text-align:right;margin-top:%?10?%}.block-wrap .goods-item .action-wrap uni-button[data-v-0ce2b6b0]{margin-right:%?20?%!important}.block-wrap .goods-item .action-wrap uni-button[data-v-0ce2b6b0]:last-child{margin-right:0!important}.block-wrap .title[data-v-0ce2b6b0]{font-size:%?32?%}.block-wrap .order-cell[data-v-0ce2b6b0]{display:flex;margin:%?20?% 0;align-items:center;background:#fff;line-height:%?40?%}.block-wrap .order-cell[data-v-0ce2b6b0]:first-child{margin-top:0}.block-wrap .order-cell .tit[data-v-0ce2b6b0]{width:%?200?%;text-align:left}.block-wrap .order-cell .box[data-v-0ce2b6b0]{flex:1;padding:0 %?20?%;line-height:inherit}.block-wrap .order-cell .box.money[data-v-0ce2b6b0]{padding:0}.block-wrap .order-cell .box.bold[data-v-0ce2b6b0]{font-weight:700}.block-wrap .order-cell .box .operator[data-v-0ce2b6b0]{font-size:%?24?%;margin-right:%?6?%}.block-wrap .order-cell .img-box[data-v-0ce2b6b0]{display:flex;flex-wrap:wrap}.block-wrap .order-cell .img-box .img[data-v-0ce2b6b0]{width:%?100?%;height:%?100?%;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:%?30?%;margin-bottom:%?30?%;position:relative;border-radius:%?10?%;line-height:1;overflow:hidden}.block-wrap .order-cell .img-box .img uni-image[data-v-0ce2b6b0]{width:100%}.block-wrap.tit-auto .tit[data-v-0ce2b6b0]{width:auto}.block-wrap.tips .title[data-v-0ce2b6b0]{font-size:%?28?%}.block-wrap.tips uni-text[data-v-0ce2b6b0]{font-size:%?24?%;display:block;margin-top:%?10?%}.block-wrap.log .title[data-v-0ce2b6b0]{font-size:%?28?%}.block-wrap.log .item[data-v-0ce2b6b0]{display:flex;align-items:center;margin-top:%?20?%}.block-wrap.log .item[data-v-0ce2b6b0]:last-child{margin-bottom:%?20?%}.block-wrap.log .item .tag[data-v-0ce2b6b0]{color:#fff;border-radius:50%;margin-right:%?20?%;padding:%?10?%}.block-wrap.log .item .action[data-v-0ce2b6b0]{flex:1}.block-wrap.log .item .action .title[data-v-0ce2b6b0]{display:flex;justify-content:space-between}.block-wrap.log .item uni-text[data-v-0ce2b6b0]{font-size:%?24?%}.placeholder-height[data-v-0ce2b6b0]{height:%?120?%}.footer-wrap[data-v-0ce2b6b0]{position:fixed;bottom:0;padding:%?20?% 0 %?10?%;z-index:10;width:100%;text-align:right;background-color:#fff;border-top:1px solid #eee;padding-bottom:calc(constant(safe-area-inset-bottom) + %?30?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?30?%)}.footer-wrap .container[data-v-0ce2b6b0]{margin:0 %?30?%}.footer-wrap .container uni-button[data-v-0ce2b6b0]{margin-left:%?20?%!important}.footer-wrap .container uni-button[data-v-0ce2b6b0]:first-child{margin-left:0!important}.goods-form .order-cell .box[data-v-0ce2b6b0]{padding-right:0}.goods-form .order-cell .copy[data-v-0ce2b6b0]{margin-left:%?10?%;float:right}.order-form .order-cell .box[data-v-0ce2b6b0]{padding-right:0}.order-form .order-cell .copy[data-v-0ce2b6b0]{margin-left:%?10?%;float:right}',""]),t.exports=e},b65d:function(t,e,i){var a=i("4d89");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("431f734f",a,!0,{sourceMap:!1,shadowMode:!1})},c4f9:function(t,e,i){"use strict";var a=i("b65d"),o=i.n(a);o.a},ca09:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("5695")),s=a(i("4003")),r=a(i("514b")),n=a(i("75f6")),l={components:{uniCountDown:o.default,nsOrderRemark:s.default},mixins:[r.default,n.default]};e.default=l},d756:function(t,e,i){var a=i("6b38");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("371fab97",a,!0,{sourceMap:!1,shadowMode:!1})},df68:function(t,e,i){"use strict";i.r(e);var a=i("13ae"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},f65e:function(t,e,i){var a=i("ac00");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("d4c2f4f6",a,!0,{sourceMap:!1,shadowMode:!1})}}]);