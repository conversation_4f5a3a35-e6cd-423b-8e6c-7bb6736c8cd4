(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-list"],{1186:function(t,e,i){"use strict";var a=i("7c27"),r=i.n(a);r.a},"185e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-2470a4dc]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-2470a4dc]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-2470a4dc]{position:fixed;left:0;right:0;z-index:998}uni-page-body[data-v-2470a4dc]{overflow:hidden}.nav-wrap[data-v-2470a4dc]{background-color:#fff;text-align:center;padding:%?30?% 0}.nav-wrap uni-text[data-v-2470a4dc]{border:1px solid;height:%?70?%;line-height:%?70?%;padding:0 %?40?%;display:inline-block;border-radius:%?40?%}.nav-wrap uni-text.selected[data-v-2470a4dc]{color:#fff}.nav-wrap uni-text[data-v-2470a4dc]:first-child{border-top-right-radius:0;border-bottom-right-radius:0}.nav-wrap uni-text[data-v-2470a4dc]:last-child{border-top-left-radius:0;border-bottom-left-radius:0}.search-wrap[data-v-2470a4dc]{display:flex;justify-content:space-between;background-color:#fff;margin:%?20?% %?30?%;border-radius:%?40?%}.search-wrap .search-input-inner[data-v-2470a4dc]{display:flex;align-items:center;width:%?460?%;height:%?70?%;padding:0 %?30?%;border-radius:%?100?%;box-sizing:border-box}.search-wrap .search-input-inner .search-input-icon[data-v-2470a4dc]{margin-right:%?10?%;color:#909399}.tab-block[data-v-2470a4dc]{background-color:#fff;position:relative;display:flex;margin:0 %?30?%}.tab-block .choose[data-v-2470a4dc]{position:absolute;right:0;min-width:50px;background-color:#fff;padding:%?20?% %?0?% 0 %?20?%;height:%?66?%}.tab-block .tab-wrap[data-v-2470a4dc]{width:calc(100% - %?120?%);position:relative;padding:%?24?% %?0?% 0 %?20?%;height:%?66?%;background-color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.tab-block .tab-wrap .tab-item[data-v-2470a4dc]{display:inline-block;position:relative;min-width:30px;margin:0 %?30?% 0 0;height:100%}.tab-block .tab-wrap .tab-item.iphone[data-v-2470a4dc]{height:calc(100% - %?24?%)}.tab-block .tab-wrap .tab-item .list-wrap[data-v-2470a4dc]{position:absolute;background:#fff;width:100%;padding:%?20?%;left:%?-20?%;top:%?60?%}.tab-block .tab-wrap .tab-item .list-wrap uni-text[data-v-2470a4dc]{display:block;margin-top:%?20?%}.tab-block .tab-wrap .tab-item .list-wrap uni-text[data-v-2470a4dc]:first-child{margin-top:0}.tab-block .tab-wrap .active[data-v-2470a4dc]{position:relative;margin-right:%?32?%}.tab-block .tab-wrap .active[data-v-2470a4dc]::after{content:"";position:absolute;bottom:0;left:0;height:%?4?%;width:100%}.order-item[data-v-2470a4dc]{margin:%?20?% %?30?%;background-color:#fff;padding:%?20?% %?30?%}.order-item .head[data-v-2470a4dc]{font-size:%?24?%}.order-item .head .order-no[data-v-2470a4dc]{color:#909399}.order-item .head .pay-type-name[data-v-2470a4dc]{margin-left:%?10?%}.order-item .head .order-status[data-v-2470a4dc]{float:right}.order-item .head .promotion-type-name[data-v-2470a4dc]{margin-right:%?10?%;float:right}.order-item .displayflex[data-v-2470a4dc]{display:flex;flex-direction:row;justify-content:space-between;align-items:center}.order-item .displayflex .displayflex-text[data-v-2470a4dc]{width:%?80?%;font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#909399;margin-top:%?10?%}.order-item .displayflex .positionr[data-v-2470a4dc]{position:relative}.order-item .displayflex .positiona[data-v-2470a4dc]{position:absolute;left:%?-20?%;top:%?55?%}.order-item .displayflex .boxshaow[data-v-2470a4dc]{box-shadow:0 0 %?30?% #ccc;width:%?200?%;min-height:%?40?%;background:#fff;border-radius:%?10?%;z-index:99}.order-item .displayflex .triangle-up[data-v-2470a4dc]{position:absolute;left:20%;top:%?-15?%;width:0;height:0;transform:translateX(-50%);-webkit-transform:translateX(-50%);-moz-transform:translateX(-50%);border-left:%?15?% solid transparent;border-right:%?15?% solid transparent;border-bottom:%?15?% solid #fff}.order-item .displayflex .boxshaow-btn[data-v-2470a4dc]{width:100%}.order-item .displayflex .boxshaow-btn uni-view[data-v-2470a4dc]{width:100%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#ff6a00;text-align:center;line-height:%?50?%}.order-item .member-wrap[data-v-2470a4dc]{position:relative;background-color:#f8f8f8;padding:%?10?% %?20?%;border-radius:%?10?%;margin:%?10?% 0}.order-item .member-wrap uni-text[data-v-2470a4dc],\r\n.order-item .member-wrap uni-view[data-v-2470a4dc]{font-size:%?24?%;vertical-align:middle}.order-item .member-wrap .nick-name[data-v-2470a4dc],\r\n.order-item .member-wrap .name[data-v-2470a4dc]{margin-right:%?20?%}.order-item .member-wrap .mobile-wrap[data-v-2470a4dc]{display:inline-block;vertical-align:middle}.order-item .member-wrap .mobile-wrap .iconfont[data-v-2470a4dc],\r\n.order-item .member-wrap .mobile-wrap .mobile[data-v-2470a4dc]{font-weight:700}.order-item .member-wrap .mobile-wrap .iconfont[data-v-2470a4dc]{margin-right:%?8?%}.order-item .member-wrap uni-view[data-v-2470a4dc]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.order-item .member-wrap .copy-sty[data-v-2470a4dc]{position:absolute;top:%?12?%;right:%?0?%;font-size:%?20?%;border:%?2?% solid #ff6a00;padding:%?0?% %?10?%;border-radius:%?10?%;color:#ff6a00}.order-item .remark-wrap[data-v-2470a4dc]{border-radius:%?10?%;padding:%?4?% %?10?%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:3;overflow:hidden;font-size:%?24?%}.order-item .goods-item[data-v-2470a4dc]{background:#fff;margin-top:%?20?%;display:flex;position:relative;flex-flow:row wrap}.order-item .goods-item .goods-img[data-v-2470a4dc]{width:%?160?%;height:%?160?%;overflow:hidden;border-radius:%?10?%;margin-right:%?20?%}.order-item .goods-item .goods-img uni-image[data-v-2470a4dc]{width:100%;height:100%}.order-item .goods-item .info-wrap[data-v-2470a4dc]{flex:1;max-width:%?450?%;display:flex;flex-direction:column}.order-item .goods-item .info-wrap .name-wrap[data-v-2470a4dc]{font-size:%?28?%;line-height:1.3;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-bottom:%?10?%}.order-item .goods-item .info-wrap .spec-wrap[data-v-2470a4dc]{line-height:1;margin-top:%?10?%;margin-bottom:%?10?%;color:#909399;font-size:%?22?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.order-item .goods-item .info-wrap .more-wrap[data-v-2470a4dc]{display:flex}.order-item .goods-item .info-wrap .more-wrap .left-wrap .goods-class[data-v-2470a4dc]{font-size:%?20?%;color:#909399}.order-item .goods-item .info-wrap .more-wrap .left-wrap .goods-class .delivery-status[data-v-2470a4dc]{padding-left:%?6?%}.order-item .goods-item .info-wrap .more-wrap .left-wrap .sku-no[data-v-2470a4dc]{font-size:%?20?%;color:#909399}.order-item .goods-item .info-wrap .more-wrap .left-wrap .present-label[data-v-2470a4dc]{padding:0 %?6?%;display:inline-block;color:#fff;font-size:%?24?%;border-radius:%?10?%;width:-webkit-fit-content;width:fit-content;position:absolute;margin-left:%?110?%}.order-item .goods-item .info-wrap .more-wrap .price-wrap[data-v-2470a4dc]{flex:1;text-align:right}.order-item .goods-item .info-wrap .more-wrap .price-wrap .unit[data-v-2470a4dc]{font-size:%?20?%}.order-item .goods-item .info-wrap .more-wrap .price-wrap .price[data-v-2470a4dc]{display:inline-block;line-height:1;font-size:%?28?%;flex:1}.order-item .goods-item .info-wrap .more-wrap .price-wrap .num[data-v-2470a4dc]{color:#909399;font-size:%?20?%;line-height:1}.order-item .goods-item .info-wrap .more-wrap .price-wrap .subtotal[data-v-2470a4dc]{display:inline-block;line-height:1;font-size:%?20?%;flex:1}.order-item .goods-item .action-wrap[data-v-2470a4dc]{width:100%;text-align:right;margin-top:%?10?%}.order-item .goods-item .action-wrap uni-button[data-v-2470a4dc]{margin-right:%?20?%!important}.order-item .goods-item .action-wrap uni-button[data-v-2470a4dc]:last-child{margin-right:0!important}.order-item .total-wrap[data-v-2470a4dc]{display:flex;align-items:center;margin-top:%?10?%}.order-item .total-wrap .create-time[data-v-2470a4dc]{color:#909399;font-size:%?24?%;flex:1}.order-item .total-wrap .label[data-v-2470a4dc]{color:#909399;font-size:%?24?%}.order-item .total-wrap .num[data-v-2470a4dc]{color:#909399;font-size:%?20?%;line-height:1}.order-item .total-wrap .price[data-v-2470a4dc]{display:inline-block;font-weight:700;font-size:%?32?%;text-align:right}.order-item .total-wrap .unit[data-v-2470a4dc]{font-size:%?20?%}.order-item .order-action-wrap[data-v-2470a4dc]{width:100%;text-align:right;margin-top:%?20?%}.order-item .order-action-wrap uni-button[data-v-2470a4dc]{margin-left:%?10?%!important}.order-item .order-action-wrap uni-button[data-v-2470a4dc]:first-child{margin-left:0!important}.screen-wrap .title[data-v-2470a4dc]{font-size:%?24?%;padding:%?20?%;background:#f8f8f8}.screen-wrap uni-scroll-view[data-v-2470a4dc]{height:85%}.screen-wrap uni-scroll-view .item-wrap[data-v-2470a4dc]{border-bottom:1px solid #eee}.screen-wrap uni-scroll-view .item-wrap[data-v-2470a4dc]:last-child{border-bottom:none}.screen-wrap uni-scroll-view .item-wrap .label[data-v-2470a4dc]{font-size:%?24?%;padding:%?20?% %?30?% 0 %?20?%;display:flex;justify-content:space-between;align-items:center}.screen-wrap uni-scroll-view .item-wrap .label .more[data-v-2470a4dc]{font-size:%?24?%}.screen-wrap uni-scroll-view .item-wrap .label .more uni-picker[data-v-2470a4dc]{display:inline-block;vertical-align:middle}.screen-wrap uni-scroll-view .item-wrap .label .more uni-picker uni-view[data-v-2470a4dc]{font-size:%?24?%}.screen-wrap uni-scroll-view .item-wrap .label .more .iconfont[data-v-2470a4dc]{display:inline-block;vertical-align:middle;color:#909399;font-size:%?28?%}.screen-wrap uni-scroll-view .item-wrap .label .uni-tag[data-v-2470a4dc]{padding:0 %?20?%;font-size:%?22?%;background:#f8f8f8;height:%?40?%;line-height:%?40?%;border:0;margin-left:%?20?%}.screen-wrap uni-scroll-view .item-wrap .list[data-v-2470a4dc]{margin:%?20?% %?30?%;overflow:hidden}.screen-wrap uni-scroll-view .item-wrap .list .uni-tag[data-v-2470a4dc]{padding:0 %?20?%;font-size:%?22?%;background:#f8f8f8;height:%?52?%;line-height:%?52?%;border:0;margin-right:%?20?%;margin-bottom:%?20?%}.screen-wrap uni-scroll-view .item-wrap .list .uni-tag[data-v-2470a4dc]:nth-child(3n){margin-right:0}.screen-wrap uni-scroll-view .item-wrap .value-wrap[data-v-2470a4dc]{display:flex;justify-content:center;align-items:center;padding:%?20?%}.screen-wrap uni-scroll-view .item-wrap .value-wrap .h-line[data-v-2470a4dc]{width:%?40?%;height:%?2?%;background-color:#909399}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-input[data-v-2470a4dc]{flex:1;background:#eee;height:%?60?%;line-height:%?60?%;font-size:%?22?%;border-radius:%?50?%;text-align:center}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-input[data-v-2470a4dc]:first-child{margin-right:%?10?%}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-input[data-v-2470a4dc]:last-child{margin-left:%?10?%}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-picker[data-v-2470a4dc]{display:inline-block;vertical-align:middle}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-picker uni-view[data-v-2470a4dc]{font-size:%?24?%}.screen-wrap .footer[data-v-2470a4dc]{height:%?90?%;display:flex;justify-content:center;align-items:flex-start;bottom:0;width:100%}.screen-wrap .footer uni-button[data-v-2470a4dc]{margin:0;width:40%}.screen-wrap .footer uni-button[data-v-2470a4dc]:first-child{border-top-right-radius:0;border-bottom-right-radius:0}.screen-wrap .footer uni-button[data-v-2470a4dc]:last-child{border-top-left-radius:0;border-bottom-left-radius:0}',""]),t.exports=e},"204f":function(t,e,i){var a=i("b6b6");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("3895596c",a,!0,{sourceMap:!1,shadowMode:!1})},"34f6":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(i("65d9")),o=a(i("4003")),n=a(i("3fe9")),s=a(i("923b")),d=a(i("514b")),l={components:{uniDrawer:r.default,uniTag:n.default,nsOrderRemark:o.default},mixins:[s.default,d.default]};e.default=l},"3fe9":function(t,e,i){"use strict";i.r(e);var a=i("7281"),r=i("d191d");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("1186");var n=i("828b"),s=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"c9e45eec",null,!1,a["a"],void 0);e["default"]=s.exports},"62d3f":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".uni-tag[data-v-c9e45eec]{box-sizing:border-box;padding:0 %?32?%;height:%?60?%;line-height:calc(%?60?% - 2px);font-size:%?28?%;display:inline-flex;align-items:center;color:#333;border-radius:%?6?%;background-color:#f8f8f8;border:1px solid #f8f8f8}.uni-tag--circle[data-v-c9e45eec]{border-radius:%?30?%}.uni-tag--mark[data-v-c9e45eec]{border-radius:0 %?30?% %?30?% 0}.uni-tag--disabled[data-v-c9e45eec]{opacity:.5}.uni-tag--small[data-v-c9e45eec]{height:%?40?%;padding:0 %?16?%;line-height:calc(%?40?% - 2px);font-size:%?24?%}.uni-tag--primary[data-v-c9e45eec]{color:#fff;background-color:#007aff;border:1px solid #007aff}.uni-tag--primary.uni-tag--inverted[data-v-c9e45eec]{color:#007aff;background-color:#fff;border:1px solid #007aff}.uni-tag--success[data-v-c9e45eec]{color:#fff;background-color:#4cd964;border:1px solid #4cd964}.uni-tag--success.uni-tag--inverted[data-v-c9e45eec]{color:#4cd964;background-color:#fff;border:1px solid #4cd964}.uni-tag--warning[data-v-c9e45eec]{color:#fff;background-color:#f0ad4e;border:1px solid #f0ad4e}.uni-tag--warning.uni-tag--inverted[data-v-c9e45eec]{color:#f0ad4e;background-color:#fff;border:1px solid #f0ad4e}.uni-tag--error[data-v-c9e45eec]{color:#fff;background-color:#dd524d;border:1px solid #dd524d}.uni-tag--error.uni-tag--inverted[data-v-c9e45eec]{color:#dd524d;background-color:#fff;border:1px solid #dd524d}.uni-tag--inverted[data-v-c9e45eec]{color:#333;background-color:#fff;border:1px solid #f8f8f8}",""]),t.exports=e},"65d9":function(t,e,i){"use strict";i.r(e);var a=i("ddd33"),r=i("da74");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("732b");var n=i("828b"),s=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"dcc4de3c",null,!1,a["a"],void 0);e["default"]=s.exports},7281:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.text?i("v-uni-view",{staticClass:"uni-tag",class:[!0===t.disabled||"true"===t.disabled?"uni-tag--disabled":"",!0===t.inverted||"true"===t.inverted?"uni-tag--inverted":"",!0===t.circle||"true"===t.circle?"uni-tag--circle":"",!0===t.mark||"true"===t.mark?"uni-tag--mark":"","uni-tag--"+t.size,"uni-tag--"+t.type],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick()}}},[t._v(t._s(t.text))]):t._e()},r=[]},"732b":function(t,e,i){"use strict";var a=i("204f"),r=i.n(a);r.a},"7c27":function(t,e,i){var a=i("62d3f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("92f7967a",a,!0,{sourceMap:!1,shadowMode:!1})},"7c4b":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"UniTag",props:{type:{type:String,default:"default"},size:{type:String,default:"normal"},text:{type:String,default:""},disabled:{type:[String,Boolean],default:!1},inverted:{type:[String,Boolean],default:!1},circle:{type:[String,Boolean],default:!1},mark:{type:[String,Boolean],default:!1}},methods:{onClick:function(){!0!==this.disabled&&"true"!==this.disabled&&this.$emit("click")}}};e.default=a},"923b":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966"),i("aa9c"),i("5c47"),i("af8f"),i("bf0f"),i("2797"),i("c223"),i("d4b5");var a=i("6638"),r={data:function(){return{showScreen:!1,orderCondition:{order_status_list:[]},orderConditionCurr:{order_type_list:0,order_status_list:0,promotion_type:0,pay_type_list:0,order_from_list:0,settlement_list:0},formData:{search:"",start_time:"",end_time:"",order_type:"",order_status:"",promotion_type:"",pay_type:"",order_from:"",settlement_state:""},orderList:[],order:{remark:""},btnIndex:null}},onLoad:function(t){this.getCondition()},onShow:function(){var t=this,e=uni.getStorageSync("order_id");""!==e&&(this.formData.order_status=e,this.orderConditionCurr.order_status_list=this.findCurr(e),uni.removeStorageSync("order_id")),this.$util.checkToken("/pages/order/list")&&(this.$store.dispatch("getShopInfo"),this.mescroll&&this.mescroll.resetUpScroll(),this.actionCallback=function(){t.mescroll.resetUpScroll()})},methods:{findCurr:function(t){var e=this.orderCondition.order_status_list,i=0;for(var a in e)if(e[a].type==t){i=parseInt(a)+1;break}return i},getCondition:function(){var t=this;(0,a.getOrderCondition)().then((function(e){var i=e.data;if(0==e.code&&i){for(var a in i){var r=[];if("order_status_list"!=a&&"pay_type_list"!=a)for(var o in i[a]){var n={type:o};n=Object.assign(n,i[a][o]),r.push(n)}else for(var o in i[a]){n={type:o,name:i[a][o]};r.push(n)}i[a]=r}t.orderCondition=i}}))},bindTimeStartChange:function(t){if(t.detail.value>=this.formData.end_time&&this.formData.end_time)return this.$util.showToast({title:"开始时间不能大于结束时间"}),!1;this.formData.start_time=t.detail.value},bindTimeEndChange:function(t){if(t.detail.value<=this.formData.start_time)return this.$util.showToast({title:"结束时间不能小于开始时间"}),!1;this.formData.end_time=t.detail.value},dateSelect:function(t){this.formData.start_time=this.getDay(t),this.formData.end_time=this.getNowDate()},getNowDate:function(){var t=new Date,e=t.getFullYear(),i=t.getMonth()+1;i<10&&(i="0"+i);var a=t.getDate();return a<10&&(a="0"+a),e+"-"+i+"-"+a},getDay:function(t){var e=new Date;e.setDate(e.getDate()-(t-1));var i=e.getFullYear(),a=e.getMonth()+1;a<10&&(a="0"+a);var r=e.getDate();return r<10&&(r="0"+r),i+"-"+a+"-"+r},screenData:function(){this.formData;this.showScreen=!1,this.$refs.mescroll.refresh();var t=uni.createSelectorQuery().in(this);t.select("#tab"+this.orderConditionCurr.order_status_list).boundingClientRect((function(t){})).exec()},uTag:function(t,e,i){if("order_type_list"==e){var a=this.orderCondition.order_type_list[t].status,r=[];for(var o in a)r.push({type:o,name:a[o]});this.orderCondition.order_status_list=r,this.orderConditionCurr.order_status_list=0,this.formData.order_status="",this.formData[i]=this.orderCondition[e][t].type}else this.formData[i]="settlement_list"==e?this.settlement_list[t].type:0==t?"":this.orderCondition[e][t-1].type;this.orderConditionCurr[e]=t,"order_type_list"==e&&this.$refs.mescroll.refresh()},resetData:function(){this.formData.search="",this.formData.start_time="",this.formData.end_time="",this.formData.order_type="",this.formData.order_status="",this.formData.promotion_type="",this.formData.pay_type="",this.formData.order_from="",this.formData.settlement_state="",this.orderConditionCurr.order_type_list=0,this.orderConditionCurr.order_status_list=0,this.orderConditionCurr.promotion_type=0,this.orderConditionCurr.pay_type_list=0,this.orderConditionCurr.order_from_list=0,this.orderConditionCurr.settlement_list=0,this.$forceUpdate()},tabChange:function(t){this.btnIndex=null,this.orderConditionCurr.order_status_list=t,this.formData.order_status=0==t?"":this.orderCondition.order_status_list[t-1].type,this.$refs.mescroll.refresh()},searchOrder:function(){this.mescroll.resetUpScroll()},getListData:function(t){var e=this,i={page_size:t.size,page:t.num};this.formData.order_type||(this.formData.order_type="all"),Object.assign(i,this.formData),this.mescroll=t,(0,a.getOrderList)(i).then((function(i){var a=[],r=i.message;0==i.code&&i.data?a=i.data.list:e.$util.showToast({title:r}),t.endSuccess(a.length),1==t.num&&(e.orderList=[]),a.forEach((function(t){t.order_status_action=JSON.parse(t.order_status_action),t.order_goods.forEach((function(t){t.sku_spec_format=t.sku_spec_format?JSON.parse(t.sku_spec_format):[]}))})),e.orderList=e.orderList.concat(a),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}))},imgError:function(t,e){this.orderList[t].order_goods[e].sku_image=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()},orderRemark:function(t){var e=this;this.order=JSON.parse(JSON.stringify(t)),this.$refs.orderRemark.show((function(){e.mescroll.resetUpScroll()}))},orderAction:function(t,e){try{this[t](e)}catch(i){console.log("orderAction error：",t,i)}},makePhoneCall:function(t){uni.makePhoneCall({phoneNumber:t})},toDetail:function(t){this.btnIndex=null;var e="";switch(t.order_type){case 2:e="store";break;case 3:e="local";break;case 4:e="virtual";break;default:e="basis"}this.$util.redirectTo("/pages/order/detail/"+e,{order_id:t.order_id,template:e})},goRefundOrderList:function(){this.$util.redirectTo("/pages/order/refund/list")},itemBtn:function(t){null==this.btnIndex?this.btnIndex=t:this.btnIndex=null},copyAddressMob:function(t){var e=[];4!=t.order_type&&e.push("名字："+t.name),e.push("电话："+t.mobile),e.push("地址："+t.full_address+t.address),this.$util.copy(e.toString("，"),(function(){}))}}};e.default=r},b498:function(t,e,i){"use strict";var a=i("c5a5"),r=i.n(a);r.a},b6b6:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".uni-drawer[data-v-dcc4de3c]{display:block;position:fixed;top:0;left:0;right:0;bottom:0;overflow:hidden;visibility:hidden;z-index:999;height:100%}.uni-drawer.uni-drawer--right .uni-drawer__content[data-v-dcc4de3c]{left:auto;right:0;-webkit-transform:translatex(100%);transform:translatex(100%)}.uni-drawer.uni-drawer--visible[data-v-dcc4de3c]{visibility:visible}.uni-drawer.uni-drawer--visible .uni-drawer__content[data-v-dcc4de3c]{-webkit-transform:translatex(0);transform:translatex(0)}.uni-drawer.uni-drawer--visible .uni-drawer__mask[data-v-dcc4de3c]{display:block;opacity:1}.uni-drawer__mask[data-v-dcc4de3c]{display:block;opacity:0;position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.4);transition:opacity .3s}.uni-drawer__content[data-v-dcc4de3c]{display:block;position:absolute;top:0;left:0;width:61.8%;height:100%;background:#fff;transition:all .3s ease-out;-webkit-transform:translatex(-100%);transform:translatex(-100%)}.safe-area[data-v-dcc4de3c]{padding-bottom:%?68?%;padding-top:%?44?%;box-sizing:border-box}",""]),t.exports=e},c5a5:function(t,e,i){var a=i("185e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var r=i("967d").default;r("7bfcef1a",a,!0,{sourceMap:!1,shadowMode:!1})},ca53:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"UniDrawer",props:{visible:{type:Boolean,default:!1},mode:{type:String,default:""},mask:{type:Boolean,default:!0}},data:function(){return{visibleSync:!1,showDrawer:!1,rightMode:!1,closeTimer:null,watchTimer:null,isIphoneX:!1}},watch:{visible:function(t){var e=this;clearTimeout(this.watchTimer),setTimeout((function(){e.showDrawer=t}),100),this.visibleSync&&clearTimeout(this.closeTimer),t?this.visibleSync=t:this.watchTimer=setTimeout((function(){e.visibleSync=t}),300)}},created:function(){var t=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.visibleSync=this.visible,setTimeout((function(){t.showDrawer=t.visible}),100),this.rightMode="right"===this.mode},methods:{close:function(){var t=this;this.showDrawer=!1,this.closeTimer=setTimeout((function(){t.visibleSync=!1,t.$emit("close")}),200)},moveHandle:function(){}}};e.default=a},d191d:function(t,e,i){"use strict";i.r(e);var a=i("7c4b"),r=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},da74:function(t,e,i){"use strict";i.r(e);var a=i("ca53"),r=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},db6b:function(t,e,i){"use strict";i.r(e);var a=i("34f6"),r=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},ddd33:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.visibleSync?i("v-uni-view",{staticClass:"uni-drawer",class:{"uni-drawer--visible":t.showDrawer,"uni-drawer--right":t.rightMode},on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.moveHandle.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-drawer__mask",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"uni-drawer__content",class:{"safe-area":t.isIphoneX}},[t._t("default")],2)],1):t._e()},r=[]},e6a20:function(t,e,i){"use strict";i.r(e);var a=i("f234"),r=i("db6b");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("b498");var n=i("828b"),s=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,"2470a4dc",null,!1,a["a"],void 0);e["default"]=s.exports},f234:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={uniDrawer:i("65d9").default,uniTag:i("3fe9").default,nsEmpty:i("63ed").default,nsOrderRemark:i("4003").default,loadingCover:i("59c1").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"order-list-wrap"},[i("v-uni-view",{staticClass:"nav-wrap"},[i("v-uni-text",{staticClass:"selected color-base-bg color-base-border"},[t._v("订单列表")]),i("v-uni-text",{staticClass:"color-base-border",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goRefundOrderList()}}},[t._v("退款维权")])],1),i("v-uni-view",{staticClass:"search-wrap"},[i("v-uni-view",{staticClass:"search-input-inner",staticStyle:{width:"100% !important"}},[i("v-uni-text",{staticClass:"search-input-icon iconfont iconsousuo",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.searchOrder()}}}),i("v-uni-input",{staticClass:"search-input-text font-size-tag",attrs:{maxlength:"40",placeholder:"搜索订单号/商品名称/手机号/收货人姓名"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.searchOrder()}},model:{value:t.formData.search,callback:function(e){t.$set(t.formData,"search",e)},expression:"formData.search"}})],1)],1),i("v-uni-view",{staticClass:"tab-block"},[i("v-uni-scroll-view",{staticClass:"scroll tab-wrap",attrs:{"scroll-x":"true","scroll-into-view":"tab"+t.orderConditionCurr.order_status_list,"scroll-with-animation":"true"}},[i("v-uni-view",{staticClass:"tab-item",class:{"active color-base-text color-base-bg-before":0==t.orderConditionCurr.order_status_list},attrs:{id:"tab0"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tabChange(0)}}},[i("v-uni-text",[t._v("全部")])],1),t._l(t.orderCondition.order_status_list,(function(e,a){return[i("v-uni-view",{key:a+"_0",staticClass:"tab-item",class:{"active color-base-text color-base-bg-before":a+1==t.orderConditionCurr.order_status_list},attrs:{id:"tab"+(a+1)},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tabChange(a+1)}}},[i("v-uni-text",[t._v(t._s(e.name))])],1)]}))],2),i("v-uni-view",{staticClass:"choose",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showScreen=!0}}},[i("v-uni-text",[t._v("筛选")]),i("v-uni-text",{staticClass:"iconfont iconshaixuan color-tip"})],1)],1),i("uni-drawer",{staticClass:"screen-wrap",attrs:{visible:t.showScreen,mode:"right"},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.showScreen=!1}}},[i("v-uni-view",{staticClass:"title color-tip"},[t._v("筛选")]),i("v-uni-scroll-view",{attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"label"},[i("v-uni-text",[t._v("下单时间")]),i("v-uni-view",{staticClass:"more"},[i("uni-tag",{attrs:{inverted:!0,text:"近7天"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.dateSelect(7)}}}),i("uni-tag",{attrs:{inverted:!0,text:"近30天"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.dateSelect(30)}}})],1)],1),i("v-uni-view",{staticClass:"value-wrap"},[i("v-uni-picker",{staticClass:"picker margin-right",attrs:{mode:"date",value:t.formData.start_time},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindTimeStartChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.start_time?t.formData.start_time:"开始时间"))])],1),i("v-uni-view",{staticClass:"h-line"}),i("v-uni-picker",{staticClass:"picker margin-left",attrs:{mode:"date",value:t.formData.end_time},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindTimeEndChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.formData.end_time?t.formData.end_time:"结束时间"))])],1)],1)],1),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"label"},[i("v-uni-text",[t._v("订单类型")])],1),i("v-uni-view",{staticClass:"list"},[t._l(t.orderCondition.order_type_list,(function(e,a){return[i("uni-tag",{key:a+"_0",attrs:{inverted:!0,text:e.name,type:a==t.orderConditionCurr.order_type_list?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(a,"order_type_list","order_type")}}})]}))],2)],1),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"label"},[i("v-uni-text",[t._v("订单状态")])],1),i("v-uni-view",{staticClass:"list"},[i("uni-tag",{attrs:{inverted:!0,text:"全部",type:0==t.orderConditionCurr.order_status_list?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(0,"order_status_list","order_status")}}}),t._l(t.orderCondition.order_status_list,(function(e,a){return[i("uni-tag",{key:a+"_0",attrs:{inverted:!0,text:e.name,type:a+1==t.orderConditionCurr.order_status_list?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(a+1,"order_status_list","order_status")}}})]}))],2)],1),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"label"},[i("v-uni-text",[t._v("营销类型")])],1),i("v-uni-view",{staticClass:"list"},[i("uni-tag",{attrs:{inverted:!0,text:"全部",type:0==t.orderConditionCurr.promotion_type?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(0,"promotion_type","promotion_type")}}}),t._l(t.orderCondition.promotion_type,(function(e,a){return[i("uni-tag",{key:a+"_0",attrs:{inverted:!0,text:e.name,type:a+1==t.orderConditionCurr.promotion_type?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(a+1,"promotion_type","promotion_type")}}})]}))],2)],1),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"label"},[i("v-uni-text",[t._v("付款方式")])],1),i("v-uni-view",{staticClass:"list"},[i("uni-tag",{attrs:{inverted:!0,text:"全部",type:0==t.orderConditionCurr.pay_type_list?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(0,"pay_type_list","pay_type")}}}),t._l(t.orderCondition.pay_type_list,(function(e,a){return[i("uni-tag",{key:a+"_0",attrs:{inverted:!0,text:e.name,type:t.orderConditionCurr.pay_type_list==a+1?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(a+1,"pay_type_list","pay_type")}}})]}))],2)],1),i("v-uni-view",{staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"label"},[i("v-uni-text",[t._v("订单来源")])],1),i("v-uni-view",{staticClass:"list"},[i("uni-tag",{attrs:{inverted:!0,text:"全部",type:0==t.orderConditionCurr.order_from_list?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(0,"order_from_list","order_from")}}}),t._l(t.orderCondition.order_from_list,(function(e,a){return[i("uni-tag",{key:a+"_0",attrs:{inverted:!0,text:e.name,type:t.orderConditionCurr.order_from_list==a+1?"primary":"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uTag(a+1,"order_from_list","order_from")}}})]}))],2)],1)],1),i("v-uni-view",{staticClass:"footer"},[i("v-uni-button",{attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.resetData.apply(void 0,arguments)}}},[t._v("重置")]),i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.screenData.apply(void 0,arguments)}}},[t._v("确定")])],1)],1),i("mescroll-uni",{ref:"mescroll",staticClass:"list-wrap",attrs:{top:"340",size:8},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[t.orderList.length>0?t._l(t.orderList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"order-item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toDetail(e)}}},[i("v-uni-view",{staticClass:"head"},[i("v-uni-text",{staticClass:"order-no"},[t._v(t._s(e.order_no))]),i("v-uni-text",{staticClass:"pay-type-name color-base-text"},[t._v(t._s(e.order_type_name))]),e.promotion_type_name?i("v-uni-text",{staticClass:"promotion-type-name"},[t._v("("+t._s(e.promotion_status_name)+")")]):t._e(),i("v-uni-text",{staticClass:"order-status"},[t._v(t._s(e.order_status_name))])],1),t._l(e.order_goods,(function(r,o){return i("v-uni-view",{key:o,staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-img"},[i("v-uni-image",{attrs:{src:t.$util.img(r.sku_image,{size:"mid"}),mode:"widthFix"},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.imgError(a,o)}}})],1),i("v-uni-view",{staticClass:"info-wrap"},[i("v-uni-view",{staticClass:"name-wrap"},[t._v(t._s(r.goods_name))]),r.sku_spec_format?i("v-uni-view",{staticClass:"spec-wrap"},[t._l(r.sku_spec_format,(function(e,i){return[t._v(t._s(e.spec_value_name)+"\n\t\t\t\t\t\t\t\t\t"+t._s(i<r.sku_spec_format.length-1?"; ":""))]}))],2):t._e(),i("v-uni-view",{staticClass:"more-wrap"},[i("v-uni-view",{staticClass:"left-wrap"},[i("v-uni-view",{staticClass:"goods-class"},[t._v(t._s(r.goods_class_name)),1==e.order_type&&1==e.order_status&&1==r.delivery_status?i("v-uni-text",{staticClass:"color-base-text delivery-status"},[t._v("已发货")]):t._e()],1),1==r.is_present?i("v-uni-text",{staticClass:"present-label color-base-bg"},[t._v("赠品")]):t._e(),r.sku_no?i("v-uni-view",{staticClass:"goods-class"},[t._v("UPC "+t._s(r.sku_no))]):t._e()],1),i("v-uni-view",{staticClass:"price-wrap"},[i("v-uni-view",{staticClass:"price"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(r.price))])],1),i("v-uni-view",{staticClass:"num"},[t._v("x"+t._s(r.num))]),i("v-uni-view",{staticClass:"subtotal"},[t._v("小计 ￥"+t._s(t._f("moneyFormat")(r.num*r.price)))])],1)],1)],1),0!=r.refund_status?i("v-uni-view",{staticClass:"action-wrap",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.goRefund(r.order_goods_id)}}},[i("v-uni-button",{attrs:{type:"primary",size:"mini"}},[t._v(t._s(r.refund_status_name))])],1):t._e()],1)})),i("v-uni-view",{staticClass:"total-wrap"},[i("v-uni-text",{staticClass:"create-time"},[t._v("下单时间："+t._s(t.$util.timeStampTurnTime(e.create_time)))]),i("v-uni-text",{staticClass:"label"},[t._v("合计：")]),i("v-uni-view",{staticClass:"price color-base-text"},[i("v-uni-text",{staticClass:"unit"},[t._v("￥")]),i("v-uni-text",[t._v(t._s(e.order_money))])],1)],1),"online"==e.order_scene?i("v-uni-view",{staticClass:"member-wrap"},[4!=e.order_type?i("v-uni-view",[e.nickname?i("v-uni-text",{staticClass:"nick-name"},[t._v(t._s(e.nickname))]):t._e(),i("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"mobile-wrap",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.makePhoneCall(e.mobile)}}},[i("v-uni-text",{staticClass:"iconfont icondianhua color-base-text"}),i("v-uni-text",{staticClass:"mobile color-base-text"},[t._v(t._s(e.mobile))])],1),i("v-uni-view",{staticClass:"address"},[t._v(t._s(e.full_address)+" "+t._s(e.address))])],1):t._e(),i("v-uni-view",[t._v(t._s(e.mobile))]),2==e.order_type&&e.buyer_ask_delivery_time_str?i("v-uni-view",{staticClass:"address"},[t._v("预提货时间："+t._s(e.buyer_ask_delivery_time_str))]):t._e(),3==e.order_type&&e.buyer_ask_delivery_time_str?i("v-uni-view",{staticClass:"address"},[t._v("配送时间："+t._s(e.buyer_ask_delivery_time_str))]):t._e(),4!=e.order_type?i("v-uni-view",{staticClass:"copy-sty",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.copyAddressMob(e)}}},[t._v("复制")]):t._e()],1):t._e(),e.buyer_message?i("v-uni-view",{staticClass:"remark-wrap color-base-bg-light color-base-text"},[t._v("买家留言："+t._s(e.buyer_message))]):t._e(),e.remark?i("v-uni-view",{staticClass:"remark-wrap color-base-bg-light color-base-text"},[t._v("卖家备注："+t._s(e.remark))]):t._e(),0==e.order_status?i("v-uni-view",{staticClass:"displayflex"},[i("v-uni-view",{staticClass:"displayflex-text positionr",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.itemBtn(a)}}},[t._v("更多"),t.btnIndex==a?i("v-uni-view",{staticClass:"positiona"},[i("v-uni-view",{staticClass:"boxshaow",staticStyle:{position:"relative"}},[i("v-uni-view",{staticClass:"triangle-up"}),i("v-uni-view",{staticClass:"boxshaow-btn"},[i("v-uni-view",{on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.orderRemark(e)}}},[t._v("备注")]),0==e.order_status&&"online"==e.order_scene?i("v-uni-view",{on:{click:function(i){if(!i.type.indexOf("key")&&t._k(i.keyCode,"sotp",void 0,i.key,void 0))return null;arguments[0]=i=t.$handleEvent(i),t.offlinePay(e.order_id)}}},[t._v("线下支付")]):t._e()],1)],1)],1):t._e()],1),i("v-uni-view",{staticClass:"order-action-wrap"},[t._l(e.order_status_action.action,(function(a,r){return i("v-uni-button",{key:r,attrs:{type:"primary",size:"mini"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.orderAction(a.action,e.order_id)}}},[t._v(t._s(a.title))])})),void 0],2)],1):i("v-uni-view",{staticClass:"order-action-wrap"},[i("v-uni-button",{attrs:{type:"primary",size:"mini",plain:!0},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.orderRemark(e)}}},[t._v("备注")]),t._l(e.order_status_action.action,(function(a,r){return i("v-uni-button",{key:r,attrs:{type:"primary",size:"mini"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.orderAction(a.action,e.order_id)}}},[t._v(t._s(a.title))])})),0==e.order_status?i("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(i){if(!i.type.indexOf("key")&&t._k(i.keyCode,"sotp",void 0,i.key,void 0))return null;arguments[0]=i=t.$handleEvent(i),t.offlinePay(e.order_id)}}},[t._v("线下支付")]):t._e(),2==e.order_type&&2==e.order_status?i("v-uni-button",{attrs:{type:"primary",size:"mini"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.storeOrderTakedeliveryFn(e.order_id)}}},[t._v("提货")]):t._e()],2)],2)})):i("ns-empty",{attrs:{text:"暂无订单数据"}})],2)],2),i("ns-order-remark",{ref:"orderRemark",attrs:{order:t.order}}),i("loading-cover",{ref:"loadingCover"})],1)},o=[]}}]);