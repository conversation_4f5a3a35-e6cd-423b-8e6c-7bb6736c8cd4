(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-edit-spec_edit"],{"2aba":function(t,a,e){"use strict";e.d(a,"b",(function(){return o})),e.d(a,"c",(function(){return s})),e.d(a,"a",(function(){return i}));var i={shmilyDragImage:e("beeb").default,nsSwitch:e("e1f1").default,loadingCover:e("59c1").default},o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"sku-list"},t._l(t.goodsSkuData,(function(a,i){return e("v-uni-view",{key:i,staticClass:"item-inner"},[e("v-uni-view",{staticClass:"item-wrap",class:{"item-shrink":a.shrink}},[e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("规格")]),e("v-uni-text",{staticClass:"spec-name"},[t._v(t._s(a.spec_name))])],1),e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),e("v-uni-text",[t._v("销售价")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:a.price,callback:function(e){t.$set(a,"price",e)},expression:"item.price"}}),e("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),3!=t.goodsClass?e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),e("v-uni-text",[t._v("库存")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:a.stock,callback:function(e){t.$set(a,"stock",e)},expression:"item.stock"}}),e("v-uni-text",{staticClass:"unit"},[t._v("件")])],1):t._e(),"verify"==t.virtual_deliver_type?e("v-uni-view",{staticClass:"form-wrap price"},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",{staticClass:"required color-base-text"},[t._v("*")]),e("v-uni-text",[t._v("核销次数")])],1),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:a.verify_num,callback:function(e){t.$set(a,"verify_num",e)},expression:"item.verify_num"}}),e("v-uni-text",{staticClass:"unit"},[t._v("次")])],1):t._e(),e("v-uni-view",{staticClass:"form-wrap goods-img",style:{height:t.skuImgHeight[i]+"px"}},[e("v-uni-text",{staticClass:"label"},[t._v("规格图片")]),e("v-uni-view",{staticClass:"img-list",class:"list-"+i},[e("shmily-drag-image",{ref:"skuShmilyDragImg",refInFor:!0,attrs:{list:a.sku_images_arr,imageWidth:150,imageHeight:170,number:10,index:i,uploadMethod:"album",openSelectMode:!0,isAWait:a.sku_images_arr&&a.sku_images_arr.length>0},on:{"update:list":function(e){arguments[0]=e=t.$handleEvent(e),t.$set(a,"sku_images_arr",e)},callback:function(a){arguments[0]=a=t.$handleEvent(a),t.uploadImgCallback.apply(void 0,arguments)}}}),e("v-uni-view",{staticClass:"tips"},[t._v("建议尺寸：800*800，长按图片可拖拽排序，最多上传10张")])],1)],1),e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("划线价")]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:a.market_price,callback:function(e){t.$set(a,"market_price",e)},expression:"item.market_price"}}),e("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("成本价")]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:a.cost_price,callback:function(e){t.$set(a,"cost_price",e)},expression:"item.cost_price"}}),e("v-uni-text",{staticClass:"unit"},[t._v("元")])],1),3==t.goodsClass?e("v-uni-view",{staticClass:"form-wrap more-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openCarmichaelEdit(a)}}},[e("v-uni-text",{staticClass:"label"},[t._v("卡密管理")]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"text",placeholder:a.carmichaelLength?a.carmichaelLength:"添加卡密",disabled:""}}),e("v-uni-text",{staticClass:"iconfont iconright"})],1):t._e(),e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("库存预警")]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"number",placeholder:"0"},model:{value:a.stock_alarm,callback:function(e){t.$set(a,"stock_alarm",e)},expression:"item.stock_alarm"}}),e("v-uni-text",{staticClass:"unit"},[t._v("件")])],1),1==t.goodsClass?e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("重量")]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:a.weight,callback:function(e){t.$set(a,"weight",e)},expression:"item.weight"}}),e("v-uni-text",{staticClass:"unit"},[t._v("kg")])],1):t._e(),1==t.goodsClass?e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("体积")]),e("v-uni-input",{staticClass:"uni-input",attrs:{type:"digit",placeholder:"0.00"},model:{value:a.volume,callback:function(e){t.$set(a,"volume",e)},expression:"item.volume"}}),e("v-uni-text",{staticClass:"unit"},[t._v("m³")])],1):t._e(),e("v-uni-view",{staticClass:"form-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("规格编码")]),e("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入规格编码"},model:{value:a.sku_no,callback:function(e){t.$set(a,"sku_no",e)},expression:"item.sku_no"}})],1),e("v-uni-view",{staticClass:"form-wrap more-wrap"},[e("v-uni-text",{staticClass:"label"},[t._v("默认展示")]),e("ns-switch",{staticClass:"switch",attrs:{checked:1==a.is_default},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.isDefault(i)}}})],1)],1),e("v-uni-view",{staticClass:"shrink",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.changeMore(i)}}},[e("v-uni-text",[t._v(t._s(a.shrink?"更多选项":"收起"))]),e("v-uni-text",{staticClass:"iconfont",class:a.shrink?"iconunfold":"iconfold"})],1)],1)})),1),e("v-uni-view",{staticClass:"footer-wrap"},[e("v-uni-button",{attrs:{type:"primary"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.save()}}},[t._v("保存")])],1),e("loading-cover",{ref:"loadingCover"})],1)},s=[]},"36e4":function(t,a,e){"use strict";e("6a54");var i=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var o=i(e("3471"));e("bf0f"),e("2797"),e("d4b5"),e("aa9c"),e("c223"),e("e838"),e("5c47"),e("a1c1"),e("c9b5"),e("ab80"),e("0506"),e("e966");var s=i(e("e1f1")),r=i(e("beeb")),n={data:function(){return{goodsClass:1,is_need_verify:0,virtual_deliver_type:"",goodsSpecFormat:[],goodsSkuData:[],tempGoodsSkuData:[],skuImgHeight:[165]}},components:{nsSwitch:s.default,shmilyDragImage:r.default},onLoad:function(t){this.goodsClass=t.goods_class||1,this.is_need_verify=t.is_need_verify||0,this.virtual_deliver_type=t.virtual_deliver_type||"",this.goodsSpecFormat=uni.getStorageSync("editGoodsSpecFormat")?JSON.parse(uni.getStorageSync("editGoodsSpecFormat")):[],this.tempGoodsSkuData=uni.getStorageSync("editGoodsSkuData")?JSON.parse(uni.getStorageSync("editGoodsSkuData")):[],this.handlerGoodsSkuData(),uni.removeStorageSync("selectedAlbumImg"),uni.removeStorageSync("selectedAlbumImgTemp")},onShow:function(){var t=this;this.goodsSkuData.forEach((function(a,e){if(t.$set(a,"shrink",!0),uni.getStorageSync("specName")==a.spec_name)if(uni.getStorageSync("editGoodsCarmichael")){var i=JSON.parse(uni.getStorageSync("editGoodsCarmichael"));a.carmichael=i||[],i.length>0?a.carmichaelLength="添加卡密【"+i.length+"】":a.carmichaelLength="添加卡密"}else;a.sku_images&&(a.sku_images_arr=a.sku_images.split(","))})),this.refreshData()},methods:{handlerGoodsSkuData:function(){var t=this,a=this.goodsSpecFormat;this.goodsSkuData=[];var e,i=(0,o.default)(a);try{for(i.s();!(e=i.n()).done;){var s=e.value,r=[];if(this.goodsSkuData.length>0){var n,l=(0,o.default)(this.goodsSkuData);try{for(l.s();!(n=l.n()).done;){var c,u=n.value,p=(0,o.default)(s["value"]);try{for(p.s();!(c=p.n()).done;){var d=c.value,g=JSON.parse(JSON.stringify(u.sku_spec_format));g.push(d);var m={spec_name:"".concat(u.spec_name," ").concat(d.spec_value_name),sku_no:"",sku_spec_format:g,price:"",market_price:"",cost_price:"",stock:"",stock_alarm:"",verify_num:"",sku_image:"",sku_images:"",sku_images_arr:[],is_default:0};1==this.goodsClass&&(m.weight="",m.volume=""),r.push(m)}}catch(b){p.e(b)}finally{p.f()}}}catch(b){l.e(b)}finally{l.f()}}else{var v,h=(0,o.default)(s["value"]);try{for(h.s();!(v=h.n()).done;){d=v.value;var f=d.spec_value_name;m={spec_name:f,sku_no:"",sku_spec_format:[d],price:"",market_price:"",cost_price:"",stock:"",stock_alarm:"",verify_num:"",sku_image:"",sku_images:"",sku_images_arr:[],is_default:0};1==this.goodsClass&&(m.weight="",m.volume=""),r.push(m)}}catch(b){h.e(b)}finally{h.f()}}this.goodsSkuData=r.length>0?r:this.goodsSkuData}}catch(b){i.e(b)}finally{i.f()}for(var k=0;k<this.tempGoodsSkuData.length;k++)for(var w=0;w<this.goodsSkuData.length;w++)if(this.tempGoodsSkuData[k].spec_name==this.goodsSkuData[w].spec_name){this.goodsSkuData[w]=this.tempGoodsSkuData[k];break}this.goodsSkuData.forEach((function(a,e){if(t.$set(a,"shrink",!0),uni.getStorageSync("specName")==a.spec_name)if(uni.getStorageSync("editGoodsCarmichael")){var i=JSON.parse(uni.getStorageSync("editGoodsCarmichael"));a.carmichael=i||[],i.length>0?a.carmichaelLength="添加卡密【"+i.length+"】":a.carmichaelLength="添加卡密"}else;a.sku_images&&(a.sku_images_arr=a.sku_images.split(","))})),setTimeout((function(){t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.$forceUpdate()}),300)},uploadImgCallback:function(t){""!=t.height&&(this.skuImgHeight[t.index]=parseFloat(t.height.replace("px",""))+80,this.goodsSkuData[t.index].sku_images_arr&&(this.goodsSkuData[t.index].sku_image=this.goodsSkuData[t.index].sku_images_arr[0],this.goodsSkuData[t.index].sku_images=this.goodsSkuData[t.index].sku_images_arr.toString()),this.$forceUpdate(),t.isLoad&&this.$refs.loadingCover&&this.$refs.loadingCover.hide())},refreshData:function(){var t=uni.getStorageSync("selectedAlbumImg");t&&(uni.setStorageSync("selectedAlbumImgTemp",t),t=JSON.parse(t),this.goodsSkuData[t.index].sku_images_arr=t.list.split(","),this.goodsSkuData[t.index].sku_image=this.goodsSkuData[t.index].sku_images_arr[0],this.goodsSkuData[t.index].sku_images=this.goodsSkuData[t.index].sku_images_arr.toString(),this.$refs.skuShmilyDragImg[t.index].refresh((function(){uni.removeStorageSync("selectedAlbumImg")})),this.$forceUpdate())},isDefault:function(t){this.goodsSkuData.forEach((function(a,e){a.is_default=t==e?1==a.is_default?0:1:0})),this.$forceUpdate()},changeMore:function(t){this.goodsSkuData[t].shrink=!this.goodsSkuData[t].shrink},verify:function(){for(var t=!0,a=0;a<this.goodsSkuData.length;a++){var e=this.goodsSkuData[a];if(0==e.price.length){this.$util.showToast({title:"请输入[第".concat(a+1,"个规格]的销售价")}),t=!1;break}if(isNaN(e.price)||!this.$util.data().regExp.digit.test(e.price)){this.$util.showToast({title:"[第".concat(a+1,"个规格的销售价]格式输入错误")}),t=!1;break}if(0==e.stock.length&&3!=this.goodsClass){this.$util.showToast({title:"请输入[第".concat(a+1,"个规格]的库存")}),t=!1;break}if(3!=this.goodsClass&&(isNaN(e.stock)||!this.$util.data().regExp.number.test(e.stock))){this.$util.showToast({title:"[第".concat(a+1,"个规格的库存]格式输入错误")}),t=!1;break}if(0==e.verify_num.length&&"verify"==this.virtual_deliver_type){this.$util.showToast({title:"请输入[第".concat(a+1,"个规格]的核销次数")}),t=!1;break}if(e.stock_alarm.length>0){if(isNaN(e.stock_alarm)||!this.$util.data().regExp.number.test(e.stock_alarm)){this.$util.showToast({title:"[第".concat(a+1,"个规格的库存预警]格式输入错误")}),t=!1;break}if(0!=parseInt(e.stock_alarm)&&parseInt(e.stock_alarm)===e.stock){this.$util.showToast({title:"[第".concat(a+1,"个规格的库存预警]不能等于库存数量")}),t=!1;break}if(parseInt(e.stock_alarm)>e.stock){this.$util.showToast({title:"[第".concat(a+1,"个规格的库存预警]不能超过库存数量")}),t=!1;break}}if(isNaN(e.market_price)||!this.$util.data().regExp.digit.test(e.market_price)){this.$util.showToast({title:"[第".concat(a+1,"个规格的划线价]格式输入错误")}),t=!1;break}if(isNaN(e.cost_price)||!this.$util.data().regExp.digit.test(e.cost_price)){this.$util.showToast({title:"[第".concat(a+1,"个规格的成本价]格式输入错误")}),t=!1;break}if(1==this.goodsClass){if(isNaN(e.weight)||!this.$util.data().regExp.float3.test(e.weight)){this.$util.showToast({title:"[第".concat(a+1,"个规格的重量kg]格式输入错误")}),t=!1;break}if(isNaN(e.volume)||!this.$util.data().regExp.float3.test(e.volume)){this.$util.showToast({title:"[第".concat(a+1,"个规格的体积(m³]格式输入错误")}),t=!1;break}}}return t},openCarmichaelEdit:function(t){t.carmichael?uni.setStorageSync("editGoodsCarmichael",JSON.stringify(t.carmichael)):uni.setStorageSync("editGoodsCarmichael",""),this.$util.redirectTo("/pages/goods/edit/carmichael_edit",{type:1,specName:t.spec_name})},save:function(){this.verify()&&(uni.setStorageSync("editGoodsSkuData",JSON.stringify(this.goodsSkuData)),uni.removeStorageSync("selectedAlbumImg"),uni.removeStorageSync("selectedAlbumImgTemp"),uni.navigateBack({delta:1}))}}};a.default=n},3759:function(t,a,e){"use strict";e.r(a);var i=e("2aba"),o=e("c5bf");for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(s);e("920a");var r=e("828b"),n=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"2297753a",null,!1,i["a"],void 0);a["default"]=n.exports},4385:function(t,a,e){var i=e("c86c");a=i(!1),a.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-2297753a]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-2297753a]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-2297753a]{position:fixed;left:0;right:0;z-index:998}.container[data-v-2297753a]{padding-bottom:%?40?%}.safe-area[data-v-2297753a]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}.goods-edit-wrap[data-v-2297753a]{margin-bottom:%?160?%}.form-title[data-v-2297753a]{display:flex;justify-content:space-between;margin:%?20?% %?30?%;color:#909399}.item-wrap uni-radio .uni-radio-input[data-v-2297753a]{width:%?30?%!important;height:%?30?%!important}.item-wrap[data-v-2297753a]{background:#fff;margin-top:%?20?%}.item-wrap .goods-type[data-v-2297753a]{display:flex;margin:0 %?40?% %?20?% %?40?%;flex-wrap:wrap}.item-wrap .goods-type uni-view[data-v-2297753a]{flex:1;text-align:center;border:1px solid #ccc;color:#909399;margin-right:%?40?%;margin-top:%?30?%;position:relative;height:%?80?%;line-height:%?80?%;white-space:nowrap;min-width:calc((100% - %?100?%) / 3);max-width:calc((100% - %?100?%) / 3)}.item-wrap .goods-type uni-view[data-v-2297753a]:nth-child(3n+3){margin-right:0}.item-wrap .goods-type uni-view .iconfont[data-v-2297753a]{display:none}.item-wrap .goods-type uni-view.selected .iconfont[data-v-2297753a]{display:block;position:absolute;bottom:%?-22?%;right:%?-22?%;font-size:%?80?%}.item-wrap .form-wrap[data-v-2297753a]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-2297753a]:last-child{border-bottom:none}.item-wrap .form-wrap .required[data-v-2297753a]{font-weight:700}.item-wrap .form-wrap .label[data-v-2297753a]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap uni-input[data-v-2297753a]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-2297753a]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-2297753a]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-2297753a]{color:#909399;margin-left:%?20?%}.item-wrap .form-wrap.more-wrap .action[data-v-2297753a]{background-color:#ccc;border-radius:50%;color:#fff;width:%?36?%;height:%?36?%;line-height:%?36?%;display:inline-block;text-align:center;font-weight:700;margin-right:%?20?%}.item-wrap .form-wrap.goods-img[data-v-2297753a]{height:%?200?%;line-height:%?200?%;display:block;position:relative}.item-wrap .form-wrap.goods-img .label[data-v-2297753a]{display:inline-block}.item-wrap .form-wrap.goods-img .img-list[data-v-2297753a]{position:absolute;width:80%;top:0;left:%?100?%;margin-top:%?40?%;margin-left:%?40?%}.item-wrap .form-wrap.goods-img .tips[data-v-2297753a]{color:#909399;font-size:%?20?%;margin-top:%?20?%}.item-wrap .form-wrap .unit[data-v-2297753a]{margin-left:%?20?%;width:%?40?%}.item-wrap .form-wrap.join-member-discount .label[data-v-2297753a]{flex:1}.item-wrap .form-wrap.validity-type[data-v-2297753a]{border-bottom:1px solid #eee!important}.footer-wrap[data-v-2297753a]{position:fixed;width:100%;bottom:0;padding:%?40?% 0;z-index:10;background-color:#fff}.popup[data-v-2297753a]{width:100vw;background:#fff;border-top-left-radius:%?24?%;border-top-right-radius:%?24?%}.popup .popup-header[data-v-2297753a]{display:flex;border-bottom:%?2?% solid #eee;position:relative;padding:%?40?%}.popup .popup-header .tit[data-v-2297753a]{flex:1;font-size:%?32?%;line-height:1;text-align:center}.popup .popup-header .iconfont[data-v-2297753a]{line-height:1;position:absolute;right:%?30?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);color:#909399;font-size:%?32?%}.popup .popup-body[data-v-2297753a]{height:calc(100% - %?250?%)}.popup .popup-body.safe-area[data-v-2297753a]{height:calc(100% - %?270?%)}.popup.category[data-v-2297753a]{height:50vh}.popup.category .popup-header[data-v-2297753a]{border-bottom:none}.popup.category .popup-body[data-v-2297753a]{padding:0 %?30?%}.popup.category .popup-body .nav[data-v-2297753a]{border-bottom:%?2?% solid #eee;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.popup.category .popup-body .nav uni-text[data-v-2297753a]{padding:0 0 %?20?% 0;margin-right:%?20?%;display:inline-block}.popup.category .popup-body .nav uni-text[data-v-2297753a]:last-child{padding-right:0}.popup.category .popup-body .nav uni-text.selected[data-v-2297753a]{border-bottom:2px solid}.popup.category .popup-body .category[data-v-2297753a]{height:100%}.popup.category .popup-body .category .item[data-v-2297753a]{display:block;margin:%?20?% 0 0}.popup.category .popup-body .category .item uni-text[data-v-2297753a]:first-child{overflow:hidden;text-overflow:ellipsis;white-space:pre;width:90%;display:inline-block;vertical-align:middle}.popup.category .popup-body .category .item .iconfont[data-v-2297753a]{float:right}.popup.category .popup-body .category .child-item[data-v-2297753a]{display:flex;justify-content:space-between;margin-left:%?40?%}.popup.choose-picture[data-v-2297753a]{background-color:#f8f8f8}.popup.choose-picture .popup-header[data-v-2297753a]{border-bottom:none;background-color:#fff}.popup.choose-picture .popup-body[data-v-2297753a]{background-color:#f8f8f8;height:auto}.popup.choose-picture .popup-body .select-wrap[data-v-2297753a]{background-color:#fff;padding:0 %?30?%}.popup.choose-picture .popup-body .item[data-v-2297753a]{text-align:center;padding:%?20?%;background-color:#fff;border-bottom:1px solid #eee}.popup.choose-picture .popup-body .item[data-v-2297753a]:last-child{border-bottom:none}.popup.choose-picture .popup-body .item.cancle[data-v-2297753a]{margin-top:%?20?%}.sku-list[data-v-2297753a]{margin-bottom:%?160?%}.sku-list .item-inner[data-v-2297753a]{position:relative;margin:%?20?% %?30?% 0;overflow:hidden}.sku-list .item-inner .item-wrap[data-v-2297753a]{margin-bottom:%?80?%;background-color:#fff}.sku-list .item-inner .item-shrink[data-v-2297753a]{overflow:hidden;height:%?620?%}.sku-list .item-inner .form-wrap .spec-name[data-v-2297753a]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.sku-list .item-inner .form-wrap.more-wrap .switch[data-v-2297753a]{position:absolute;right:0;margin-right:%?30?%;padding-right:%?20?%}.sku-list .item-inner .shrink[data-v-2297753a]{position:absolute;left:0;right:0;bottom:%?12?%;display:flex;align-items:center;justify-content:center;padding-bottom:%?20?%;height:%?52?%;background-color:#fff;color:#909399}.sku-list .item-inner .shrink .iconfont[data-v-2297753a]{font-size:%?36?%;margin-left:%?6?%}',""]),t.exports=a},"920a":function(t,a,e){"use strict";var i=e("f88d"),o=e.n(i);o.a},c5bf:function(t,a,e){"use strict";e.r(a);var i=e("36e4"),o=e.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(s);a["default"]=o.a},f88d:function(t,a,e){var i=e("4385");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("4947fb7e",i,!0,{sourceMap:!1,shadowMode:!1})}}]);