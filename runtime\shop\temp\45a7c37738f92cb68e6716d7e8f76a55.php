<?php /*a:2:{s:33:"app/shop/view/layout/default.html";i:1741057066;s:30:"app/shop/view/layout/base.html";i:1741057066;}*/ ?>
<!DOCTYPE html>
<html>
<head>
	<meta name="renderer" content="webkit" />
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1" />
	<title><?php echo htmlentities((isset($shop_info['site_name']) && ($shop_info['site_name'] !== '')?$shop_info['site_name']:"")); ?></title>
	<meta name="keywords" content="<?php echo isset($shop_info['seo_keywords']) ? htmlentities($shop_info['seo_keywords']) : ''; ?>">
	<meta name="description" content="<?php echo isset($shop_info['seo_description']) ? htmlentities($shop_info['seo_description']) : ''; ?>">
	<link rel="icon" type="image/x-icon" href="http://**********/public/static/img/shop_bitbug_favicon.ico" />
	<?php if(!(empty($load_diy_icon_url) || (($load_diy_icon_url instanceof \think\Collection || $load_diy_icon_url instanceof \think\Paginator ) && $load_diy_icon_url->isEmpty()))): ?>
		<!-- 加载自定义图标库 -->
		<?php echo implode('',$load_diy_icon_url); ?>
	<?php endif; ?>
	<link rel="stylesheet" type="text/css" href="http://**********/public/static/css/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="http://**********/public/static/ext/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="http://**********/app/shop/view/public/css/template/<?php echo htmlentities($theme_config['url']); ?>" />
	<link rel="stylesheet" type="text/css" href="http://**********/app/shop/view/public/css/common.css?time=20240822" />
	<script src="http://**********/public/static/js/jquery-3.1.1.js"></script>
	<script src="http://**********/public/static/js/jquery.cookie.js"></script>
	<script src="http://**********/public/static/ext/layui/layui.js"></script>
	<script>
		layui.use(['layer', 'upload', 'element'], function() {});
		//全局定义一次, 加载formSelects
		layui.extend({
			formSelects: 'http://**********/public/static/ext/layui/extend/formSelects-v4',
			layCascader: 'http://**********/public/static/ext/layui/extend/cascader/cascader',
			dropdown: 'http://**********/public/static/ext/layui/extend/dropdown/dropdown'
		});
		window.ns_url = {
			baseUrl: "http://**********/",
			route: ['<?php echo request()->module(); ?>', '<?php echo request()->controller(); ?>', '<?php echo request()->action(); ?>'],
			appModule: '<?php echo isset($app_module) ? htmlentities($app_module) : ""; ?>',
			siteId: '<?php echo request()->siteid(); ?>',
			shopImg: 'http://**********/app/shop/view/public/img',
			staticImg: 'http://**********/public/static/img',
			staticExt: 'http://**********/public/static/ext',
			uploadMaxFileSize: '<?php echo isset($upload_max_filesize) ? htmlentities($upload_max_filesize) : 0; ?>',
			siteName : "<?php echo htmlentities($shop_info['site_name']); ?>",
		};
		window.regexp_config = <?php echo json_encode(config('regexp')); ?>;
	</script>
	<script src="http://**********/public/static/js/common.js?v=20241114"></script>
	<script src="http://**********/app/shop/view/public/js/common.js?time=20241114"></script>
</head>
<body>
<div class="layui-layout layui-layout-admin">
	<div class="layui-header">
		<div class="layui-logo">
			<a href="<?php echo url('shop/index/index'); ?>">
				<img src="http://**********/public/static/img/logo/shop_logo.jpg" />
			</a>
		</div>

		<div class="layui-header-right">
			<div class="layui-header-crumbs">
				<div class="layui-header-crumbs-first">
					<span><!-- 动态加载菜单名称 --></span>
				</div>
				<div class="layui-header-crumbs-second">
                    <span class="layui-breadcrumb" lay-separator="-"><!-- 动态加载面包屑 --></span>
				</div>
			</div>

			<!-- 账号 -->
			<div class="login-box layui-layout-right">
				<div class="shop-ewm">
					<button class="layui-btn" onclick="getShopUrl()">访问店铺</button>
				</div>

				<div class="help-btn" onclick="goHelpDocument()">
					<i class="iconfont iconwenhao1"></i>
					<span>帮助</span>
				</div>

				<ul class="layui-nav head-account">
					<li class="layui-nav-item layuimini-setting">
						<a href="javascript:;"><?php echo htmlentities($user_info['username']); ?></a>
						<dl class="layui-nav-child">
							<dd class="reset-pass" onclick="resetPassword();">
								<a href="javascript:;">修改密码</a>
							</dd>
							<dd>
								<a href="<?php echo href_url('shop/system/cache'); ?>">清除缓存</a>
							</dd>
							<dd>
								<a href="<?php echo addon_url('shop/login/logout'); ?>" class="login-out">退出登录</a>
							</dd>
						</dl>
					</li>
				</ul>
			</div>

		</div>
	</div>

	<!-- 一级菜单 -->
	<div class="layui-side first-nav">
		<div class="layui-side-scroll">
			<!-- 动态加载一级菜单 -->
			<ul class="layui-nav layui-nav-tree menu-first-wrap"></ul>
		</div>
	</div>

	<!-- 二、三级菜单 -->
	<div class="layui-side second-nav">
		<div class="layui-side-scroll">
			<!-- 动态加载二、三级菜单 -->
			<ul class="layui-nav layui-nav-tree"></ul>
		</div>
	</div>

	<div class="body-wrap layui-body">

		<!-- 动态加载四级菜单导航 -->

		<!-- 内容 -->
		<div class="body-content">
			<!-- 加载动画 -->
			<div class="common-loading-wrap">
				<i class="common-loading-layer layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
			</div>
		</div>

		<!-- 版权信息 -->
		<div class="footer">
			<a class="footer-img" href="javascript:;"><img src="<?php if(!empty($copyright['logo'])): ?> <?php echo img($copyright['logo']); else: ?>http://**********/public/static/img/copyright_logo.png<?php endif; ?>" /></a>
		</div>
	</div>
</div>

<script src="http://**********/app/shop/view/public/js/route.js?time=20241105"></script>
<script type="text/html" id="reset_pass">
	<!-- 重置密码弹框html -->
	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>原密码</label>
			<div class="layui-input-block">
				<input type="password" id="old_pass" name="old_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="new_pass" name="new_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>确认新密码</label>
			<div class="layui-input-block">
				<input type="password" id="repeat_pass" name="repeat_pass" required class="layui-input len-mid" autocomplete="off" readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);">
			</div>
		</div>

		<div class="form-row mid">
			<button class="layui-btn" onclick="repass()">确定</button>
			<button class="layui-btn layui-btn-primary" onclick="layer.closeAll()">返回</button>
		</div>
	</div>
</script>
<script type="text/html" id="patch_alert">
	<table class="layui-table">
		<colgroup>
			<col width="30%">
			<col width="70%">
		</colgroup>
		<thead>
		<tr>
			<th>补丁名称</th>
			<th>补丁说明</th>
		</tr>
		</thead>
		<tbody>
			{{# d.forEach((item)=>{ }}
			<tr>
				<td>{{item.patch_name}}</td>
				<td>{{item.patch_desc}}</td>
			</tr>
			{{# }) }}
		</tbody>
	</table>
</script>
</body>
</html>