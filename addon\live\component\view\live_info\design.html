<nc-component :data="data[index]" class="component-live-info">
	
	<!-- 预览 -->
	<template slot="preview">

		<div class="live-wrap">
			<div class="banner-wrap">
				<img src="{$resource_path}/img/live_default_banner.png">
				<div class="shade"></div>
				<div class="wrap">
					<div class="room-name">
						<span class="status-name"><img src="{$resource_path}/img/live_default_icon.png"/>直播中</span>
						双十一活动，限时秒杀！
					</div>
				</div>
			</div>
			<div class="room-info" v-if="nc.isShowAnchorInfo || nc.isShowLiveGood">
				<template v-if="nc.isShowAnchorInfo">
					<img src="{:img('public/static/img/default_img/head.png')}" class="anchor-img">
					<span class="anchor-name">主播：主播昵称</span>
				</template>
				<template v-if="nc.isShowAnchorInfo && nc.isShowLiveGood">
					<span class="separate">|</span>
				</template>
				<template v-if="nc.isShowLiveGood">
					<span class="goods-text">直播商品：名称</span>
				</template>
			</div>
		</div>

	</template>
	
	<!-- 内容编辑 -->
	<template slot="edit-content">
		<template v-if="nc.lazyLoad">
			<live-set></live-set>
		</template>
	</template>

	<!-- 样式编辑 -->
	<template slot="edit-style"></template>
	
	<!-- 资源 -->
	<template slot="resource">
		<css src="{$resource_path}/css/design.css"></css>
		<js src="{$resource_path}/js/design.js"></js>
	</template>

</nc-component>