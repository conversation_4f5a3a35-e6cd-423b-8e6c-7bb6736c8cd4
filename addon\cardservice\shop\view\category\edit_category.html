<style>
	.js-pid a{
		margin-left: 20px;
	}
	.form-wrap {
		margin-top: 0;
	}
	.link-url-show{margin-right: 10px}
	.click-link{height: 34px;line-height: 34px;display: inline-block;white-space: nowrap;text-align: center;border-radius: 2px;cursor: pointer;padding: 0 16px;border: 1px solid #C9C9C9;background-color: #fff;color: #555;}
	.goods-category-list .layui-table td{border-left: 0;border-right: 0;}
	.goods-category-list .layui-table .switch{font-size: 16px;cursor: pointer;width: 12px;line-height: 1;display: inline-block;text-align: center;vertical-align: middle;}
	.goods-category-list .layui-table img{width: 40px;}
	/* 分类样式*/
	.table_div{color:#666}
	.table_head{display: flex;font-weight: bold;background-color: #F7F7F7;}
	.table_head li{height: 50px;line-height: 50px;border: 0;padding: 0 15px;font-size: 14px;font-weight: 400;color: #333;}
	.table_head .operate{text-align: right;}
	.table_head li:first-child{padding-right: 0;}
	.table_tr{display: flex;border-bottom: 1px solid #e6e6e6;background: #fff;align-items: center;}
	.table_tr .table_td{position: relative;padding: 5px 15px;font-size: 14px;display: flex;align-items: center;}
	.table_tr .table_td span{cursor: pointer;}
	.table_tr .table_td span>img{width:12px;height:12px}
	.table_tr .table_td span>img.rotate{transform:rotate(90deg);}
	.table_tr .table_td .img-box{width:30px;height:30px;line-height: 30px;}
	.table_tr .table_td:first-child{padding-right:0}
	.table_tr .table-btn{display: flex;flex-wrap: wrap;justify-content: flex-end;}
	.table_tr .layui-btn{display: flex;justify-content: center;align-items: center;height: 23px;border-radius: 50px;background-color: transparent;color: var(--base-color);text-align: center;padding: 2px 0;margin: 5px 0 5px 10px;position: relative;}
	.table_two_div{display: none;}
	.table_three{display: none;}
	.empty_switch{display: inline-block;width:30px;height:25px;padding-right:15px;}
	.js-switch{display: inline-block;width:30px;text-align: center;}
	.table_move{float:left;margin-right: 10px;}
	.table_moves{float:left;margin-right: 10px;}
	.tables_move{float:left;margin-right: 20px;padding-left: 70px;}
	.select-category .layui-layer-content{overflow: auto!important;}
	.table_three .table_td.checkbox{
		padding-left: 70px;
	}
</style>

<form class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分类名称：</label>
		<div class="layui-input-block">
			<input name="category_name" type="text" value="{$goods_category_info['category_name']}" placeholder="请输入分类名称" maxlength="30" lay-verify="required" class="layui-input len-long" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>分类名称最长不超过30个字符</p>
		</div>
	</div>
	
	<div class="layui-form-item layui-hide">
		<label class="layui-form-label">上级分类：</label>
		<div class="layui-input-block len-mid js-pid">
			{if $goods_category_info['pid'] == 0}
			<span class="input-text">顶级分类</span>
			{else/}
			<span class="input-text">{$goods_category_parent_info['category_name']}</span>
			{/if}
			<input type="hidden" name="pid" value="{$goods_category_info['pid']}">
			<input type="hidden" name="level" value="{$goods_category_info['level']}">
			<input type="hidden" name="category_id_1" value="{$goods_category_info['category_id_1']}">
			<input type="hidden" name="category_id_2" value="{$goods_category_info['category_id_2']}">
			<input type="hidden" name="category_id_3" value="{$goods_category_info['category_id_3']}">
			<input type="hidden" name="category_name_1" value="">
			<a class="text-color" href="javascript:selectedCategoryPopup();">选择分类</a>
		</div>
		<div class="word-aux">
			{if $goods_category_info['level'] == 1}
			<p>注意：顶级分类不能修改</p>
			{elseif $goods_category_info['level'] == 2}
			<p>注意：二级分类可以修改一级分类</p>
			{elseif $goods_category_info['level'] == 3}
			<p>注意：三级分类可以修改二级分类</p>
			{/if}
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">分类图片：</label>
		<div class="layui-input-block">
			<div class="upload-img-block">
				<div class="upload-img-box {notempty name="$goods_category_info['image']"}hover{/notempty}">
					<div class="upload-default" id="imgUpload">
						{notempty name="$goods_category_info['image']"}
						<div id="preview_imgUpload" class="preview_img">
							<img layer-src src="{:img($goods_category_info['image'])}" class="img_prev"/>
						</div>
						{else/}
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
						{/notempty}
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="image" value="{$goods_category_info['image']}">
				</div>
			</div>
		</div>
		<div class="word-aux">
			<p>建议图片尺寸：不能大于100k。图片格式：jpg、png、jpeg。</p>
		</div>
		<a id="imageUploadAction"></a>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label">分类广告图：</label>
        <div class="layui-input-block">
            <div class="upload-img-block ">
                <div class="upload-img-box {notempty name="$goods_category_info['image_adv']"} hover {/notempty}" >
					<div class="upload-default" id="imgUploadAdv">
						{notempty name="$goods_category_info['image_adv']"}
						<div id="preview_imgUploadAdv" class="preview_img">
							<img layer-src src="{:img($goods_category_info['image_adv'])}" class="img_prev"/>
						 </div>
						{else/}
						    <div class="upload">
						        <i class="iconfont iconshangchuan"></i>
						        <p>点击上传</p>
						    </div>
						{/notempty}
					</div>
                    <div class="operation">
                    	<div>
                    		<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
                    		<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
                    	</div>
                    	
                    	<div class="replace_img js-replace">点击替换</div>
                    </div>
					<input type="hidden" name="image_adv" value="{$goods_category_info['image_adv']}">
                </div>
            </div>
        </div>
	    <div class="word-aux">
		    <p>该图片只对一级使用, 建议图片尺寸：550px * 250px。图片格式：jpg、png、jpeg。</p>
	    </div>
		<a id="imageAdvUploadAction"></a>
    </div>

	<div class="layui-form-item link-url-show-wrap">
		<label class="layui-form-label">广告链接：</label>
		<div class="layui-input-block">
			<input name="link_url" type="hidden" class="layui-input len-long" autocomplete="off" value="{$goods_category_info.link_url}">
			<a class="click-link" onclick="selectedLink()">选择链接</a>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否显示</label>
		<div class="layui-input-block">
			<input type="radio" name="is_show" value="0" title="显示" {if $goods_category_info['is_show'] == 0} checked="" {/if}>
			<input type="radio" name="is_show" value="-1" title="不显示"  {if $goods_category_info['is_show'] == -1} checked="" {/if}>
		</div>
	</div>

	<input type="hidden" id="category_id" name="category_id" value="{$goods_category_info['category_id']}">
	<input type="hidden" name="category_full_name" value="{$goods_category_info['category_full_name']}">
	
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="backCardServiceCategoryList()">返回</button>
	</div>
</form>

<script type="text/html" id="selectedCategory">
	<div class="goods-category-list layui-form">
		<div class="table_div" >
			<ul class="table_head">
				<li style="width:60px"></li>
				<li style="flex:6">分类名称</li>
				<li style="flex:2">是否显示</li>
			</ul>
			<div class="table_body">
				{if condition="$list"}
				{foreach name="$list" item="vo" key="index"}
				<li class="table_one" data-index="{$index}" data-sort="{$vo['sort']}" data-cateid="{$vo['category_id']}">
					<div class="table_tr">
						<div class="table_td" style="width:60px">
							<div class="table_move">
								<input type="checkbox" name="category_id" title="" lay-skin="primary" data-name="{$vo['category_name']}" value="{$vo['category_id']}" data-category-id="{$vo['category_id']}" data-level="{$vo['level']}" data-open="0" lay-filter="category">
							</div>
							{notempty name="$vo['child_list']"}
							<span class="switch text-color js-switch" data-category-id="{$vo['category_id']}" data-level="{$vo['level']}" data-open="0">
							+
						</span>

							{/notempty}
						</div>
						<div class="table_td" style="flex:6">{$vo['category_name']}</div>

						<div class="table_td" style="flex:2">
							{if $vo['is_show'] == 0} 显示 {else /} 不显示 {/if}
						</div>

					</div>
					{notempty name="$vo['child_list']"}
					<div class="table_two_div">
						{foreach name="$vo['child_list']" item="second"}

						<div class="table_two" data-index="{$index}" data-sort="{$second['sort']}" data-cateid="{$second['category_id']}">

							<div class="table_tr">
								<div class="table_td" style="width: 36px">
								</div>
								<div class="table_td" style="flex:6.4">
									<div class="table_move">
										<input type="checkbox" name="category_id" title="" lay-skin="primary" value="{$second['category_id']}" data-category-id="{$second['category_id']}" data-level="{$second['level']}" data-open="0" data-name="{$second['category_name']}" lay-filter="category">
									</div>
<!--									<span class="switch text-color empty_switch" >  </span>-->
									<span>{$second['category_name']}</span>
								</div>

								<div class="table_td" style="flex:2">
									{if $second['is_show'] == 0} 显示 {else /} 不显示 {/if}
								</div>

							</div>
							{notempty name="$second['child_list']"}

							<div class="table_three layui-hide">
								{foreach name="$second['child_list']" item="third"}
								<div class="table_tr table_three_tr" data-sort="{$third['sort']}" data-cateid="{$third['category_id']}">
									<div class="table_td" style="width: 36px">
									</div>
									<div class="table_td checkbox" style="flex:5.5">
										<input type="checkbox" name="category_id" title="" lay-skin="primary" data-name="{$third['category_name']}" value="{$third['category_id']}" data-category-id="{$third['category_id']}" data-level="{$third['level']}" data-open="0">
										<div>{$third['category_name']}</div>
									</div>

									<div class="table_td" style="flex:2">
										{if $third['is_show'] == 0} 显示 {else /} 不显示 {/if}
									</div>

								</div>
								{/foreach}

							</div>
							{/notempty}
						</div>
						{/foreach}
					</div>
					{/notempty}
				</li>

				{/foreach}
				{else/}
				<div class="table_tr">
					<div class="table_td" style="flex:1;text-align: center;">暂无数据</div>
				</div>
				{/if}
			</div>
		</div>

	</div>
</script>

<script src="ADDON_CARDSERVICE_JS/edit_category.js"></script>
