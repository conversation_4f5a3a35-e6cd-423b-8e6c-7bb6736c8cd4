<div class="layui-form form-wrap">
	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">基础设置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>直播类型：</label>
				<div class="layui-input-block">
					<input type="radio" name="type" value="0" title="手机直播" checked>
				</div>
				<div class="word-aux">
					<p>通过“小程序直播主播端小程序”发起直播</p>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>直播标题：</label>
				<div class="layui-input-block">
					<input type="text" name="name" lay-verify="required|name" class="layui-input len-mid" autocomplete="off">
				</div>
				<div class="word-aux">
					<p>直播标题必须为3-17个字符</p>
				</div>
				
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>开播时间：</label>
				<div class="layui-input-block len-mid">
					<input type="text" class="layui-input" name="startTime" lay-verify="required" id="startTime" autocomplete="off" readonly>
					<i class=" iconrili iconfont calendar"></i>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>结束时间：</label>
				<div class="layui-input-block len-mid end_time">
					<input type="text" class="layui-input" name="endTime" lay-verify="required|time" id="endTime" autocomplete="off" readonly>
					<i class=" iconrili iconfont calendar"></i>
				</div>
				<div class="word-aux">
					<p>开播时间至少需在10分钟后</p>
					<p>开播时间段仅供参考，不是实际直播间可以开播的时间。</p>
					<p>直播间在开始时间前也可以开播，并且到结束时间后不会强制结束。</p>
					<p>若到结束时间仍未开播，则直播间无法再开播。</p>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>主播昵称：</label>
				<div class="layui-input-block len-mid">
					<input type="text" class="layui-input" name="anchorName" lay-verify="required|anchorName"  autocomplete="off">
				</div>
				<div class="word-aux">
					<p>主播昵称必须为2-15个字符</p>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>主播微信账号：</label>
				<div class="layui-input-block len-mid">
					<input type="text" class="layui-input" name="anchorWechat" lay-verify="required"  autocomplete="off">
				</div>
				<div class="word-aux">
					<p>每个直播间需要绑定一个用作核实主播身份</p>
					<p>主播微信号，需通过实名认证</p>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">分享卡片样式配置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>分享卡片封面：</label>
				<div class="layui-input-block">
					<div class="upload-img-block">
						<div class="upload-img-box">
							<div class="upload-default" id="shareImgUpload">
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
							</div>
							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="shareImg" value="" lay-verify="shareImg"/>
						</div>
					</div>
				</div>
				<div class="word-aux">
					<p>观众在微信对话框内分享的直播间将以分享卡片的形式呈现。建议尺寸：800像素 * 640像素，图片大小不得超过1M。</p>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">直播间样式配置</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>直播间背景墙：</label>
				<div class="layui-input-block">
					<div class="upload-img-block">
						<div class="upload-img-box">
							<div class="upload-default" id="coverImgUpload">
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
							</div>
							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="coverImg" value="" lay-verify="coverImg"/>
						</div>
					</div>
					
				</div>
				<div class="word-aux">
					<p>直播间背景墙是每个直播间的默认背景。建议尺寸：1080像素 * 1920像素，图片大小不得超过2M。</p>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>购物直播频道封面：</label>
				<div class="layui-input-block">
					<div class="upload-img-block">
						<div class="upload-img-box">
							<div class="upload-default" id="feedsImgUpload">
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
							</div>
							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="feedsImg" value="" lay-verify="feedsImg"/>
						</div>
					</div>
				</div>
				<div class="word-aux">
					<p>购物直播频道封面图。建议尺寸：建议像素800*800，大小不超过100KB。</p>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">直播间功能：</label>
				<div class="layui-input-block">
					<input type="checkbox" name="closeLike" value="0" title="开启点赞" lay-skin="primary" checked><br>
					<input type="checkbox" name="closeGoods" value="0" title="开启货架" lay-skin="primary" checked><br>
					<input type="checkbox" name="closeComment" value="0" title="开启评论" lay-skin="primary" checked><br>
					<input type="checkbox" name="closeReplay" value="0" title="开启回放" lay-skin="primary" checked><br>
					<input type="checkbox" name="closeKf" value="0" title="开启客服" lay-skin="primary" checked>
				</div>
			</div>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">添加</button>
		<button class="layui-btn layui-btn-primary" onclick="backLiveRoom()">返回</button>
	</div>
</div>

<script type="text/javascript">
	var form, laydate, repeat_flag = false, //防重复标识
		minDate = "";

	layui.use(['form', 'laydate'], function() {
		form = layui.form,
		laydate = layui.laydate;

		form.render();

		// 开始时间
		laydate.render({
		  	elem: '#startTime' ,//指定元素
		  	type: 'datetime',
			value: new Date(Date.parse(new Date()) + 3600000),
			done: function(value){
				minDate = value;
				reRender();
			}
		});
		
		//结束时间
		laydate.render({
		  	elem: '#endTime' ,//指定元素
		  	type: 'datetime',
			value: ''
		});

		/**
		 * 重新渲染结束时间
		 * */
		function reRender(){
			$("#endTime").remove();
			$(".end_time").html('<input type="text" id="endTime" name="endTime" placeholder="请输入结束时间" lay-verify="required|time" class="layui-input len-mid" autocomplete="off">');
			laydate.render({
				elem: '#endTime',
				type: 'datetime',
				min: minDate
			});
		}

		/**
		 * 表单验证
		 */
		form.verify({
			name: function(value){
				if (value.length < 3 || value.length > 17) {
					return '直播标题必须为3-17个字符';
				}
			},
			anchorName: function(value){
				if (value.length < 2 || value.length > 15) {
					return '主播昵称必须为2-15个字符';
				}
			},
			time: function(value) {
				var now_time = (new Date()).getTime();
				var start_time = (new Date($("#start_time").val())).getTime();
				var end_time = (new Date(value)).getTime();
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
				if (start_time > end_time) {
					return '结束时间不能小于开始时间!';
				}
			},
			shareImg: function(value){
				var patt  = /[\S]+/;
				if (!patt.test(value)) {
					return '请上传分享卡片封面!';
				}
			},
			coverImg: function(value){
				var patt  = /[\S]+/;
				if (!patt.test(value)) {
					return '请上传直播间背景墙!';
				}
			},
            feedsImg: function(value){
                var patt  = /[\S]+/;
                if (!patt.test(value)) {
                    return '请上传直播间购物直播频道封面!';
                }
            }
		});

		// 添加
		form.on('submit(save)', function(data){
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type: 'POST',
				url: ns.url("live://shop/room/add"),
				data: data.field,
				dataType: 'JSON',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						location.hash = ns.hash("live://shop/room/index");
					}
				}
			});
		});

        var share_upload = new Upload({
            elem: '#shareImgUpload',
            url: ns.url("live://shop/room/addimagemedia"),
            callback: function(res) {
                if (res.code >= 0) {
                    $("input[name='shareImg']").val(res.data.media_info.media_id);
                    // $("#shareImgUpload").parent().find(".upload-img-box").html("<img src=" + ns.img(res.data.pic_info.pic_path) + " >");
					$("#shareImgUpload").html("<div id='preview_imgUpload' class='preview_img'><img layer-src class='img_prev' src=" + ns.img(res.data.pic_info.pic_path) + " ></div>");
                }
                return layer.msg(res.message);
            }
        });

		var cover_upload = new Upload({
			elem: '#coverImgUpload',
			url: ns.url("live://shop/room/addimagemedia"),
            callback: function(res) {
                if (res.code >= 0) {
                    $("input[name='coverImg']").val(res.data.media_info.media_id);
                    $("#coverImgUpload").html("<div id='preview_imgUpload' class='preview_img'><img layer-src class='img_prev' src=" + ns.img(res.data.pic_info.pic_path) + "  ></div>");
                }
                return layer.msg(res.message);
            }
		});

        var feeds_upload = new Upload({
            elem: '#feedsImgUpload',
            url: ns.url("live://shop/room/addimagemedia"),
            callback: function(res) {
                if (res.code >= 0) {
                    $("input[name='feedsImg']").val(res.data.media_info.media_id);
                    $("#feedsImgUpload").html("<div id='preview_imgUpload' class='preview_img'><img layer-src class='img_prev' src=" + ns.img(res.data.pic_info.pic_path) + "  ></div>");
                }
                return layer.msg(res.message);
            }
        });

	});

	function backLiveRoom() {
		location.hash = ns.hash("live://shop/room/index");
	}
</script>
