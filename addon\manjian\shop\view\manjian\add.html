<style>
	.discount { display: flex; justify-content: space-between; height: 34px; line-height: 34px; padding: 5px 15px; background-color: #F6FBFD; border: 1px dashed #BCE8F1; }
	.manjian-rule .level-head{display: flex;justify-content: space-between;background: #eee;padding: 0 10px;margin-bottom: 15px;}
	.manjian-rule .title { color: #454545;font-weight: 600; }
	.manjian-rule .wrap .layui-form-label { width: 140px; }
	.manjian-rule .wrap .layui-form-label + .layui-input-block { margin-left: 140px }
	.manjian-rule .wrap .layui-form-checkbox[lay-skin=primary] {margin-top: 0}
	.manjian-rule .wrap .discount-cont {padding-left: 28px;min-height: 36px}
	.manjian-rule .discount-item .word-aux {margin-left: 0}
	.layui-form-item .layui-input-inline.end-time{float: none;}
	.layui-table-body{max-height: 480px !important;}
	.goods-title{display: flex;align-items: center;}
	.goods-title .goods-img{display: flex;align-items: center;justify-content: center;width: 55px;height: 55px;margin-right: 5px;}
	.goods-title .goods-img img{max-height: 100%;max-width: 100%;}
	.goods-title .goods-name{flex: 1;line-height: 1.6;}
	.select-coupon-layer .layui-layer-content{ overflow-y: scroll!important; }
	.goods-item{height: 325px;overflow: hidden;display: block;overflow-y: auto;}
	.table-title-name{width: 100px ; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;margin-right: 135px!important;}
	.coupon-end-time{padding-right: 63px !important;}
	.word-aux{margin-left: 200px;margin-top: 0}
	.goods_num {padding-left: 20px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动名称：</label>
		<div class="layui-input-block">
			<input type="text" name="manjian_name" lay-verify="required|len" class="layui-input len-long" autocomplete="off" maxlength="40">
		</div>
			
		<div class="word-aux">
			<p>活动名称最多为25个字符</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动时间：</label>
		<div class="layui-inline">
			<div class="layui-input-inline len-mid">
				<input type="text" class="layui-input" name="start_time" lay-verify="required" id="start_time" autocomplete="off" readonly>
				<i class=" iconrili iconfont calendar"></i>
			</div>
			<span class="layui-form-mid">-</span>
			<div class="layui-input-inline len-mid end-time">
				<input type="text" class="layui-input" name="end_time" lay-verify="required|time" id="end_time" autocomplete="off" readonly>
				<i class=" iconrili iconfont calendar"></i>
			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>条件类型：</label>
		<div class="layui-input-block">
			<input type="radio" name="type" lay-filter="type" value="0" title="满N元" checked>
			<input type="radio" name="type" lay-filter="type" value="1" title="满N件">
		</div>
	</div>

	<div class="layui-form-item">
		<div class="layui-form manjian-rule">
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>优惠设置：</label>

				<div class="layui-input-block discount-level">
					<div class="level-item">
						<div class="level-head">
							<label class="title">活动层级1：</label>
						</div>
						<div class="wrap">
							<div class="condition">
								<label class="layui-form-label"><span class="required">*</span>优惠门槛：</label>
								<div class="layui-input-block">
									<div class="type-0">
										<div class="layui-form-mid">满</div>
										<div class="layui-input-inline len-short">
											<input type="number" name="money" value="" lay-verify="manjian_money" placeholder="" autocomplete="off" class="layui-input len-short">
										</div>
										<div class="layui-form-mid">元</div>
									</div>
									<div class="type-1 layui-hide">
										<div class="layui-form-mid">满</div>
										<div class="layui-input-inline len-short">
											<input type="number" name="num" value="" lay-verify="manjian_num" placeholder="" autocomplete="off" class="layui-input len-short">
										</div>
										<div class="layui-form-mid">件</div>
									</div>
								</div>
							</div>
							<div class="content">
								<label class="layui-form-label"><span class="required">*</span>优惠内容：</label>
								<div class="layui-input-block">
									<div class="discount-item discount-money">
										<div>
											<input type="checkbox" name="discount_type" value="discount_money" class="input-checkbox" lay-skin="primary"><span>订单金额优惠</span>
										</div>
										<div class="discount-cont layui-hide">
											<div class="layui-form-mid">减</div>
											<div class="layui-input-inline len-short">
												<input type="number" value="" placeholder="" autocomplete="off" class="layui-input len-short">
											</div>
											<div class="layui-form-mid">元</div>
										</div>
									</div>
									<div class="discount-item">
										<div>
											<input type="checkbox" name="" value="free_shipping" class="input-checkbox" lay-skin="primary"><span>包邮</span>
										</div>
										<div class="word-aux" >
											<p>仅参与该活动的商品包邮，非整单包邮</p>
										</div>
									</div>
									<div class="discount-item point">
										<div>
											<input type="checkbox" name="discount_type" value="point" class="input-checkbox" lay-skin="primary"><span>送积分</span>
										</div>
										<div class="discount-cont layui-hide">
											<div class="layui-form-mid">送</div>
											<div class="layui-input-inline len-short">
												<input type="number" name="" value="" placeholder="" autocomplete="off" class="layui-input len-short">
											</div>
											<div class="layui-form-mid">积分</div>
										</div>
									</div>
									<div class="discount-item coupon">
										<div>
											<input type="checkbox" name="discount_type" value="coupon" class="input-checkbox" lay-skin="primary"><span>送优惠券</span>
										</div>
										<div class="discount-cont layui-hide">
											<div><a href="javascript:;" class="text-color select-coupon">选择优惠券</a></div>
											<div class="word-aux">
												<p>请确认优惠券数量是否充足，优惠券数量不足将导致赠送失败</p>
											</div>
											<div>
												<table class="layui-table" lay-skin="nob">
												  	<colgroup>
													    <col width="30%">
													    <col width="30%">
														<col width="20%">
													    <col width="20%">
												  	</colgroup>
											  		<thead>
													    <tr>
													      	<th>优惠券</th>
													      	<th>优惠内容</th>
															<th>赠券数 <i title="每人每次参加该活动赠送的优惠券数量" class="iconfont iconwenhao1"></i> </th>
													      	<th style="text-align:center;">操作</th>
													    </tr>
												  	</thead>
												  	<tbody></tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<label class="layui-form-label"></label>
				<div class="layui-input-block">
					<button class="layui-btn" onclick="addDiscountLevel()">添加活动层级</button>
				</div>

			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动商品：</label>
		<div class="layui-input-block">
			<input type="radio" name="manjian_type" lay-filter="manjian_type" value="1" title="全部商品参与" checked>
			<input type="radio" name="manjian_type" lay-filter="manjian_type" value="2" title="指定商品参与">
		</div>
	</div>

	<div class="layui-form-item goods_list" style="display:none">
		<label class="layui-form-label"></label>
		<div class="layui-input-block">
			<table id="selected_goods_list"></table>
			<button class="layui-btn" onclick="addGoods()">选择商品</button> <span class="goods_num">已选商品（<span id="goods_num" class="text-color">0</span>）</span>
		</div>
	</div>
	<input type="hidden" name="goods_ids" lay-verify="goods_num">

	<div class="layui-form-item">
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea len-long" maxlength="150"></textarea>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">提交</button>
		<button class="layui-btn layui-btn-primary" onclick="backManjianList()">返回</button>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" onclick="delGoods({{d.goods_id}})">删除</a>
	</div>
</script>

<script type="text/javascript" src="MANJIAN_JS/add.js"></script>

<!-- 优惠券 -->
<script type="text/html" id="couponList">
	<div class="coupon-box">
		<div class="single-filter-box">
			<div class="layui-form">
				<div class="layui-input-inline">
					<input type="text" name="coupon_name" placeholder="请输入优惠券名称" class="layui-input">
					<button type="button" class="layui-btn layui-btn-primary" lay-filter="coupon-search" lay-submit>
						<i class="layui-icon">&#xe615;</i>
					</button>
				</div>
			</div>
		</div>

		<div class="gods-box">
			<table class="layui-table" lay-skin="line" lay-size="lg">
				<colgroup>
					<col width="8%">
					<col width="50%">
					<col width="15%">
					<col width="27%">
				</colgroup>
				<thead>
				<tr>
					<th class="check-box">
						<div class="layui-form">
							<input type="checkbox" name="" lay-filter="selectAll" lay-skin="primary">
						</div>
					</th>
					<th class="layui-elip">优惠券名称</th>
					<th class="layui-elip">优惠金额/折扣</th>
					<th class="layui-elip">结束时间</th>
				</tr>
				</thead>
			</table>
			<table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
				<!-- <colgroup>
					<col width="8%">
					<col width="50%">
					<col width="15%">
					<col width="27%">
				</colgroup> -->
				<tbody class="goods-item">
					{foreach $coupon_list.data as $coupon_list_k => $coupon_list_v}
					<tr>
						<td class="check-box">
							<div class="layui-form">
								{{# var a = {$coupon_list_v.coupon_type_id} }}
								{{#  if($.inArray(a.toString(), d.coupon_id) != -1){  }}
								<input type="checkbox" name="" lay-filter="select{$coupon_list_k}" lay-skin="primary" checked>
								{{#  }else{  }}
								<input type="checkbox" name="" lay-filter="select{$coupon_list_k}" lay-skin="primary">
								{{#  }  }}
								<input type="hidden" id="coupon_id" value="{$coupon_list_v.coupon_type_id}">
							</div>
						</td>
						<td>
							<div class="table-title">
								<div class="title-pic">
									{if condition="$coupon_list_v.image"}
									<img src="{:img($coupon_list_v.image)}">
									{else/}
									<img src="__ROOT__/public/uniapp/game/coupon.png">
									{/if}
								</div>
								<div class="title-content  table-title-name">
									<p class="multi-line-hiding">{$coupon_list_v.coupon_name}</p>
								</div>
							</div>
						</td>
						{if $coupon_list_v.type == 'reward'}
						<td class="layui-elip coupon-money">{$coupon_list_v.money}元</td>
						{else/}
						<td class="layui-elip coupon-money">{$coupon_list_v.discount}折</td>
						{/if}
						{if $coupon_list_v.validity_type == 0}
						<td class="layui-elip coupon-end-time">{:time_to_date($coupon_list_v.end_time)}</td>
						{elseif $coupon_list_v.validity_type == 1}
						<td class="layui-elip coupon-end-time">领取之日起{$coupon_list_v.fixed_term}天有效</td>
						{else/}
						<td class="layui-elip coupon-end-time">长期有效</td>
						{/if}
						<input type="hidden" name="at_least" value="{$coupon_list_v.at_least}">
						<input type="hidden" name="type" value="{$coupon_list_v.type}">
						<input type="hidden" name="discount" value="{$coupon_list_v.discount}">
						<input type="hidden" name="money" value="{$coupon_list_v.money}">
					</tr>
					{/foreach}
				</tbody>
			</table>
		</div>

		<button class="layui-btn" onclick="couponSelected()">确定</button>
	</div>
</script>

<!-- 优惠券 -->
<script type="text/html" id="couponListSearch">
	<div class="coupon-box">
		<div class="single-filter-box">
			<div class="layui-form">
				<div class="layui-input-inline">
					<input type="text" name="coupon_name" placeholder="请输入优惠券名称" class="layui-input">
					<button type="button" class="layui-btn layui-btn-primary" lay-filter="coupon-search" lay-submit>
						<i class="layui-icon">&#xe615;</i>
					</button>
				</div>
			</div>
		</div>

		<div class="gods-box">
			<table class="layui-table" lay-skin="line" lay-size="lg">
				<colgroup>
					<col width="8%">
					<col width="50%">
					<col width="15%">
					<col width="27%">
				</colgroup>
				<thead>
					<tr>
						<th class="check-box">
							<div class="layui-form">
								<input type="checkbox" name="" lay-filter="selectAll" lay-skin="primary">
							</div>
						</th>
						<th class="layui-elip">优惠券名称</th>
						<th class="layui-elip">优惠金额/折扣</th>
						<th class="layui-elip">结束时间</th>
					</tr>
				</thead>
			</table>
			<table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
				<!-- <colgroup>
					<col width="8%">
					<col width="50%">
					<col width="15%">
					<col width="27%">
				</colgroup> -->
				<tbody class="goods-item">

					{{#  layui.each(d.list, function(index, item){ }}
					<tr>
						<td class="check-box">
							<div class="layui-form">
								{{# var a = item.coupon_type_id }}
								{{#  if($.inArray(a.toString(), d.coupon_id) != -1){  }}
								<input type="checkbox" name="" lay-filter="select{{item.index}}" lay-skin="primary" checked>
								{{#  }else{  }}
								<input type="checkbox" name="" lay-filter="select{{item.index}}" lay-skin="primary">
								{{#  }  }}
								<input type="hidden" id="coupon_id" value="{{item.coupon_type_id}}">
							</div>
						</td>
						<td>
							<div class="table-title">
								<div class="title-pic">
									{{#if(item.image) {  }}
									<img src="{{item.image}}">
									{{#  }else{  }}
									<img src="__ROOT__/public/uniapp/game/coupon.png">
									{{#  }  }}
								</div>
								<div class="title-content table-title-name">
									<p class="multi-line-hiding">{{item.coupon_name}}</p>
								</div>
							</div>
						</td>
						{{#if(item.type == 'reward') {  }}
						<td class="layui-elip coupon-money">{{item.money}}元</td>
						{{#  }else{  }}
						<td class="layui-elip coupon-money">{{item.discount}}折</td>
						{{#  }  }}
						{{#if(item.validity_type == 0) {  }}

						<td class="layui-elip coupon-end-time">{{layui.util.toDateString(item.end_time * 1000,'yyyy-MM-dd HH:mm:ss')}}</td>
						{{#  }else{  }}
						<td class="layui-elip coupon-end-time">领取之日起{{item.fixed_term}}天有效</td>
						{{#  }  }}
						<input type="hidden" name="at_least" value="{{item.at_least}}">
						<input type="hidden" name="type" value="{{item.type}}">
						<input type="hidden" name="discount" value="{{item.discount}}">
						<input type="hidden" name="money" value="{{item.money}}">
					</tr>
					{{#  }); }}

				</tbody>
			</table>
		</div>

		<button class="layui-btn" onclick="couponSelected()">确定</button>
	</div>
</script>

<!-- 优惠券-名称 -->
<script type="text/html" id="couponName">
	<div class="table-tuwen-box">
		<div class="font-box">
			<p class="multi-line-hiding">{{d.coupon_name}}</p>
		</div>
	</div>
</script>

<!-- 优惠券-操作 -->
<script type="text/html" id="couponOperation">

	{{# var select_coupon_list = ','+coupon_list+','}}

	{{# if(select_coupon_list.indexOf(','+d.coupon_type_id+',') != -1){ }}
	<p title="该优惠券已参加">已添加</p>
	{{# }else{ }}
	<a class="layui-btn" lay-event="add">添加</a>
	{{# } }}

</script>
<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
	<button class="layui-btn layui-btn-primary" lay-event="add">批量添加</button>
</script>
<script type="text/html" id="addCoupon">
	{{# for(var i = 0; i < d.length; i++){ }}
		<tr data-coupon="{{ d[i].coupon_type_id }}">
			<td>{{d[i].coupon_name}}</td>
			{{# if(d[i].at_least > 0){}}
				<td>满{{d[i].at_least}}{{d[i].type == 'discount' ? '打'+ d[i].discount +'折' : '减' + d[i].money}}</td>
			{{#} else {}}
				<td>无门槛，{{d[i].type == 'discount' ? '打'+ d[i].discount +'折' : '减' + d[i].money}}</td>
			{{#}}}
			<td><input type="number" name="number" value="1" class="layui-input len-short" lay-verify="coupon_num"></td>
			<td style="text-align:center;">
				<a href="javascript:;" onClick="deleteCoupon(this, {{i}})" className="text-color">删除</a>
			</td>
		</tr>
	{{# } }}

</script>
<script type="text/javascript">
layui.use(['carousel'], function() {
	var carousel = layui.carousel;
	carousel.render({
		elem: '#carousel'
		, width: '100%' //设置容器宽度
		, arrow: 'always' //始终显示箭头
	});
})
</script>
