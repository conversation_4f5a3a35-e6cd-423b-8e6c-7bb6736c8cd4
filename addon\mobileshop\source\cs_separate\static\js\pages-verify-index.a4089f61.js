(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-verify-index"],{"2cc4":function(e,i,t){var n=t("c86c");i=n(!1),i.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-a53190f0]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-a53190f0]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-a53190f0]{position:fixed;left:0;right:0;z-index:998}.verify[data-v-a53190f0]{padding:1px 0}.scan[data-v-a53190f0]{text-align:center;padding:%?30?%;margin:%?30?% %?30?%;background-color:#fff;border-radius:%?10?%}.scan uni-image[data-v-a53190f0]{width:%?300?%;height:%?300?%}.scan .recode[data-v-a53190f0]{text-align:right;margin-bottom:%?30?%}.scan .tip[data-v-a53190f0]{margin-top:%?20?%}.menu_item[data-v-a53190f0]{margin:%?30?% %?30?% 0;padding-top:%?25?%;padding-bottom:%?30?%}.menu_item .menu_title[data-v-a53190f0]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;font-size:%?32?%;font-weight:700;margin-bottom:%?10?%}.menu_item .menu_title .line[data-v-a53190f0]{display:inline-block;height:%?28?%;width:%?4?%;border-radius:%?4?%}.menu_item .menu_content[data-v-a53190f0]{display:flex;align-items:center;margin-top:%?20?%;background-color:#fff;border-radius:%?10?%}.menu_item .menu_content .uni-input[data-v-a53190f0]{flex:1;padding:0 %?30?%}.menu_item .menu_content uni-button[data-v-a53190f0]{border-radius:%?10?%;border-top-left-radius:0;border-bottom-left-radius:0;margin:0}.list[data-v-a53190f0]{background-color:#fff;margin:0 %?30?% %?30?%;padding:0 %?30?% %?20?%;border-radius:%?10?%}.list .title[data-v-a53190f0]{display:flex;align-items:center;padding:%?20?% 0;font-size:%?24?%;border-bottom:1px solid #eee}.list .title .time[data-v-a53190f0]{flex:1;color:#909399}.list .title .status[data-v-a53190f0]{margin-left:%?30?%}.list .goods[data-v-a53190f0]{display:flex;padding:%?30?% 0}.list .goods .img[data-v-a53190f0]{height:%?140?%;width:%?140?%;min-width:%?140?%}.list .goods .info[data-v-a53190f0]{flex:1;margin-left:%?30?%;display:flex;flex-direction:column;justify-content:space-between}.list .goods .info .goods_name[data-v-a53190f0]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;line-height:1.5}.list .goods .info .flex[data-v-a53190f0]{margin-top:%?10?%;display:flex;justify-content:space-between;align-items:baseline}.list .goods .info .flex .flex_left[data-v-a53190f0]{font-size:%?24?%;color:#909399}.list .other_info[data-v-a53190f0]{color:#909399;font-size:%?24?%}',""]),e.exports=i},"2ffb":function(e,i,t){"use strict";var n=t("4a0d"),a=t.n(n);a.a},4550:function(e,i,t){t("6a54");var n=t("8bcf");e.exports=function(e,i,t){return i=n(i),i in e?Object.defineProperty(e,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[i]=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"4a0d":function(e,i,t){var n=t("2cc4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=t("967d").default;a("044b49f0",n,!0,{sourceMap:!1,shadowMode:!1})},"56c9":function(e,i,t){t("9e15"),t("884b"),t("01a2"),t("e39c"),t("bf0f"),t("7a76"),t("c9b5"),t("64aa");var n=t("bdbb")["default"];e.exports=function(e,i){if("object"!==n(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,i||"default");if("object"!==n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"5d30":function(e,i,t){"use strict";t("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,t("5ef2"),t("5c47"),t("af8f");var n=t("9f0f"),a={data:function(){return{verify_code:"",search_info:"",item_array:[],remark_array:[]}},onShow:function(){this.$util.checkToken("/pages/verify/index")},methods:{search:function(){var e=this;this.$api.sendRequest({url:"/shopapi/verify/verifyCard",data:{verify_code:this.verify_code},success:function(i){i.code>=0&&i.data?(e.search_info=i.data,e.item_array=i.data.data.item_array,e.remark_array=i.data.data.remark_array):(e.remark_array=[],e.item_array=[],e.$util.showToast({title:i.message}))}})},verify:function(){var e=this;this.$api.sendRequest({url:"/shopapi/verify/verify",data:{verify_code:this.verify_code},success:function(i){var t=e;e.$util.showToast({title:i.message}),i.code>=0&&setTimeout((function(){t.$util.redirectTo("/pages/verify/records")}),1e3)}})},scanCode:function(){var e=this;if(this.$util.isWeiXin()){if(this.$util.isWeiXin()){if("ios"==uni.getSystemInfoSync().platform)var i=uni.getStorageSync("initUrl");else i=location.href;this.$api.sendRequest({url:"/wechat/api/wechat/jssdkconfig",data:{url:i},success:function(i){if(0==i.code){var t=new n.Weixin;t.init(i.data),t.scanQRCode((function(i){if(i.resultStr){var t=i.result;-1==t.indexOf("http://")&&-1==t.indexOf("https://")||-1==t.indexOf("pages_tool/verification/detail")?-1!=t.indexOf("CODE_128")?(e.verify_code=t.split(",")[1],e.search()):e.$util.showToast({title:"无法识别"}):(e.verify_code=e.getQueryVariable(t,"code"),e.search())}}),["qrCode","barCode"])}else e.$util.showToast({title:i.message})}})}}else this.$util.showToast({title:"H5端不支持扫码核销"})},getQueryVariable:function(e,i){var t=e,n=t.split("?");if(n.length>1){for(var a,o=n[1].split("&"),r=0;r<o.length;r++)if(a=o[r].split("="),null!=a&&a[0]==i)return a[1];return""}return""},imgError:function(e){this.item_array[e].img=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()}}};i.default=a},"88a9":function(e,i,t){"use strict";t.r(i);var n=t("5d30"),a=t.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){t.d(i,e,(function(){return n[e]}))}(o);i["default"]=a.a},"8bcf":function(e,i,t){var n=t("bdbb")["default"],a=t("56c9");e.exports=function(e){var i=a(e,"string");return"symbol"===n(i)?i:String(i)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"94f7":function(e,i,t){"use strict";t.r(i);var n=t("ad0c"),a=t("88a9");for(var o in a)["default"].indexOf(o)<0&&function(e){t.d(i,e,(function(){return a[e]}))}(o);t("2ffb");var r=t("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"a53190f0",null,!1,n["a"],void 0);i["default"]=s.exports},"9f0f":function(e,i,t){"use strict";t("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.Weixin=void 0,t("d4b5");i.Weixin=function(){var e=t("cee0");this.init=function(i){e.config({debug:!1,appId:i.appId,timestamp:i.timestamp,nonceStr:i.nonceStr,signature:i.signature,jsApiList:["chooseWXPay","openAddress","updateAppMessageShareData","updateTimelineShareData","scanQRCode"]})},this.pay=function(i,t){e.ready((function(){e.chooseWXPay({timestamp:i.timestamp,nonceStr:i.nonceStr,package:i.package,signType:i.signType,paySign:i.paySign,success:function(e){"function"==typeof t&&t(e)}})}))},this.openAddress=function(i){e.ready((function(){e.openAddress({success:function(e){"function"==typeof i&&i(e)},fail:function(e){alert(JSON.stringify(e))}})}))},this.setShareData=function(i,t){e.ready((function(){e.updateAppMessageShareData({title:i.title||"",desc:i.desc||"",link:i.link||"",imgUrl:i.imgUrl||"",success:function(){"function"==typeof t&&t(res)}}),e.updateTimelineShareData({title:i.title||"",link:i.link||"",imgUrl:i.imgUrl||"",success:function(){"function"==typeof t&&t(res)}})}))},this.scanQRCode=function(i){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["qrCode"];e.ready((function(){e.scanQRCode({needResult:1,scanType:t,success:function(e){"function"==typeof i&&i(e)}})}))}}},ad0c:function(e,i,t){"use strict";t.d(i,"b",(function(){return n})),t.d(i,"c",(function(){return a})),t.d(i,"a",(function(){}));var n=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t("v-uni-view",{staticClass:"verify iphone-safe-area"},[e.$util.isWeiXin()?t("v-uni-view",{staticClass:"scan"},[t("v-uni-image",{attrs:{src:e.$util.img("public/uniapp/shop_uniapp/verify/verify.png"),mode:"aspectFit"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.scanCode()}}}),t("v-uni-view",{staticClass:"tip color-tip"},[e._v("点击此区域扫描核销码")])],1):e._e(),t("v-uni-view",{staticClass:"menu_item"},[t("v-uni-view",{staticClass:"menu_title"},[t("v-uni-view",[t("v-uni-text",{staticClass:"line color-base-bg margin-right"}),t("v-uni-text",[e._v("输入核销码")])],1),t("v-uni-view",{staticClass:"recode color-base-text",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.redirectTo("/pages/verify/records")}}},[e._v("核销记录")])],1),t("v-uni-view",{staticClass:"menu_content"},[t("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入核销码"},on:{confirm:function(i){arguments[0]=i=e.$handleEvent(i),e.search()}},model:{value:e.verify_code,callback:function(i){e.verify_code=i},expression:"verify_code"}}),t("v-uni-button",{attrs:{type:"primary",disabled:""==e.verify_code},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.search.apply(void 0,arguments)}}},[e._v("提交核销码")])],1)],1),e.search_info?t("v-uni-view",[t("v-uni-view",{staticClass:"list"},[e._l(e.item_array,(function(i,n){return t("v-uni-view",{key:n,staticClass:"goods"},[t("v-uni-image",{staticClass:"img",attrs:{src:e.$util.img(i.img),mode:"aspectFit"},on:{error:function(i){arguments[0]=i=e.$handleEvent(i),e.imgError(n)}}}),t("v-uni-view",{staticClass:"info"},[t("v-uni-view",{staticClass:"goods_name"},[e._v(e._s(i.name))]),t("v-uni-view",{staticClass:"flex"},[t("v-uni-view",{staticClass:"flex_left"},[e._v("x"+e._s(i.num))]),t("v-uni-view",{staticClass:"flex_right"},[t("v-uni-text",{staticClass:"font-size-tag"},[e._v("￥")]),e._v(e._s(i.price))],1)],1)],1)],1)})),e._l(e.remark_array,(function(i,n){return t("v-uni-view",{key:n,staticClass:"other_info"},[e._v(e._s(i.title)+"："+e._s(i.value))])}))],2),t("v-uni-button",{attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.verify.apply(void 0,arguments)}}},[e._v("验证使用")])],1):e._e()],1)},a=[]},cee0:function(e,i,t){(function(e){var n,a=t("4550").default,o=t("bdbb").default;t("5ef2"),t("5c47"),t("a1c1"),t("d4b5"),t("2c10"),t("aa9c"),function(a,r){"object"==o(i)&&"object"==o(e)?e.exports=r(a):(n=function(){return r(a)}.call(i,t,i,e),void 0===n||(e.exports=n))}(window,(function(e,i){function t(i,t,n){e.WeixinJSBridge?WeixinJSBridge.invoke(i,o(t),(function(e){s(i,e,n)})):d(i,n)}function n(i,t,n){e.WeixinJSBridge?WeixinJSBridge.on(i,(function(e){n&&n.trigger&&n.trigger(e),s(i,e,t)})):d(i,n||t)}function o(e){return(e=e||{}).appId=C.appId,e.verifyAppId=C.appId,e.verifySignType="sha1",e.verifyTimestamp=C.timestamp+"",e.verifyNonceStr=C.nonceStr,e.verifySignature=C.signature,e}function r(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function s(e,i,t){"openEnterpriseChat"==e&&(i.errCode=i.err_code),delete i.err_code,delete i.err_desc,delete i.err_detail;var n=i.errMsg;n||(n=i.err_msg,delete i.err_msg,n=function(e,i){var t=e,n=m[t];n&&(t=n);var a="ok";if(i){var o=i.indexOf(":");"confirm"==(a=i.substring(o+1))&&(a="ok"),"failed"==a&&(a="fail"),-1!=a.indexOf("failed_")&&(a=a.substring(7)),-1!=a.indexOf("fail_")&&(a=a.substring(5)),"access denied"!=(a=(a=a.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=a||(a="permission denied"),"config"==t&&"function not exist"==a&&(a="ok"),""==a&&(a="fail")}return t+":"+a}(e,n),i.errMsg=n),(t=t||{})._complete&&(t._complete(i),delete t._complete),n=i.errMsg||"",C.debug&&!t.isInnerInvoke&&alert(JSON.stringify(i));var a=n.indexOf(":");switch(n.substring(a+1)){case"ok":t.success&&t.success(i);break;case"cancel":t.cancel&&t.cancel(i);break;default:t.fail&&t.fail(i)}t.complete&&t.complete(i)}function c(e){if(e){for(var i=0,t=e.length;i<t;++i){var n=e[i],a=p[n];a&&(e[i]=a)}return e}}function d(e,i){if(!(!C.debug||i&&i.isInnerInvoke)){var t=m[e];t&&(e=t),i&&i._complete&&delete i._complete,console.log('"'+e+'",',i||"")}}function l(){return(new Date).getTime()}function u(i){S&&(e.WeixinJSBridge?i():g.addEventListener&&g.addEventListener("WeixinJSBridgeReady",i,!1))}if(!e.jWeixin){var f,p={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},m=function(){var e={};for(var i in p)e[p[i]]=i;return e}(),g=e.document,v=g.title,h=navigator.userAgent.toLowerCase(),y=navigator.platform.toLowerCase(),_=!(!y.match("mac")&&!y.match("win")),w=-1!=h.indexOf("wxdebugger"),S=-1!=h.indexOf("micromessenger"),b=-1!=h.indexOf("android"),x=-1!=h.indexOf("iphone")||-1!=h.indexOf("ipad"),k=(R=h.match(/micromessenger\/(\d+\.\d+\.\d+)/)||h.match(/micromessenger\/(\d+\.\d+)/))?R[1]:"",I={initStartTime:l(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},T={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:x?1:b?2:-1,clientVersion:k,url:encodeURIComponent(location.href)},C={},M={_completes:[]},P={state:0,data:{}};u((function(){I.initEndTime=l()}));var A=!1,V=[],O=(f={config:function(i){d("config",C=i);var n=!1!==C.check;u((function(){if(n)t(p.config,{verifyJsApiList:c(C.jsApiList)},function(){M._complete=function(e){I.preVerifyEndTime=l(),P.state=1,P.data=e},M.success=function(e){T.isPreVerifyOk=0},M.fail=function(e){M._fail?M._fail(e):P.state=-1};var e=M._completes;return e.push((function(){!function(e){if(!(_||w||C.debug||k<"6.0.2"||T.systemType<0)){var i=new Image;T.appId=C.appId,T.initTime=I.initEndTime-I.initStartTime,T.preVerifyTime=I.preVerifyEndTime-I.preVerifyStartTime,O.getNetworkType({isInnerInvoke:!0,success:function(e){T.networkType=e.networkType;var t="https://open.weixin.qq.com/sdk/report?v="+T.version+"&o="+T.isPreVerifyOk+"&s="+T.systemType+"&c="+T.clientVersion+"&a="+T.appId+"&n="+T.networkType+"&i="+T.initTime+"&p="+T.preVerifyTime+"&u="+T.url;i.src=t}})}}()})),M.complete=function(i){for(var t=0,n=e.length;t<n;++t)e[t]();M._completes=[]},M}()),I.preVerifyStartTime=l();else{P.state=1;for(var e=M._completes,i=0,a=e.length;i<a;++i)e[i]();M._completes=[]}})),O.invoke||(O.invoke=function(i,t,n){e.WeixinJSBridge&&WeixinJSBridge.invoke(i,o(t),n)},O.on=function(i,t){e.WeixinJSBridge&&WeixinJSBridge.on(i,t)})},ready:function(e){0!=P.state?e():(M._completes.push(e),!S&&C.debug&&e())},error:function(e){k<"6.0.2"||(-1==P.state?e(P.data):M._fail=e)},checkJsApi:function(e){t("checkJsApi",{jsApiList:c(e.jsApiList)},(e._complete=function(e){if(b){var i=e.checkResult;i&&(e.checkResult=JSON.parse(i))}e=function(e){var i=e.checkResult;for(var t in i){var n=m[t];n&&(i[n]=i[t],delete i[t])}return e}(e)},e))},onMenuShareTimeline:function(e){n(p.onMenuShareTimeline,{complete:function(){t("shareTimeline",{title:e.title||v,desc:e.title||v,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){n(p.onMenuShareAppMessage,{complete:function(i){"favorite"===i.scene?t("sendAppMessage",{title:e.title||v,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):t("sendAppMessage",{title:e.title||v,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){n(p.onMenuShareQQ,{complete:function(){t("shareQQ",{title:e.title||v,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){n(p.onMenuShareWeibo,{complete:function(){t("shareWeiboApp",{title:e.title||v,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){n(p.onMenuShareQZone,{complete:function(){t("shareQZone",{title:e.title||v,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){t("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){t("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){t("startRecord",{},e)},stopRecord:function(e){t("stopRecord",{},e)},onVoiceRecordEnd:function(e){n("onVoiceRecordEnd",e)},playVoice:function(e){t("playVoice",{localId:e.localId},e)},pauseVoice:function(e){t("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){t("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){n("onVoicePlayEnd",e)},uploadVoice:function(e){t("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){t("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){t("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){t("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(b){var i=e.localIds;try{i&&(e.localIds=JSON.parse(i))}catch(e){}}},e))},getLocation:function(e){},previewImage:function(e){t(p.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){t("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){t("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(e){!1===A?(A=!0,t("getLocalImgData",{localId:e.localId},(e._complete=function(e){if(A=!1,0<V.length){var i=V.shift();wx.getLocalImgData(i)}},e))):V.push(e)},getNetworkType:function(e){t("getNetworkType",{},(e._complete=function(e){e=function(e){var i=e.errMsg;e.errMsg="getNetworkType:ok";var t=e.subtype;if(delete e.subtype,t)e.networkType=t;else{var n=i.indexOf(":"),a=i.substring(n+1);switch(a){case"wifi":case"edge":case"wwan":e.networkType=a;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){t("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)}},a(f,"getLocation",(function(e){t(p.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))})),a(f,"hideOptionMenu",(function(e){t("hideOptionMenu",{},e)})),a(f,"showOptionMenu",(function(e){t("showOptionMenu",{},e)})),a(f,"closeWindow",(function(e){t("closeWindow",{},e=e||{})})),a(f,"hideMenuItems",(function(e){t("hideMenuItems",{menuList:e.menuList},e)})),a(f,"showMenuItems",(function(e){t("showMenuItems",{menuList:e.menuList},e)})),a(f,"hideAllNonBaseMenuItem",(function(e){t("hideAllNonBaseMenuItem",{},e)})),a(f,"showAllNonBaseMenuItem",(function(e){t("showAllNonBaseMenuItem",{},e)})),a(f,"scanQRCode",(function(e){t("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(x){var i=e.resultStr;if(i){var t=JSON.parse(i);e.resultStr=t&&t.scan_code&&t.scan_code.scan_result}}},e))})),a(f,"openAddress",(function(e){t(p.openAddress,{},(e._complete=function(e){var i;(i=e).postalCode=i.addressPostalCode,delete i.addressPostalCode,i.provinceName=i.proviceFirstStageName,delete i.proviceFirstStageName,i.cityName=i.addressCitySecondStageName,delete i.addressCitySecondStageName,i.countryName=i.addressCountiesThirdStageName,delete i.addressCountiesThirdStageName,i.detailInfo=i.addressDetailInfo,delete i.addressDetailInfo,e=i},e))})),a(f,"openProductSpecificView",(function(e){t(p.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)})),a(f,"addCard",(function(e){for(var i=e.cardList,n=[],a=0,o=i.length;a<o;++a){var r=i[a],s={card_id:r.cardId,card_ext:r.cardExt};n.push(s)}t(p.addCard,{card_list:n},(e._complete=function(e){var i=e.card_list;if(i){for(var t=0,n=(i=JSON.parse(i)).length;t<n;++t){var a=i[t];a.cardId=a.card_id,a.cardExt=a.card_ext,a.isSuccess=!!a.is_succ,delete a.card_id,delete a.card_ext,delete a.is_succ}e.cardList=i,delete e.card_list}},e))})),a(f,"chooseCard",(function(e){t("chooseCard",{app_id:C.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))})),a(f,"openCard",(function(e){for(var i=e.cardList,n=[],a=0,o=i.length;a<o;++a){var r=i[a],s={card_id:r.cardId,code:r.code};n.push(s)}t(p.openCard,{card_list:n},e)})),a(f,"consumeAndShareCard",(function(e){t(p.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)})),a(f,"chooseWXPay",(function(e){t(p.chooseWXPay,r(e),e)})),a(f,"openEnterpriseRedPacket",(function(e){t(p.openEnterpriseRedPacket,r(e),e)})),a(f,"startSearchBeacons",(function(e){t(p.startSearchBeacons,{ticket:e.ticket},e)})),a(f,"stopSearchBeacons",(function(e){t(p.stopSearchBeacons,{},e)})),a(f,"onSearchBeacons",(function(e){n(p.onSearchBeacons,e)})),a(f,"openEnterpriseChat",(function(e){t("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)})),a(f,"launchMiniProgram",(function(e){t("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var i=e.split("?")[0],t=e.split("?")[1];return i+=".html",void 0!==t?i+"?"+t:i}}(e.path),envVersion:e.envVersion},e)})),a(f,"miniProgram",{navigateBack:function(e){e=e||{},u((function(){t("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){u((function(){t("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){u((function(){t("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){u((function(){t("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){u((function(){t("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){u((function(){t("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(i){u((function(){i({miniprogram:"miniprogram"===e.__wxjs_environment})}))}}),f),E=1,L={};return g.addEventListener("error",(function(e){if(!b){var i=e.target,t=i.tagName,n=i.src;if(("IMG"==t||"VIDEO"==t||"AUDIO"==t||"SOURCE"==t)&&-1!=n.indexOf("wxlocalresource://")){e.preventDefault(),e.stopPropagation();var a=i["wx-id"];if(a||(a=E++,i["wx-id"]=a),L[a])return;L[a]=!0,wx.ready((function(){wx.getLocalImgData({localId:n,success:function(e){i.src=e.localData}})}))}}}),!0),g.addEventListener("load",(function(e){if(!b){var i=e.target,t=i.tagName;if(i.src,"IMG"==t||"VIDEO"==t||"AUDIO"==t||"SOURCE"==t){var n=i["wx-id"];n&&(L[n]=!1)}}}),!0),i&&(e.wx=e.jWeixin=O),O}var R}))}).call(this,t("dc84")(e))}}]);