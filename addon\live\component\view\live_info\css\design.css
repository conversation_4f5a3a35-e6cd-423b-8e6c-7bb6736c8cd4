.live-wrap { /*background: #fff;*/ /* border-radius: 8px; */overflow: hidden;}
.live-wrap .banner-wrap {width: 100%;position: relative;line-height: 1;display: flex;}
.live-wrap .banner-wrap img {width: 100%;height:104px;}
.live-wrap .banner-wrap .shade {width: 100%;height: 100%;position: absolute;background: rgba(180, 180, 180, 0.3);left: 0;top: 0;z-index: 5;}
.live-wrap .banner-wrap .wrap {width: 100%;height: 100%;position: absolute;left: 0;top: 0;z-index: 10;padding: 10px;box-sizing: border-box;}
.live-wrap .banner-wrap .wrap .room-name {font-size: 14px;color: #fff;line-height: 1;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;display: flex;align-items: center;}
.live-wrap .banner-wrap .room-name .status-name {display: inline-block;font-size: 12px;color: #fff;padding: 4px 6px;background-color: #FF4544;border-radius: 18px;margin-right: 10px;}
.live-wrap .banner-wrap .room-name .status-name img{height: 9px;width: 9px;vertical-align: middle;margin-right: 5px;}
.live-wrap .room-name .status-name .iconzhibozhong {font-size: 12px;color: #fff;margin-right: 2px;}

.live-wrap .room-info {padding: 10px 10px;background: #fff;display: flex;align-items: center;height: 30px;color: #303133;font-size: 14px;}
.live-wrap .room-info .anchor-img {width: 30px;height: 30px;border-radius: 50%;overflow: hidden;margin-right: 10px;}
.live-wrap .room-info .anchor-name, .live-wrap .room-info .goods-text {font-size: 12px;line-height: 1;color:#303133;}
.live-wrap .room-info .separate {color: #808080;margin: 0 5px;line-height: 1;}

.component-live-info h3 {font-size: 14px; /* font-weight: 600; */padding: 5px 10px 10px 10px;}
.component-live-info .layui-form-item .layui-input-inline {padding-left: 20px;}

/* 复选 */
.component-live-info .checkbox-wrap .layui-form-checkbox, .checkbox-wrap .layui-input-inline-checkbox .layui-form-checkbox {float: right;}
.component-live-info .checkbox-wrap .layui-form-item .layui-form-checkbox[lay-skin=primary] {margin-top: -4px;padding-left:0;}
.component-live-info .checkbox-wrap .layui-form-item .layui-input-inline-checkbox .layui-form-checkbox[lay-skin=primary] {margin-top: 8px;}
.component-live-info .layui-form-checkbox[lay-skin=primary] {margin-top: -4px;padding-left:0;}

.component-live-info .tab-wrap{display: none !important;}