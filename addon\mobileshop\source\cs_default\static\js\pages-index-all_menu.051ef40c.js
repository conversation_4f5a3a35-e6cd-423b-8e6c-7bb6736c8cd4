(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-all_menu"],{"0e00":function(e,t,i){"use strict";i.r(t);var n=i("14cb"),a=i("b722");for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(s);i("4c6d");var o=i("828b"),p=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"28cd7c74",null,!1,n["a"],void 0);t["default"]=p.exports},"14cb":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[i("v-uni-view",{staticClass:"u-tabbar-inner"},e._l(e.list,(function(t,n){return i("v-uni-view",{key:n,staticClass:"tabbar-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.redirectTo(t.link)}}},[i("v-uni-image",{attrs:{src:n==e.linkIndex?t.selectedIconPath:t.iconPath,mode:"scaleToFill"}}),i("v-uni-text",{staticClass:"item-desc",class:{"color-base-text":n==e.linkIndex}},[e._v(e._s(t.text))])],1)})),1),i("v-uni-view",{staticClass:"un-tabbar-flex"})],1)},a=[]},1731:function(e,t,i){"use strict";i.r(t);var n=i("9106"),a=i("297d");for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(s);i("4ba8");var o=i("828b"),p=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"cfde43fc",null,!1,n["a"],void 0);t["default"]=p.exports},"1ddc":function(e,t,i){var n=i("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-28cd7c74]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-28cd7c74]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-28cd7c74]{position:fixed;left:0;right:0;z-index:998}.u-tabbar-inner[data-v-28cd7c74]{position:fixed;left:0;bottom:0;width:100%;min-height:50px;z-index:998;display:flex;background-color:#fff;box-sizing:border-box;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.u-tabbar-inner .tabbar-item[data-v-28cd7c74]{display:flex;flex-direction:column;align-items:center;width:%?150?%}.u-tabbar-inner .tabbar-item uni-image[data-v-28cd7c74]{margin-top:7px;width:24px;height:24px}.u-tabbar-inner .tabbar-item .item-desc[data-v-28cd7c74]{font-size:10px;color:#333}.u-tabbar-inner .un-tabbar-flex[data-v-28cd7c74]{height:50px;background-color:initial;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}.un-tabbar-flex[data-v-28cd7c74]{width:100%;height:50px;background-color:initial;padding-bottom:0;padding-bottom:constant(safe-area-inset-bottom);padding-bottom:env(safe-area-inset-bottom)}',""]),e.exports=t},"23b0":function(e,t,i){var n=i("687b");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("967d").default;a("96c7a23c",n,!0,{sourceMap:!1,shadowMode:!1})},"297d":function(e,t,i){"use strict";i.r(t);var n=i("382d"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=a.a},"382d":function(e,t,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("bf0f"),i("2797"),i("aa9c");var a=i("9aa2"),s=n(i("f2a2")),o=n(i("2f32")),p=n(i("0e00")),r=n(i("b03f")),u={data:function(){return{permission:[],menuList:[{title:"店铺经营",menu:[{page:"/pages/goods/edit/index",img:"public/uniapp/shop_uniapp/index/manage_good_send.png",name:"PHYSICAL_GOODS_ADD",title:"商品发布"},{page:"/pages/goods/list",img:"public/uniapp/shop_uniapp/index/manage_good.png",name:"GOODS_MANAGE",title:"商品管理"},{page:"/pages/order/list",img:"public/uniapp/shop_uniapp/index/manage_order.png",name:"ORDER_MANAGE",title:"订单管理"},{page:"/pages/member/list",img:"public/uniapp/shop_uniapp/index/member_card.png",name:"MEMBER_LIST",title:"会员管理"},{page:"/pages/invoices/invoices",img:"public/uniapp/shop_uniapp/index/invoice_setting.png",name:"INVOICE_LIST",title:"发票管理"},{page:"/pages/storemanage/storemanage",img:"public/uniapp/shop_uniapp/index/store_setting.png",name:"STORE_LIST",title:"门店管理"}]},{title:"财务管理",menu:[{page:"/pages/property/dashboard/index",img:"public/uniapp/shop_uniapp/index/finance_survey.png",name:"ACCOUNT_DASHBOARD_INDEX",title:"财务概况"},{page:"/pages/property/withdraw/list",img:"public/uniapp/shop_uniapp/index/tixian.png",name:"MEMBER_WITHDRAW_LIST",title:"会员提现"},{page:"/pages/property/settlement/list_store",img:"public/uniapp/shop_uniapp/index/store_jiesuan.png",name:"ADDON_STORE_SHOP_STORE_SETTLEMENT",title:"门店结算"}]},{title:"营业数据",menu:[{page:"/pages/statistics/transaction",img:"public/uniapp/shop_uniapp/index/tongji_jiaoyi.png",name:"STAT_ORDER",title:"交易数据"},{page:"/pages/statistics/goods",img:"public/uniapp/shop_uniapp/index/tongji_good.png",name:"STAT_GOODS",title:"商品数据"},{page:"/pages/statistics/member",img:"public/uniapp/shop_uniapp/index/tongji_shop.png",name:"STAT_MEMBER",title:"会员数据"},{page:"/pages/statistics/store",img:"public/uniapp/shop_uniapp/index/tongji_shop.png",name:"STAT_STORE",title:"门店数据"},{page:"/pages/statistics/visit",img:"public/uniapp/shop_uniapp/index/tongji_member.png",name:"STAT_VISIT",title:"流量数据"}]},{title:"店铺设置",menu:[{page:"/pages/my/shop/config",img:"public/uniapp/shop_uniapp/index/set_shop.png",name:"SHOP_CONFIG",title:"店铺信息"},{page:"/pages/my/user/user",img:"public/uniapp/shop_uniapp/index/set_member.png",name:"USER_LIST",title:"用户管理"},{page:"/pages/my/statistics",img:"public/uniapp/shop_uniapp/index/set_jiaoyi.png",name:"ORDER_CONFIG_SETTING",title:"交易设置"},{page:"/pages/goods/config",img:"public/uniapp/shop_uniapp/index/goods_setting.png",name:"CONFIG_BASE_GOODS",title:"商品设置"},{page:"/pages/my/shop/contact",img:"public/uniapp/shop_uniapp/index/set_address.png",name:"SHOP_CONTACT",title:"联系地址"},{page:"/pages/verify/index",img:"public/uniapp/shop_uniapp/index/verify.png",name:"ORDER_VERIFY_CARD",title:"核销台"}]}]}},components:{uniGrid:s.default,uniGridItem:o.default,uCharts:r.default,diyBottomNav:p.default},onLoad:function(){uni.getStorageSync("menuPermission")&&(this.permission=uni.getStorageSync("menuPermission")),this.getPermission()},computed:{handleMenu:function(){var e=this;if(0==this.permission.length)return this.menuList;var t=[];return this.menuList.forEach((function(i,n){var a={title:i.title,menu:[]};i.menu.forEach((function(t,i){e.menuAuth(t.name)&&a.menu.push(t)})),a.menu.length&&t.push(a)})),t}},methods:{getPermission:function(){var e=this;(0,a.getUserPermission)().then((function(t){0==t.code&&(e.permission=t.data,uni.setStorageSync("menuPermission",t.data),e.$refs.loadingCover.hide())}))},menuAuth:function(e){return 0==this.permission.length||-1!=this.$util.inArray(e,this.permission)}}};t.default=u},"4ba8":function(e,t,i){"use strict";var n=i("23b0"),a=i.n(n);a.a},"4c6d":function(e,t,i){"use strict";var n=i("e281"),a=i.n(n);a.a},"687b":function(e,t,i){var n=i("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-cfde43fc]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-cfde43fc]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-cfde43fc]{position:fixed;left:0;right:0;z-index:998}uni-page-body[data-v-cfde43fc]{overflow:auto!important}.menu_item[data-v-cfde43fc]{background-color:#fff;padding:%?25?% %?30?% 0}.menu_item .menu_title[data-v-cfde43fc]{font-size:%?32?%;font-weight:700;margin-bottom:%?10?%}.menu_item .menu_title .line[data-v-cfde43fc]{display:inline-block;height:%?28?%;width:%?4?%;border-radius:%?4?%}.menu_item .menu_content .grid_item[data-v-cfde43fc]{text-align:center}.menu_item .menu_content .grid_item .image[data-v-cfde43fc]{width:%?50?%;height:%?50?%;min-height:%?50?%}.menu_item .menu_content .grid_item .text[data-v-cfde43fc]{margin-top:%?16?%;color:#303133}',""]),e.exports=t},9106:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){return n}));var n={uniGrid:i("f2a2").default,uniGridItem:i("2f32").default,nsEmpty:i("63ed").default,diyBottomNav:i("0e00").default,loadingCover:i("59c1").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",[e.handleMenu.length?e._l(e.handleMenu,(function(t,n){return i("v-uni-view",{key:n,staticClass:"menu_item"},[i("v-uni-view",{staticClass:"menu_title"},[i("v-uni-text",{staticClass:"line color-base-bg margin-right"}),e._v(e._s(t.title))],1),i("v-uni-view",{staticClass:"menu_content"},[i("uni-grid",{attrs:{column:4,showBorder:!1}},e._l(t.menu,(function(t,n){return i("uni-grid-item",{key:n},[i("v-uni-view",{staticClass:"grid_item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.$util.redirectTo(t.page)}}},[i("v-uni-image",{staticClass:"image",attrs:{src:e.$util.img(t.img),mode:"aspectFit"}}),i("v-uni-view",{staticClass:"text"},[e._v(e._s(t.title))])],1)],1)})),1)],1)],1)})):i("ns-empty"),i("diy-bottom-nav",{attrs:{"link-index":2}}),i("loading-cover",{ref:"loadingCover"})],2)},s=[]},"96e2":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa");var n={name:"diy-bottom-nav",props:{linkIndex:{type:Number,default:function(){return 0}}},data:function(){return{currentRoute:"",jumpFlag:!0,list:[{link:"/pages/order/list",selectedIconPath:"/static/images/tabbar/order_selected.png",iconPath:"/static/images/tabbar/order.png",text:"订单"},{link:"/pages/goods/list",selectedIconPath:"/static/images/tabbar/goods_selected.png",iconPath:"/static/images/tabbar/goods.png",text:"商品"},{link:"/pages/index/index",selectedIconPath:"/static/images/tabbar/shop_selected.png",iconPath:"/static/images/tabbar/shop.png",text:"店铺"},{link:"/pages/member/list",selectedIconPath:"/static/images/tabbar/member_selected.png",iconPath:"/static/images/tabbar/member.png",text:"会员"},{link:"/pages/my/index",selectedIconPath:"/static/images/tabbar/my_selected.png",iconPath:"/static/images/tabbar/my.png",text:"我的"}]}},methods:{redirectTo:function(e){var t=this;this.jumpFlag&&(this.jumpFlag=!1,setTimeout((function(){t.jumpFlag=!0}),300),this.$util.redirectTo(e))}}};t.default=n},b722:function(e,t,i){"use strict";i.r(t);var n=i("96e2"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=a.a},e281:function(e,t,i){var n=i("1ddc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("967d").default;a("b5371374",n,!0,{sourceMap:!1,shadowMode:!1})}}]);