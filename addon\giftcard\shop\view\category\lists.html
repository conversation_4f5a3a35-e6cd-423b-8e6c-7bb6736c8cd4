<style>
    .len-short{width: 70px !important;}
</style>

<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加分组</button>
</div>
<div class="screen layui-collapse" lay-filter="category_tab">
    <div class="layui-colla-item">
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">分组名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="search_text" placeholder="请输入分组名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="form-row">
                <button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab table-tab" lay-filter="category_tab">
    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="list" lay-filter="list"></table>
    </div>
</div>

<!-- 编辑排序 -->
<script type="text/html" id="editSort">
    <input name="sort" type="number" onchange="modifySort({{d.category_id}}, this)" value="{{d.sort}}" class="layui-input edit-sort len-short">
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script>

<script>
    var table ,form, laytpl,layer_label, element;
    var repeat_flag = false; //防重复标识

    layui.use(['form', 'laytpl','element',], function () {
        form = layui.form;
        laytpl = layui.laytpl;
        form.render();

        table = new Table({
            elem: '#list',
            url: ns.url("giftcard://shop/category/lists"),
            cols: [
                [{
                    field: 'category_name',
                    title: '分组名称',
                    unresize: 'false',
                    width: '15%',
                }, {
                    field: 'font_color',
                    unresize: 'false',
                    title: '分组颜色',
                    templet: function (data) {
                        return '<span style="color:'+data.font_color+'">'+data.font_color+'</span>'
                    },
                }, {
                    field: 'sort',
                    unresize: 'false',
                    title: '排序',
                    templet: '#editSort',
                    sort : true,
                    width: '15%',
                }, {
                    field: 'create_time',
                    title: '创建时间',
                    unresize: 'false',
                    templet: function (data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
                    align : 'right'
                }]
            ]
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function (obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit':
                    location.hash = ns.hash("giftcard://shop/category/edit", {'category_id': data.category_id});
                    break;
                case 'delete':
                    del(data.category_id);
                    break;
            }
        });

        /**
         * 监听搜索
         */
        form.on('submit(search)', function(data){
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
            return false;
        });

        function del(id){
            if (repeat_flag) return false;
            repeat_flag = true;
            layer.confirm('您正在执行删除操作,请谨慎处理', {
                btn: ['确定','取消'] //按钮
                ,cancel: function(index, layero){
                    repeat_flag = false;
					layer.close(index);
                }
            },function (index) {
				layer.close(index);
                $.ajax({
                    url: ns.url("giftcard://shop/category/delete"),
                    data: {
                        category_id: id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function(index){
                repeat_flag = false;
				layer.close(index);
            });
        }
    });

    // 监听单元格编辑
    function modifySort(id, event) {
        var data = $(event).val();
        if (!new RegExp("^-?[1-9]\\d*$").test(data)) {
            layer.msg("排序号只能是整数");
            return;
        }
        if(data<0){
            layer.msg("排序号必须大于0");
            return ;
        }
        $.ajax({
            type: 'POST',
            url: ns.url("giftcard://shop/category/modifySort"),
            data: {
                sort: data,
                category_id: id
            },
            dataType: 'JSON',
            success: function(res) {
                layer.msg(res.message);
                if (res.code == 0) {
                    table.reload();
                }
            }
        });
    }

    function add() {
        location.hash = ns.hash("giftcard://shop/category/add");
    }

</script>
