<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com

 * =========================================================
 */

namespace addon\cashier\event;

use app\Controller;

/**
 * 收银交班打印
 */
class PrinterHtml extends Controller
{

    public function handle($data)
    {
        return $this->fetch('addon/cashier/shop/view/printer/printer_template.html');
    }
}