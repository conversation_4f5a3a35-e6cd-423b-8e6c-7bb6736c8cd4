<?php /*a:7:{s:55:"E:\aicode\shop\addon\cashier\shop\view\order\lists.html";i:1741057080;s:44:"app/shop/view/order/order_common_action.html";i:1741057066;s:43:"app/shop/view/order/order_adjust_price.html";i:1741057066;s:37:"app/shop/view/order/order_action.html";i:1741057066;s:45:"app/shop/view/order/order_address_update.html";i:1741057066;s:55:"app/shop/view/storeorder/store_order_take_delivery.html";i:1741057066;s:43:"app/shop/view/order/shop_active_refund.html";i:1741057066;}*/ ?>
<link rel="stylesheet" href="http://**********/app/shop/view/public/css/order_list.css"/>
<style>
    .layui-tab-content .order-operation-btn{display: flex;align-items: center;margin-top: 10px}
    .layui-tab-content .order-operation-all-btn{width: 4%;text-align: center; }
    .layui-tab-content .btn-box{ width: 96%;}
    .layui-tab-content .order-operation-btn .btn-box span{padding: 0px 5px;font-size: 12px;line-height: 2;height: auto;display: inline-block; border: 1px solid #C9C9C9;cursor: pointer;}
    .sub-selected-checkbox{display: flex;justify-content: center;align-items: center;}
    .layui-layout-admin .layui-table-cell{height: 32px; line-height: 32px;}
    .layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
</style>

<div class="screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <form class="layui-colla-content layui-form layui-show"  lay-filter="order_list">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">搜索方式</label>
                    <div class="layui-input-inline">
                        <select name="order_label" >
                            <?php foreach($order_label_list as $k => $label_val): ?>
                            <option value="<?php echo htmlentities($k); ?>"><?php echo htmlentities($label_val); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="layui-form-mid">&nbsp;</div>
                    <div class="layui-input-inline">
                        <input type="text" name="search" autocomplete="off" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">来源门店</label>
                    <div class="layui-input-inline">
                        <select name="store_id">
                            <option value="">请选择来源门店</option>
                            <?php foreach($store_list as $item): ?>
                            <option value="<?php echo htmlentities($item['store_id']); ?>"><?php echo htmlentities($item['store_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">订单来源</label>
                    <div class="layui-input-inline">
                        <select name="order_from">
                            <option value="">全部</option>
                            <?php foreach($order_from_list as $order_from_k => $order_from_v): ?>
                            <option value="<?php echo htmlentities($order_from_k); ?>"><?php echo htmlentities($order_from_v['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">付款方式</label>
                    <div class="layui-input-inline">
                        <select name="pay_type" >
                            <option value="">全部</option>
                            <?php foreach($pay_type_list as $pay_type_k => $pay_type_v): ?>
                            <option value="<?php echo htmlentities($pay_type_k); ?>"><?php echo htmlentities($pay_type_v); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">订单类型</label>
                    <div class="layui-input-inline">
                        <select name="cashier_order_type" >
                            <option value="">全部</option>
                            <?php foreach($cashier_order_type_list as $cashier_order_type_k => $cashier_order_type_v): ?>
                            <option value="<?php echo htmlentities($cashier_order_type_k); ?>"><?php echo htmlentities($cashier_order_type_v); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">下单时间</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
                        <i class=" iconrili iconfont calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
                        <i class=" iconrili iconfont calendar"></i>
                    </div>
                    <button class="layui-btn layui-btn-primary date-picker-btn date-picker-btn-seven" onclick="datePick(7, this);return false;">近7天</button>
                    <button class="layui-btn layui-btn-primary date-picker-btn date-picker-btn-thirty" onclick="datePick(30, this);return false;">近30天</button>
                </div>
                <div class="layui-inline layui-hide" id="delivery_time_box">
                    <label class="layui-form-label">自提时间</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="delivery_start_time" placeholder="开始时间" id="delivery_start_time" readonly>
                        <i class=" iconrili iconfont calendar"></i>
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="delivery_end_time" placeholder="结束时间" id="delivery_end_time" readonly>
                        <i class=" iconrili iconfont calendar"></i>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <button class="layui-btn" lay-submit id="btn_search"lay-filter="btn_search">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button class="layui-btn layui-btn-primary" lay-submit lay-filter="batch_export_order_goods">导出订单商品</button>
                <button class="layui-btn layui-btn-primary" lay-submit lay-filter="batch_export_order" >导出订单</button>
                <a class="layui-btn layui-btn-primary" href="<?php echo href_url('shop/order/export'); ?>" target="_blank">查看导出记录</a>
            </div>
            <input type="hidden" name="order_scene" value="cashier">
            <input type="hidden" name="status"/>
            <input type="hidden" name="page"/>
        </form>
    </div>
</div>

<div class="layui-tab table-tab" lay-filter="order_tab">
    <div class="layui-tab-content">
        <!-- 列表 -->
        <div class="layui-form order-operation-btn all-selected-checkbox" style="display: none">
            <div class="order-operation-all-btn">
                <input type="checkbox" name="" lay-skin="primary" lay-filter="allCheckbox">
                <input type="hidden" name="allOrderId">
            </div>
            <div class="btn-box">
                <span class="btn-deliver">批量收货</span>
            </div>
        </div>
        <div id="order_list"></div>
    </div>
</div>

<div id="order_page"></div>
<div id="order_operation"></div>

<script type="text/javascript">
var laytpl;
var form;
var order_list = [];
var express_company_list = [];
var deliyer_list = [];
var printer_addon_is_exit = '<?php echo addon_is_exit("printer"); ?>';
var isTradeManaged = false; // 微信小程序是否已开通发货信息管理服务

function reloadList(){
    <?php if(!empty($order_detail)): ?>
		listenerHash(); // 刷新页面
		layer.closeAll();
	<?php else: ?>
		getOrderList();
	<?php endif; ?>
}
$(function () {
	// 获取物流公司
	$.ajax({
		type: "post",
		url: ns.url("shop/express/getShopExpressCompanyList"),
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				express_company_list = res.data;
			}
		}
	});

	// 获取配送员
	$.ajax({
		type: "post",
		url: ns.url("shop/local/getDeliverList"),
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				deliyer_list = res.data;
			}
		}
	});

	getOrderShippingIsTradeManaged();

});

//渲染模板引擎
layui.use(['laytpl','form'], function(){
    laytpl = layui.laytpl;
    form = layui.form;
	form.render();
    <?php if(!empty($order_detail)): ?> setOrderInfo([<?php echo json_encode($order_detail); ?>]);<?php endif; ?>
});

/**
** 设置订单信息
**/
function setOrderInfo(temp_order_list){
    var temp = {};
    temp_order_list.forEach(item => temp[item.order_id] = item);
    order_list = temp;
}

/**
 ** 获取订单信息
 **/
function getOrderInfo(order_id){
    return order_list[order_id];
}

/**
 * 订单操作
 * @param fun
 * @param order_id
 */
function orderAction(fun, order_id){
    eval(fun+"("+order_id+")");
}

// 打印发货单
function printDeliverOrder(order_id) {
	var url = ns.url("shop/printer/batchprintorder", {request_mode: 'download',order_id: order_id});
	var LODOP = getLodop();
	if (LODOP) {
		LODOP.PRINT_INIT("发货单打印");
		LODOP.ADD_PRINT_TBURL(5, 10, "770", "95%", url);
		LODOP.SET_PRINT_STYLEA(0, "HOrient", 3);
		LODOP.SET_PRINT_STYLEA(0, "VOrient", 3);
		LODOP.ADD_PRINT_TEXT(590, 680, 130, 22, "页号：第#页/共&页");
		LODOP.SET_PRINT_STYLEA(0, "ItemType", 2);
		LODOP.SET_PRINT_STYLEA(0, "Horient", 1);
		LODOP.SET_PRINT_STYLEA(0, "Vorient", 1);
		LODOP.SET_SHOW_MODE("MESSAGE_GETING_URL", ""); //该语句隐藏进度条或修改提示信息
		LODOP.PREVIEW(); //预览
	}
}

// 订单备注
function orderRemark(order_id){
    var order_info = getOrderInfo(order_id);
    layer.prompt({
        formType: 2,
        value: order_info.remark,
        title: '卖家备注',
        area: ['400px', '100px'], //自定义文本域宽高
        yes: function(index, layero){
            var value = layero.find(".layui-layer-input").val();
            if(value.trim().length > 200){
                layer.msg("备注太长，最多200个字符！");
                return false;
            }
            $.ajax({
                type: "post",
                url: ns.url("shop/order/orderRemark"),
                async: true,
                dataType: 'json',
                data: {order_id : order_id, remark : value},
                success: function (res) {
                    layer.msg(res.message);
                    if(res.code == 0){
                        layer.close(layer.index - 1);
                        reloadList();
                    }
                }
            })
        }
    });

}

// 关闭订单
var closeRepeat = false;
function orderClose(order_id){
    var temp_index = layer.confirm('确定要关闭该订单吗?', function(index) {
        if (closeRepeat) return;
        closeRepeat = true;
		layer.close(index);
        $.ajax({
            url: ns.url("shop/order/close"),
            data: {order_id : order_id},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);
                if(res.code == 0){
                    layer.close(layer.index - 1);
                    reloadList();
                }
				closeRepeat = false;
            }
        });
    }, function () {
        layer.close();
        closeRepeat = false;
    });
}

/**
* 线下支付
* @param order_id
*/
var payRepeat = false;
function offlinePay(order_id){
    var order_info = getOrderInfo(order_id);
    ns.openOperateIframe({
        url:ns.url("offlinepay://shop/pay/pay", {out_trade_no: order_info.out_trade_no,member_id:order_info.member_id}),
        title:'线下支付',
        area:['700px', '500px'],
        getResFunc:'paySubmit',
        success:function (res){
            layer.msg(res.message);
            if(res.code == 0){
                reloadList();
            }
            payRepeat = false;
        }
    })
}

//线下支付审核
function offlinePayAudit(order_id){
    var order_info = getOrderInfo(order_id);
    window.open(ns.href('offlinepay://shop/pay/lists', {out_trade_no:order_info.out_trade_no}));
}

/**
 * 删除订单
 * @param order_id
 */
function orderDelete(order_id){
    layer.confirm('确定要删除该订单吗?', function(index) {
		layer.close(index);
    	$.ajax({
            url: ns.url("shop/order/delete"),
            data: {order_id : order_id},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);
                if(res.code == 0){
                    reloadList();
                }
            }
        });
    }, function () {
        layer.close();
    });
}

/**
 * 确认收货
 * @param order_id
 * @param type
 */
function takeDelivery(order_id, type = 0){
    var html = "";
    if(type == 0){
        html = '确保买家已经收到您的商品，并且与买家协商完毕提前确认收货?';
    }else{
        html = '确保买家已经收到您的商品，并且与买家协商完毕提前确认收货?（退款中的订单及虚拟订单无法确认收货）';
    }
    layer.confirm(html, function(index) {
		layer.close(index);
    	$.ajax({
            url: ns.url("shop/order/takeDelivery"),
            data: {order_id : order_id, type : type},
            dataType: 'JSON',
            type: 'POST',
            success: function(res) {
                layer.msg(res.message);
                if(res.code == 0){
                    reloadList();
                }
            }
        });
    }, function () {
        layer.close();
    });
}

// 打印订单小票
function printTicket(order_id){
	$.ajax({
		type: 'post',
		dataType: 'json',
		url: ns.url("shop/order/printTicket"),
		data: {order_id},
		success: function (res) {
			if (res.code != 0) {
				layer.msg(res.message ? res.message : '小票打印失败');
			}
		}
	});
}

// 查询小程序是否已开通发货信息管理服务
function getOrderShippingIsTradeManaged() {
	$.ajax({
		type: "post",
		url: ns.url("shop/order/orderShippingIsTradeManaged"),
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				isTradeManaged = res.data;
			}
		}
	});
}

</script>
<!-- 修改订单价格 -->
<!-- 调整价格模态 -->
<script type="text/html" id="adjust_price_html">
    <div style="padding:10px;">
        <div class="layui-form adjust-price-html" id='adjust_price'lay-filter="adjust_price">
            <div style="color: #666;">注意 : 只有订单未付款时才支持改价,改价后请联系买家刷新订单核实订单金额后再支付。</div>
            <table class="layui-table">
                <colgroup>
                    <col width="10%">
                    <col width="4%">
                    <col width="6%">
                    <col width="4%">
                    <col width="9%">
                    <col width="4%">
                    <col width="8%">
                    <col width="8%">
                    <col width="9%">
                    <col width="10%">
                    <col width="10%">
                    <col width="10%">
                    <col width="6%">
                </colgroup>
                <thead>
                    <tr>
                        <th>商品信息</th>
                        <th>单价</th>
                        <th>数量</th>
                        <th>小计</th>
                        <th>商品总额</th>
                        <th>优惠</th>
                        <th>优惠券</th>
                        <th>积分抵现</th>
                        <th>发票费用</th>
                        <th>发票邮寄费用</th>
                        <th>调整金额</th>
                        <th>运费</th>
                        <th>总计</th>
                    </tr>
                </thead>
                <tbody>
                    {{# layui.each(d.order_goods, function(index, item){ }}
                    <tr data-order_money="{{ d.order_money }}"data-adjust_money="{{ d.adjust_money }}"data-delivery_money="{{ d.delivery_money }}"
                        data-promotion_money="{{ d.promotion_money }}" data-coupon_money="{{ d.coupon_money }}" data-goods_money="{{ d.goods_money }}"
                        data-adjust_money="{{ d.adjust_money }}"data-delivery_money="{{ d.delivery_money }}" data-invoice_rate="{{ d.invoice_rate }}"
                        data-invoice_delivery_money="{{ d.invoice_delivery_money }}"  data-is_invoice="{{ d.is_invoice }}" data-point_money="{{ d.point_money }}" >
                        <td>{{ item.sku_name }}</td>
                        <td>{{ item.price }}</td>
                        <td>{{ item.num }}</td>
                        <td>{{ item.goods_money }}</td>
                        {{#  if(index == 0){ }}
                        <td rowspan="{{ d.order_goods.length }}">{{ d.goods_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}">{{ d.promotion_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}">{{ d.coupon_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}">{{ d.point_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}" class="adjust-invoice-money">{{ d.invoice_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}" class="adjust-invoice-delivery-money">{{ d.invoice_delivery_money }}</td>
                        <td rowspan="{{ d.order_goods.length }}"><input type="number" name="adjust_money" min="{{ d.goods_money - d.promotion_money - d.coupon_money }}" class="layui-input adjust-money" onchange="adjustChange(this);" value="{{ d.adjust_money }}"/></td>
                        <td rowspan="{{ d.order_goods.length }}"><input type="number"  name="delivery_money" class="layui-input delivery-money" onchange="adjustChange(this);" value="{{ d.delivery_money }}"/></td>
                        <td rowspan="{{ d.order_goods.length }}" class="adjust-pay-money">{{ d.order_money }}</td>
                        {{#  } }}
                    </tr>
                    {{#  }); }}
                </tbody>
            </table>
            <div style="color: #666;">
                <p><a class="text-color">实际商品金额</a> = 商品总额 - 优惠金额 - 优惠券金额 - 积分抵现 + 调价</p>
                <p><a class="text-color">发票费用</a> = 实际商品金额 * 发票比率</p>
                <p>订单总额 = <a class="text-color">实际商品金额</a> + <a class="text-color">发票费用</a> + 运费 +  发票邮寄费用</p>
            </div>

            <input type="hidden" name="order_id" value="{{ d.order_id }}"/>
            <button class="layui-btn"  lay-submit id="submit_price" lay-filter="submit_price" style="display:none;">保存</button>
        </div>
    </div>
</script>

<script>
    var form;
    // 订单调价
    function orderAdjustMoney(order_id) {
        var order_info = getOrderInfo(order_id);
        var getTpl = $("#adjust_price_html").html();
        laytpl(getTpl).render(order_info, function (html) {
            layer.open({
                type: 1,
                shadeClose: true,
                shade: 0.3,
                offset: 'auto',
                scrollbar: true,
                fixed: false,
                title: "调整价格",
                area: ['1250px', 'auto'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    $("#submit_price").click();
                },
                btn2: function (index, layero) {
                    layer.close(index);
                },
                content: html,
                success: function (layero, index) {
                    var repeat_flag = false;//防重复标识
                    form.render();
                    form.on('submit(submit_price)', function (data) {
                        if (repeat_flag) return;
                        repeat_flag = true;

                        $.ajax({
                            url: ns.url("shop/order/adjustPrice"),
                            type: "POST",
                            dataType: "JSON",
                            async: false,
                            data: data.field,
                            success: function (res) {
                                layer.msg(res.message);
                                if (res.code == 0) {
                                    layer.close(layer.index - 1);
                                    reloadList();
                                } else {
                                    repeat_flag = false;
                                }

                            }
                        });
                        return false;
                    });
                }
            });
            form.render();
        });
    }

    function adjustChange(obj){
		var adjust_money = 0;
		var delivery_money = 0;
        var parent_obj = $(obj).parent().parent();
        var o_order_money = parent_obj.attr("data-order_money");
        var o_adjust_money = parent_obj.attr("data-adjust_money");
        var o_delivery_money = parent_obj.attr("data-delivery_money");
        var invoice_delivery_money = parent_obj.attr("data-invoice_delivery_money");
        var promotion_money = parent_obj.attr("data-promotion_money");
        var coupon_money = parent_obj.attr("data-coupon_money");
        var goods_money = parent_obj.attr("data-goods_money");
        var is_invoice = parent_obj.attr("data-is_invoice");
        var point_money = parent_obj.attr("data-point_money");
		$(".adjust-money").each(function(){
		adjust_money += parseFloat($(this).val());
		});
		$(".delivery-money").each(function(){
		delivery_money += parseFloat($(this).val());
		});
        var real_goods_money = parseFloat(goods_money) - parseFloat(promotion_money) - parseFloat(coupon_money) + parseFloat(adjust_money) - parseFloat(point_money);
        var invoice_rate = is_invoice == 1 ? parent_obj.attr("data-invoice_rate") : 0;
        var invoice_money = Math.round(parseFloat(real_goods_money) * parseFloat(invoice_rate)/100 * 100) / 100;
        var total_money = parseFloat(goods_money) - parseFloat(promotion_money) - parseFloat(coupon_money) - parseFloat(point_money) + parseFloat(adjust_money) + parseFloat(invoice_delivery_money) + parseFloat(invoice_money) + parseFloat(delivery_money)
        total_money = Math.round(total_money * 100) / 100;
        $(obj).parent().parent().find(".adjust-invoice-money").text(invoice_money);
        // $(obj).parent().parent().find(".adjust-invoice-delivery-money").text(total_money);
        // var total_money = parseFloat(o_order_money) - parseFloat(o_adjust_money) - parseFloat(o_delivery_money) + parseFloat(adjust_money) + parseFloat(delivery_money);
        $(".adjust-pay-money").html(total_money);
    }
</script>
<!-- 修改订单收货地址 -->
<!-- 修改订单收货地址 -->
<!--<script type="text/javascript" src="<?php echo htmlentities($http_type); ?>://webapi.amap.com/maps?v=1.4.6&amp;key=2df5711d4e2fd9ecd1622b5a53fc6b1d"></script>-->
<!--<script type="text/javascript" src="http://**********/public/static/js/map_address.js"></script>-->
<script type="text/javascript" src="http://**********/app/shop/view/public/js/address.js"></script>
<script src="https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=<?php echo htmlentities($tencent_map_key); ?>"></script>
<script src="https://map.qq.com/api/js?v=2.exp&key=<?php echo htmlentities($tencent_map_key); ?>"></script>
<script src="https://mapapi.qq.com/jsapi_v2/2/4/148/main.js"></script>
<script type="text/javascript" src="http://**********/public/static/js/qq_map.js?time=20240601"></script>
<style>
    .update-address-html .order-map{width:876px;height:380px;}
</style>

<!-- 修改收货地址模态 -->
<div id="update_address_box" class="update-address-box"></div>
<script type="text/html" id="update_address_html">

<div class="layui-form update-address-html" id='update_address'lay-filter="update_address">
    <input type="hidden" name="order_id" value="{{ d.order_id }}"/>
    <!--自提点地址-->
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">* </span>收货地址</label>
        <div class="layui-input-inline area-select">
            <select name="province_id" lay-filter="province_id" lay-verify="province_id">
                <option value="">请选择省份</option>
            </select>
        </div>
        <div class="layui-input-inline area-select">
            <select name="city_id"  lay-filter="city_id" lay-verify="city_id">
                <option value="">请选择城市</option>
            </select>
        </div>
        <div class="layui-input-inline area-select">
            <select name="district_id"  lay-filter="district_id" lay-verify="district_id">
                <option value="">请选择区/县</option>
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"></label>
        <div class="layui-input-inline" >
            <input type="text" name="address"  placeholder="请填写具体地址。" lay-verify="required" autocomplete="off" class="layui-input address-content len-long" value="{{# if(d.address != undefined){}}{{ d.address}}{{#  } }}">
            {{# if(d.order_type == 3){ }}
            <input type = "hidden" name="longitude" lay-verify="required" class="layui-input" value="{{# if(d.longitude != undefined){}}{{ d.longitude}}{{#  } }}"/>
            <input type = "hidden" name="latitude" lay-verify="required" class="layui-input" value="{{# if(d.latitude != undefined){}}{{ d.latitude}}{{#  } }}"/>
            {{# } }}
        </div>
        {{# if(d.order_type == 3){ }}
        <button class='layui-btn-primary layui-btn' onclick="refreshFrom();">查找地址</button>
        {{# } }}
    </div>
    {{# if(d.order_type == 3){ }}
    <!--地图定位-->
    <div class="layui-form-item">
        <label class="layui-form-label">地图定位</label>
        <div class="layui-input-block special-length">
            <div id="container" class="order-map"></div>
        </div>
    </div>
    {{# } }}
    <!--联系人方式-->
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">收货人</label>
            <div class="layui-input-block">
                <input type="text" name="name" lay-verify="required" placeholder="请填写收货联系人" autocomplete="off" class="layui-input selffetch-input len-mid" value="{{# if(d.name != undefined){}}{{ d.name}}{{#  } }}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">手机号码</label>
            <div class="layui-input-block">
                <input type="text" name="mobile" lay-verify="mobile" placeholder="请填写手机号码" autocomplete="off" class="layui-input selffetch-input len-mid" value="{{# if(d.mobile != undefined){}}{{d.mobile }}{{#  } }}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">固定号码</label>
            <div class="layui-input-block">
                <input type="text" name="telephone" placeholder="请填写固定号码" autocomplete="off" class="layui-input selffetch-input len-mid" value="{{# if(d.telephone != undefined){}}{{d.telephone }}{{#  } }}">
            </div>
        </div>
    </div>
    <button class="layui-btn"  lay-submit id="submit_address" lay-filter="submit_address" style="display:none;">保存</button>
</div>
</script>
<script>
var map_class, form,latlng;
// 订单地址修改
function orderAddressUpdate(order_id) {
    var order_info = getOrderInfo(order_id);
    var getTpl = $("#update_address_html").html();
    var order_data = order_info;
    laytpl(getTpl).render(order_data, function(html) {
        layer_index = layer.open({
            type: 1,
            shadeClose: true,
            shade: 0.3,
            offset: 'auto',
            scrollbar: true,
            fixed: false,
            title: "编辑收货地址",
            area: ['1200px'],
            btn: ['确定', '取消'],
            yes: function(index, layero){
                $("#submit_address").click();
            },
            btn2: function(index, layero){
                layer.close(index);
            },
            content:  html,
            success: function(layero, index){
                form.render();
                //初始化省级地址
                getAreaList(0, 1);
                var repeat_flag = false;//防重复标识
                form.render();
                var initdata = {province_id : order_data.province_id, city_id : order_data.city_id, district_id : order_data.district_id};
                initAddress(initdata, "update_address");

                if(order_data.order_type == 3) {
                    if ($.isEmptyObject(order_data) == true) {
                        latlng = {lat: '', lng: ''};
                    } else {
                        latlng = {lat: order_data.latitude, lng: order_data.longitude};
                    }

                    if($("#container").length) {
                        setTimeout(function () {
                            map_class = new mapClass("container", latlng);
                        },200);
                    }
                }
                form.render();
                form.verify({
                    mobile:function (value){
                        if(!ns.getRegexp('mobile').test(value)){
                            return '请输入正确的手机号';
                        }
                    }
                })
                form.on('submit(submit_address)', function(data){

                    if(data.field.province_id == ''){
                        layer.msg('请选择省份', {icon: 5, anim: 6});
                        return;
                    }
                    if(data.field.city_id == ''){
                        layer.msg('请选择城市', {icon: 5, anim: 6});
                        return;
                    }
                    if(data.field.district_id == ''){
                        layer.msg('请选择区/县', {icon: 5, anim: 6});
                        return;
                    }
                    if(data.field.address == ''){
                        layer.msg('请输入详细地址', {icon: 5, anim: 6});
                        return;
                    }
                    //外卖订单修改地址必须选坐标
                    if(order_data.order_type == 3){
                        if(data.field.latitude == '' || data.field.longitude == ''  ){
                            layer.msg('外卖订单必须选择地图坐标', {icon: 5, anim: 6});
                            return;
                        }
                    }
                    var province_name = $("option[value='" + data.field.province_id + "']").text();
                    var city_name = $("option[value='" + data.field.city_id + "']").text();
                    var district_name = $("option[value='" + data.field.district_id + "']").text();
                    // var community_name = $("option[value='" + data.field.community_id + "']").text();
                    data.field.province_name = province_name;
                    data.field.city_name = city_name;
                    data.field.district_name = district_name;
                    // data.field.community_name = community_name;
                    data.field.full_address = province_name + '-' + city_name + '-' + district_name;

                    if(repeat_flag)return;
                    repeat_flag = true;
                    $.ajax({
                        url: ns.url("shop/order/editaddress"),
                        type: "POST",
                        dataType: "JSON",
                        async: false,
                        data: data.field,
                        success: function (res) {
                            layer.msg(res.message);
                            if(res.code == 0){
                                layer.close(layer_index);
                                reloadList();
                            }else{
                                repeat_flag = false;
                            }
                        }
                    });
                    return false;
                });
            }
        });
        form.render();
    });

}

/**
 * 重新渲染表单
 */
function refreshFrom(){
    form.render();
    orderAddressChange();//改变地址
    map_class.mapChange();
}

//动态改变订单地址赋值
function orderAddressChange(){
    map_class.address.province = $("select[name=province_id]").val();
    map_class.address.province_name = $("select[name=province_id] option:selected").text();
    map_class.address.city = $("select[name=city_id]").val();
    map_class.address.city_name = $("select[name=city_id] option:selected").text();
    map_class.address.district = $("select[name=district_id]").val();
    map_class.address.district_name = $("select[name=district_id] option:selected").text();
    // map_class.address.township = $("select[name=community_id]").val();
    // map_class.address.township_name = $("select[name=community_id] option:selected").text();
    map_class.address.detail_address = $("input[name=address]").val()
}

/**
 * 地址下拉框（主要用于坐标记录）
 */
function selectCallBack(){
    $("input[name=longitude]").val(map_class.address.longitude);//坐标
    $("input[name=latitude]").val(map_class.address.latitude);//坐标
    $("input[name=address]").val(map_class.address.address);//详细地址
}

//地图点击回调时间
function mapChangeCallBack(){
    $("input[name=address]").val(map_class.address.address);//详细地址
    $("input[name=longitude]").val(map_class.address.longitude);//坐标
    $("input[name=latitude]").val(map_class.address.latitude);//坐标
    $.ajax({
        type : "post",
        url : ns.url("shop/address/getGeographicId"),
        dataType: 'json',
        async : true,
        data : {
            "address" : map_class.address.area
        },
        success : function(data) {
            map_class.address.province = data.province_id;
            map_class.address.city = data.city_id;
            map_class.address.district = data.district_id;
            // map_class.address.township = data.community_id;
            map_class.map_change = false;
            form.val("update_address", {
                "province_id": data.province_id // "name": "value"
            });
            $("select[name=province_id]").change();
            form.val("update_address", {
                "city_id": data.city_id // "name": "value"
            });
            $("select[name=city_id]").change();
            form.val("update_address", {
                "district_id": data.district_id // "name": "value"
            });
            $("select[name=district_id]").change();
            // form.val("update_address", {
            //     "community_id": data.community_id // "name": "value"
            // });
            refreshFrom();//重新渲染form
            map_class.map_change = true;
        }
    })
}
</script>

<!-- 门店自提  提货 -->
<script>
    /**
     * 直接提货
     * @param order_id
     */
    function storeOrderTakedelivery(order_id){
        layer.confirm('确定要直接提货吗?', {title:'提示'},function(index) {
			layer.close(index);
            $.ajax({
                url: ns.url("shop/storeorder/storeOrderTakedelivery"),
                data: {order_id : order_id},
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.msg(res.message);
                    if(res.code == 0){
                        reloadList();
                    }
                }
            });
        }, function () {
            layer.close();
        });
    }
</script>
<!-- 主动退款 -->
<style>
    .refund-view-list{font-size:14px;line-height:20px;color:#323233;color:var(--theme-stroke-1,#323233)}
    .refund-view-item {margin-bottom: 10px;}
    .refund-view-item-label{width:75px; vertical-align: top;}
    .refund-view-item-content{display:inline-block}
    .refund-view-list .word-aux{margin-left:74px;}
</style>
<!-- 店铺主动退款 -->
<script type="text/html" id="refund_transfer_html">
    <div style="padding:10px;">
        <div class="layui-form refund-transfer-html" id='refund_transfer'lay-filter="refund_transfer">
            <div class="refund-view-list">
                <div class="refund-view-item">
                    <label class="refund-view-item-label">退款金额：</label>
                    <div class="refund-view-item-content">
                        <span class="refund-money">￥{{ d.order_goods_info.refund_apply_money }}</span>
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">主动退款：</label>
                    <div class="refund-view-item-content">
                        <input type='number' class="layui-input" name="shop_active_refund_money" value="{{d.order_goods_info.refund_apply_money}}" placeholder="0.00">
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">完成状态：</label>
                    <div class="refund-view-item-content">
                        <input type="radio" title="部分退款状态" checked name="refund_status" value="PARTIAL_REFUND">
                        <input type="radio" title="退款完成状态"  name="refund_status" value="REFUND_COMPLETE">
                    </div>
                    <div class="word-aux">
                        <div>1、如果是退部分金额，退款后可以是部分退款状态或退款完成状态</div>
                        <div>2、如果是退全部金额，则退款后一定是退款完成状态</div>
                        <div>3、退款完成才会执行相关业务如核销码失效，卡包失效等操作</div>
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">退款方式：</label>
                    <div class="refund-view-item-content">
                        <input type="radio" title="原路退款" checked name="shop_active_refund_money_type" value="1">
                        <input type="radio" title="线下退款"  name="shop_active_refund_money_type" value="2">
                        <input type="radio" title="退款到余额"  name="shop_active_refund_money_type" value="3">
                    </div>
                </div>
                <div class="refund-view-item">
                    <label class="refund-view-item-label">退款说明：</label>
                    <div class="refund-view-item-content">
                        <textarea name="shop_active_refund_remark" class="layui-textarea len-long" maxlength="150"></textarea>
                    </div>
                </div>

            </div>
            <input type="hidden" name="order_goods_id" value="{{ d.order_goods_info.order_goods_id }}"/>
            <button class="layui-btn"  lay-submit id="submit_transfer" lay-filter="submit_transfer" style="display:none;">保存</button>
        </div>
    </div>
</script>

<script>
    var laytpl,form,active_refund_layer;
    layui.use(['laytpl','form'], function(){
        laytpl = layui.laytpl;
        form = layui.form;
        form.render();
    });

    // 主动退款
    function shopActiveRefund(order_goods_id) {
        $.ajax({
            url: ns.url("shop/orderrefund/getOrderGoodsRefundInfo"),
            type: "POST",
            dataType: "JSON",
            async: false,
            data: {order_goods_id: order_goods_id},
            success: function (res) {
                if (res.code >= 0) {
                    var getTpl = $("#refund_transfer_html").html();
                    var refund_data = res.data;
                    laytpl(getTpl).render(refund_data, function (html) {
                        active_refund_layer = layer.open({
                            type: 1,
                            shadeClose: true,
                            shade: 0.3,
                            offset: 'auto',
                            scrollbar: true,
                            fixed: false,
                            title: "店铺主动退款",
                            area: ['700px', 'auto'],
                            btn: ['确认退款', '取消'],
                            yes: function (index, layero) {
                                $("#submit_transfer").click();
                            },
                            btn2: function (index, layero) {
                                layer.close(index);
                            },
                            content: html,
                            success: function (layero, index) {
                                var repeat_flag = false;//防重复标识
                                form.render();

                                form.on('submit(submit_transfer)', function (data) {
                                    if(!ns.getRegexp('>=0float2').test(data.field.shop_active_refund_money)){
                                        layer.msg('请输入正确的退款金额，最多保留两位小数');
                                        return;
                                    }
                                    if(Number(data.field.shop_active_refund_money) > Number(refund_data.order_goods_info.refund_apply_money)){
                                        layer.msg('主动退款金额不能大于可退款总额');
                                        return;
                                    }
                                    if (repeat_flag) return;
                                    repeat_flag = true;
                                    $.ajax({
                                        url: ns.url("shop/orderrefund/shopActiveRefund"),
                                        type: "POST",
                                        dataType: "JSON",
                                        data: data.field,
                                        beforeSend: function () {layer_index = layer.load();},
                                        complete: function () {layer.close(layer_index);},
                                        success: function (res) {
                                            layer.msg(res.message);
                                            if (res.code == 0) {
                                                layer.close(active_refund_layer);
                                                reloadList();
                                            } else {
                                                repeat_flag = false;
                                            }

                                        }
                                    });
                                    return false;
                                });
                            }
                        });
                        form.render();
                    });
                } else {
                    layer.msg(res.message);
                }
            }
        });

    }
</script>
<script src="http://**********/addon/cashier/shop/view/public/js/order_list.js?time=20241121"></script>
<script>
    var laypage,element, form;
    var is_refresh = false;
    var laypage_util;
    var orderIdAll = [];
    var order_type_status_json = <?php echo json_encode($order_type_list); ?>;

    // 通过hash获取页数
    function getHashPage(){
        var page = 1;
        var startTime = '';
        var endTime = '';
        var hash_arr = getHashArr();
        $.each(hash_arr,function(index, itemobj){
            var item_arr = itemobj.split("=");
            if(item_arr.length == 2){
                switch(item_arr[0]){
                    case "page":
                        page = item_arr[1];
                        break;
                    case "start_time":
                        startTime = ns.date_to_time(item_arr[1].split("%")[0]);
                        break;
                    case "end_time":
                        endTime = ns.date_to_time(item_arr[1].split("%")[0]);
                        break;
                }
            }
        });

        var _time = (endTime - startTime) / (24 * 60 * 60);
        if (_time == 6) {
            $(".date-picker-btn-seven").addClass("selected");
            $(".date-picker-btn-thirty").removeClass("selected");
        } else if (_time == 29) {
            $(".date-picker-btn-thirty").addClass("selected");
            $(".date-picker-btn-seven").removeClass("selected");
        } else {
            $(".date-picker-btn-seven").removeClass("selected");
            $(".date-picker-btn-thirty").removeClass("selected");
        }

        return page;
    }

    //从hash中获取数据
    function getHashData() {
        var hash_arr = getHashArr();
        var form_json = {
            "end_time": "",
            "delivery_end_time": "",
            "order_from": "",
            "order_label": $("select[name=order_label]").val(),
            "order_name": "",
            "order_status": "",
            "promotion_type": "",
            "pay_type": "",
            "search": "",
            "start_time": "",
            "delivery_start_time": "",
            "order_type": 'all',
            'page_size': '',
            "page": "",
            "store_id": $("select[name=store_id]").val(),
            'cashier_order_type': ''
        };
        if (hash_arr.length > 0) {
            $.each(hash_arr, function (index, itemobj) {
                var item_arr = itemobj.split("=");
                if (item_arr.length == 2) {
                    $.each(form_json, function (key, form_val) {
                        if (item_arr[0] == key) {
                            form_json[key] = item_arr[1];
                        }
                    })
                }
            })
        }

        resetOrderStatus(form_json.order_type, 2);
        form.val("order_list", form_json);
        switchOrderType(form_json.order_type);
        setOrderStatusTab(form_json.order_status);
        return form_json;
    }

    /**
     * 获取哈希值order_type
     */
    function getHashOrderType(){
        var hash_arr = getHashArr();
        var order_type = "all";
        if(hash_arr.length > 0){
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    if(item_arr[0].indexOf("order_type") != "-1") {
                        order_type = item_arr[1];
                    }
                }
            })
        }
        return order_type;
    }

    layui.use(['laypage','laydate','form', 'element'], function(){
        form = layui.form;
        laypage = layui.laypage;
        element = layui.element;
        var laydate = layui.laydate;
        form.render();

        //渲染时间
        laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

        laydate.render({
            elem: '#delivery_start_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

        laydate.render({
            elem: '#delivery_end_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

        //监听筛选事件
        form.on('submit(btn_search)', function(data){
            is_refresh = true;
            data.field.page = 1;
            setHashOrderList(data.field);
            setOrderStatusTab(data.field.order_status);
            return false;
        });

        //批量导出（订单项）
        form.on('submit(batch_export_order_goods)', function(data){
            data.field.order_ids = orderIdAll.toString(); // 选择要导出的订单
            $.ajax({
                type: 'post',
                dataType: 'json',
                url: ns.url("shop/order/exportordergoods"),
                data: data.field,
                success: function (res) {
                }
            });
            window.open(ns.href("shop/order/export",{}));
            return false;
        });

        //批量导出（订单）
        form.on('submit(batch_export_order)', function(data){
            data.field.order_ids = orderIdAll.toString(); // 选择要导出的订单
            $.ajax({
                type: 'post',
                dataType: 'json',
                url: ns.url("shop/order/exportorder"),
                data: data.field,
                success: function (res) {

                }
            });
            window.open(ns.href("shop/order/export",{}));
            return false;
        });

        //订单类型
        form.on('select(order_type)', function(data){
            switchOrderType(data.value);
            resetOrderStatus(data.value, 1);
            return false;
        });

        //监听Tab切换，以改变地址hash值
        element.on('tab(order_tab)', function(){
            $(".all-selected-checkbox input").prop("checked",false);
            var status = this.getAttribute('lay-id');
            form.val("order_list", {"order_status":status});

            var hash_data = getHashList();
            hash_data.order_status = status;
            hash_data.page = 1;
            setHashOrderList(hash_data);
        });
        getHashData();
        getOrderList();//筛选
    });

    function switchOrderType(value){
        if(value == 2 || value == 3){
            var time_type = value == 2 ? '要求自提时间：': '要求送达时间：';
            $("#delivery_time_box").removeClass('layui-hide').find('label').text(time_type);
        }else{
            $("#delivery_time_box").addClass('layui-hide');
        }
    }

    function setOrderStatusTab(order_status){
        $(".layui-tab-title li").removeClass("layui-this");
        $(".layui-tab-title li").each(function(){
            var status = $(this).attr("lay-id");
            if(status == order_status){
                $(this).addClass("layui-this")
            }
        });
    }
    //重置状态tab 选项卡
    function resetOrderStatus(order_type, is_tab){
        var hash_order_type = getHashOrderType();
        if(hash_order_type != order_type || is_refresh == false){
            if(is_tab != 1 || is_refresh == false) {
                $(".layui-tab-title li").not(':first').remove();
                $(".layui-tab-title li").find(":first").addClass("layui-this");
            }
            if(is_tab != 2 || is_refresh == false){
                $("select[name=order_status] option").not(':first').remove();
            }
            var status_item = order_type_status_json[order_type]["status"];
            $.each(status_item,function(index, itemobj){
                if(is_tab != 1 || is_refresh == false) {
                    $(".layui-tab-title").append('<li lay-id="' + index + '">' + itemobj + '</li>');
                }
                if(is_tab != 2 || is_refresh == false) {
                    $("select[name=order_status]").append('<option value="' + index + '">' + itemobj + '</option>');
                }
            });
            form.render('select');
        }
    }

    //哈希值 订单数据
    function setHashOrderList(data){
        localStorage.setItem('formSubmit','search'); // 表单搜索标识，防止页面重新加载
        var hash = ['url=cashier://shop/order/lists'];
        for (var key in data) {
            if (data[key] != '' && data[key] != 'all') {
                hash.push(`${key}=${data[key]}`)
            }
        }
        location.hash = hash.join('&');
        getOrderList();
    }

    function getHashList(){
        var hash_arr = getHashArr();
        var form_json = {
            "end_time" : "",
            "delivery_end_time" : "",
            "order_from" : "",
            "order_label" : $("select[name=order_label]").val(),
            "order_name" : "",
            "order_status" : "",
            "promotion_type" : "",
            "pay_type" : "",
            "search" : "",
            "start_time" : "",
            "delivery_start_time" : "",
            "order_type" : 'all',
            'page_size':'',
            "page" : "",
            "store_id" : $("select[name=store_id]").val(),
            'cashier_order_type':''
        };
        if(hash_arr.length > 0){
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    $.each(form_json,function(key, form_val){
                        if(item_arr[0].indexOf(key) != "-1"){
                            form_json[key] = item_arr[1];
                        }
                    })
                }
            })
        }

        return form_json;
    }

    var order = new Order();
    function getOrderList(){
        var url = ns.url("cashier://shop/order/lists", getHashArr().join('&'));

        $.ajax({
            type : 'get',
            dataType: 'json',
            url :url,
            success : function(res){
                if(res.code == 0){
                    setOrderInfo(res.data.list);
                    order.setData(res.data);
                    $("#order_list").html(order.fetch());

                    if(res.data.order_status == 3){
                        $(".order-operation-btn.all-selected-checkbox").show();
                    } else{
                        $(".order-operation-btn.all-selected-checkbox").hide();
                    }

                    form.render();
                    //批量选择
                    form.on('checkbox(allCheckbox)', function(data){
                        $(".sub-selected-checkbox input").prop("checked",data.elem.checked);
                        $(".all-selected-checkbox input").prop("checked",data.elem.checked);

                        $(".sub-selected-checkbox input").each(function(index,item){
                            if($(item).attr('disabled') == 'disabled'){
                                $(item).prop("checked",false);
                            }
                        });
                        form.render("checkbox");
                        getOrderId();
                    });

                    //全选选择
                    form.on('checkbox(subCheckbox)', function(data){
                        var subLen = $(".sub-selected-checkbox input:checked").length;
                        $(".all-selected-checkbox input").prop("checked",false);
                        if (subLen == 10){
                            $(".all-selected-checkbox input").prop("checked",true);
                        }

                        $(".sub-selected-checkbox input").each(function(index,item){
                            if($(item).attr('disabled') == 'disabled'){
                                $(item).prop("checked",false);
                            }
                        });

                        form.render("checkbox");
                        getOrderId();
                    });

                    //获取选中的id
                    function getOrderId(){
                        var lists = $(".sub-selected-checkbox input:checked");
                        orderIdAll = [];
                        lists.each(function(index,item){
                            if($(item).parents(".sub-selected-checkbox").attr("disabled") != "disabled"){
                                orderIdAll.push(JSON.parse($(item).parents(".sub-selected-checkbox").attr("data-id")));
                            }
                        });
                    }
                    laypage_util = new Page({
                        elem: 'order_page',
                        count: res.data.count,
                        curr: getHashPage(),
                        limit:getHashData()['page_size'] || 10,
                        callback: function(obj){
                            var hash_data = getHashData();
                            hash_data.page = obj.curr;
                            hash_data.page_size = obj.limit;
                            setHashOrderList(hash_data);
                        }
                    });

                }else{
                    layer.msg(res.message);
                }
            }
        });
    }

    /**
     * 七天时间
     */
    function datePick(date_num,event_obj){
        $(".date-picker-btn").removeClass("selected");
        $(event_obj).addClass('selected');
        Date.prototype.Format = function (fmt,date_num) { //author: meizz
            this.setDate(this.getDate()-date_num);
            var o = {
                "M+": this.getMonth() + 1, //月份
                "d+": this.getDate(), //日
                "H+": this.getHours(), //小时
                "m+": this.getMinutes(), //分
                "s+": this.getSeconds(), //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        };
        var now_time =  new Date().Format("yyyy-MM-dd 23:59:59",0);//当前日期
        var before_time =  new Date().Format("yyyy-MM-dd 00:00:00",date_num-1);//前几天日期
        $("input[name=start_time]").val(before_time,0);
        $("input[name=end_time]").val(now_time,date_num-1);
    }

    //批量收货
    $("body").off("click",".layui-tab-content .btn-deliver").on("click",".layui-tab-content .btn-deliver", function () {
        var subLen = $(".sub-selected-checkbox input:checked").length;
        if (subLen <=0 ) {
            layer.msg("请选择订单");
            return false;
        }
        takeDelivery(orderIdAll.toString(), 1);
    });
</script>
