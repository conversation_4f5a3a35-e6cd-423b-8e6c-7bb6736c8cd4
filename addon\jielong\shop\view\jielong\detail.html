<link rel="stylesheet" href="STATIC_CSS/promotion_detail.css">

<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
        <span class="card-title">基本信息</span>
	</div>
	<div class="layui-card-body">
		<div class="promotion-view">
			<div class="promotion-view-item">
				<label>活动名称：</label>
				<span>{$info.jielong_name}</span>
			</div>
			<div class="promotion-view-item">
				<label>活动状态：</label>
				<span>{$info.status_name}</span>
			</div>
			<div class="promotion-view-item">
				<label>开始时间：</label>
				<span>{:date('Y-m-d H:i:s',$info.start_time)}</span>
			</div>
			<div class="promotion-view-item">
				<label>结束时间：</label>
				<span>{:date('Y-m-d H:i:s',$info.end_time)}</span>
			</div>

			<div class="promotion-view-item">
				<label>添加时间：</label>
				<span>{:date('Y-m-d H:i:s',$info.create_time)}</span>
			</div>
		</div>
		<div class="promotion-view">
			<div class="promotion-view-item-line">
				<label class="promotion-view-item-custom-label">活动说明：</label>
				<div class="promotion-view-item-custom-box">{$info.desc}</div>
			</div>
		</div>
	</div>
</div>

<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
        <span class="card-title">活动商品</span>
	</div>
	<div class="layui-card-body">
		<div class='promotion-view-list'>
			<table id="promotion_list"></table>
		</div>
	</div>
</div>

<script type='text/html' id="promotion_list_item_box_html">
	<div class="promotion-list-item-title">
		<div class="promotion-list-item-title-icon">
			<img src="{{ ns.img(d.goods_image) }}" alt="">
		</div>
		<p class="promotion-list-item-title-name multi-line-hiding">{{ d.goods_name }}</p>
	</div>
</script>
<script>
	var promotion_list = {:json_encode($info.sku_list, JSON_UNESCAPED_UNICODE)};
	layui.use('table', function() {
		new Table({
			elem: '#promotion_list',
			cols: [
                [{
                    field: 'sku_name',
                    title: '商品名称',
                    width: '30%',
                    unresize: 'false',
                    templet: '#promotion_list_item_box_html'
                }, {
                    field: 'price',
                    title: '商品价格',
                    unresize: 'false',
                    templet: function(data) {
                        return '￥' + data.price;
                    }
                }, {
                    field: 'stock',
                    title: '商品库存',
                    unresize: 'false',
                    templet: function(data) {
                        return data.goods_stock;
                    }
                }]
			],
			data: promotion_list
		});
	});

</script>