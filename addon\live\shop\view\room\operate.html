<style type="text/css">
.goods-empty {padding: 100px 0;text-align: center;}
.goods-info {padding: 5px 0;align-items: center;flex-wrap:unset!important;}
.goods-info .room-name {padding-left: 5px;line-height: 1}
.goods-info img {width:50px;height: 50px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">直播间信息</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">直播间：</label>
				<div class="layui-input-block">
					<p>{$room_info.name}</p>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">直播时间：</label>
				<div class="layui-input-block">
					<p>{:time_to_date($room_info.start_time)} - {:time_to_date($room_info.end_time)}</p>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">列表展示</span>
		</div>
		<div class="layui-card-body">
			<div class="layui-form-item">
				<label class="layui-form-label">主播头像：</label>
				<div class="layui-input-block">
					<div class="upload-img-block">
						<div class="upload-img-box {notempty name="$room_info['anchor_img']"}hover{/notempty}">
							<div class="upload-default" id="anchorImgUpload">
							{if condition="$room_info.anchor_img"}
							<div id="preview_imgUpload" class="preview_img">
								<img layer-src src="{:img($room_info.anchor_img)}" class="img_prev"/>
							</div>
							{else/}
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
							{/if}
							</div>
							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete" ></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="anchor_img" value="{if condition="$room_info.anchor_img"}{$room_info.anchor_img}{/if}"/>
						</div>
						<!-- <p id="anchorImgUpload" class=" {if condition='$room_info.anchor_img'} replace {else/} no-replace{/if}">替换</p>
						<i class="del {if condition="$room_info.anchor_img"}show{/if}">x</i> -->
					</div>
				</div>
				<div class="word-aux">
					<p>在直播列表中展示。建议尺寸：100像素 * 100像素，图片大小不得超过1M。</p>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>直播间横幅：</label>
				<div class="layui-input-block">
					<div class="upload-img-block">
						<div class="upload-img-box {notempty name="$room_info['banner']"}hover{/notempty}" >
							<div class="upload-default" id="bannerUpload">
							{if condition="$room_info.banner"}
							<div id="preview_imgUpload" class="preview_img">
								<img layer-src src="{:img($room_info.banner)}" class="img_prev"/>
							</div>
							{else/}
								<i class="iconfont iconshangchuan"></i>
								<p>点击上传</p>
							{/if}
							</div>
							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="banner" value="{if condition="$room_info.banner"}{$room_info.banner}{/if}"/>
						</div>
						<!-- <p  class="{if condition='$room_info.banner'} replace {else/} no-replace{/if}">替换</p>
						
						<i class="del {if condition="$room_info.banner"}show{/if}">x</i> -->
					</div>
				</div>
				<div class="word-aux">
					<p>在直播列表中展示。建议尺寸：702像素 * 208像素，图片大小不得超过2M。</p>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">商品货架</span>
		</div>
		<div class="layui-card-body">
			{notempty name="$room_info['goods']"}
				{if $room_info['live_status'] == '102'}
				<div class="single-filter-box">
					<button class="layui-btn" onclick="importGoods()">从商品库导入商品</button>
				</div>
				{/if}
				<table class="layui-table" lay-skin="nob">
				  	<colgroup>
				    	<col width="10%">
				    	<col width="45">
				    	<col width="15">
				    	<col width="30">
				    	<col>
				  	</colgroup>
				  	<thead>
			    		<tr>
				      		<th>序号</th>
				      		<th>商品信息</th>
				      		<th>价格</th>
				      		<th>商品链接</th>
			    		</tr> 
				  	</thead>
				  	<tbody>
				  		{foreach name="$room_info['goods']" item="vo" index="k"}
				  			{php}
				  				preg_match("/(pages\/goods\/detail\?sku_id=)(\d*)$/", $vo['url'], $matches);
				  			{/php}
				  			{if isset($matches[2])}
				  			<tr data-sku="{$matches[2]}">
				  			{else/}
				  			<tr>
				  			{/if}
						      	<td>{$k}</td>
						      	<td>
						      		<div class="table-btn goods-info">
										<img src="{:img($vo.cover_img)}">
										<span class="room-name" title="{$vo.name}">{$vo.name}</span>
									</div>
						      	</td>
						      	<td>{:sprintf("%.2f", $vo.price)}</td>
						      	<td>{$vo.url}</td>
						    </tr>
				  		{/foreach}
				  	</tbody>
				</table>
			{else/}
				{if $room_info['live_status'] == '102'}
				<div class="goods-empty">暂无商品<a href="javascript:;" class="text-color" onclick="importGoods()">从商品库中导入</a></div>
				{else/}
				<div class="goods-empty">暂无商品</div>
				{/if}
			{/notempty}
		</div>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form'], function() {

		var anchor_upload = new Upload({
			elem: '#anchorImgUpload',
			callback:function (res) {
				if (res.code >= 0) {
					$.ajax({
						type: 'POST',
						dataType: 'JSON',
						url: ns.url("live://shop/room/operate"),
						data: {
							id: {$room_info.id},
							anchor_img: res.data.pic_path
						},
						success: function(res) {}
					});
				}
			},
			deleteCallback:function () {
				anchor_upload.delete();
				$.ajax({
					type: 'POST',
					dataType: 'JSON',
					url: ns.url("live://shop/room/operate"),
					data: {
						id: {$room_info.id},
						anchor_img: ''
					},
					success: function(res) {
						layer.msg(res.message);
					}
				});
			}
		});

		var banner_upload = new Upload({
			elem: '#bannerUpload',
			callback:function (res) {
				if (res.code >= 0) {
					$.ajax({
						type: 'POST',
						dataType: 'JSON',
						url: ns.url("live://shop/room/operate"),
						data: {
							id: {$room_info.id},
							banner: res.data.pic_path
						},
						success: function(res) {}
					});
				}
			},
			deleteCallback:function () {
				banner_upload.delete();
				$.ajax({
					type: 'POST',
					dataType: 'JSON',
					url: ns.url("live://shop/room/operate"),
					data: {
						id: {$room_info.id},
						banner: ''
					},
					success: function(res) {
						layer.msg(res.message);
					}
				});
			}
		});

	});

	function importGoods() {
		var sku_id = [];
		if ($('[data-sku]').length) {
			$('[data-sku]').each(function (el) {
				sku_id.push($(this).attr('data-sku'));
			})
		}

		layer.open({
			type: 2,
			title: '从商品库导入商品',
			content: ns.url("live://shop/room/getGoodsPageList", {request_mode: 'iframe', ids: sku_id.toString()}),
			area: ['600px', '550px'],
			btn: ['确定', '取消'],
			yes: function (index, layero) {
				form.render();
				var iframeWin = document.getElementById(layero.find('iframe')[0]['name']).contentWindow;//得到iframe页的窗口对象，执行iframe页的方法：
				iframeWin.liveSelectGoodsCallbackListener(function (data) {
					if (data.length == 0) {
						layer.msg('请选择要添加的商品', {icon: 5, shift: 6});
						return;
					}
					$.ajax({
						type: 'POST',
						url: ns.url("live://shop/room/addGoods"),
						data: {
							room_id: {$room_info.roomid},
							data: JSON.stringify(data)
						},
						dataType: 'JSON',
						success: function (res) {
							layer.msg(res.msg);
							if (res.code == 0) {
								layer.closeAll();
								listenerHash(); // 刷新页面
							}
						}
					});
				})
			}
		})
	}
</script>
