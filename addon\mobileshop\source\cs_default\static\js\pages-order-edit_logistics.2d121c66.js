(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-edit_logistics"],{"075c":function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getExpressCompanyList=function(){return i.default.get("/shopapi/express/expressCompany")},t.getExpressTemplateList=function(){return i.default.get("/shopapi/express/getExpressTemplateList")};var i=a(r("9027"))},"283d":function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("bf0f"),r("2797"),r("aa9c");var i=a(r("2634")),n=a(r("2fdc")),o=r("6638"),d=r("075c"),s={data:function(){return{order:{},repeatFlag:!1,data:{order_id:0,package_id:0,delivery_type:1,express_company_id:0,delivery_no:""},expressCompany:[],company_name:"",picker:{index:0,arr:[]},logistics:{}}},onLoad:function(e){var t=this;return(0,n.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.logistics=uni.getStorageSync("editLogistics")?JSON.parse(uni.getStorageSync("editLogistics")):{},!t.logistics.id){e.next=13;break}return t.data.order_id=t.logistics.order_id,t.data.package_id=t.logistics.id,t.data.delivery_type=t.logistics.delivery_type,t.data.delivery_no=t.logistics.delivery_no,t.data.express_company_id=t.logistics.express_company_id,t.company_name=t.logistics.express_company_name,t.getShopExpressCompanyList(),e.next=11,t.getOrderInfo();case 11:e.next=15;break;case 13:t.$util.showToast({title:"订单信息不存在"}),setTimeout((function(){uni.navigateBack({delta:1})}),1e3);case 15:case"end":return e.stop()}}),e)})))()},onShow:function(){},methods:{getOrderInfo:function(){var e=this;return(0,n.default)((0,i.default)().mark((function t(){var r;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,o.getOrderInfoById)(e.data.order_id);case 2:r=t.sent,0==r.code?(e.order=r.data,e.$refs.loadingCover&&e.$refs.loadingCover.hide()):(e.$util.showToast({title:r.message}),setTimeout((function(){e.$util.redirectTo("/pages/order/list",{},"redirectTo")}),1e3));case 4:case"end":return t.stop()}}),t)})))()},getShopExpressCompanyList:function(){var e=this;(0,d.getExpressCompanyList)().then((function(t){0==t.code&&(e.expressCompany=t.data,e.expressCompany.forEach((function(t,r){e.express_company_id==t.company_id&&(e.picker.index=r),e.picker.arr.push(t.company_name)})))}))},bindPickerChange:function(e){0!=this.expressCompany.length&&(this.picker.index=e.target.value,this.data.express_company_id=this.expressCompany[this.picker.index].company_id,this.company_name=this.expressCompany[this.picker.index].company_name)},verify:function(){if(1==this.data.delivery_type){if(0==this.data.express_company_id)return this.$util.showToast({title:"请选择物流公司"}),!1;if(0==this.data.delivery_no.length)return this.$util.showToast({title:"请输入快递单号"}),!1}return!0},save:function(){var e=this;this.verify()&&(0==this.data.delivery_type&&(this.data.express_company_id=0,this.data.delivery_no=""),this.repeatFlag||(this.repeatFlag=!0,(0,o.editOrderDelivery)(this.data).then((function(t){0==t.code?(uni.removeStorageSync("editLogistics"),setTimeout((function(){uni.navigateBack({delta:1})}),1e3)):e.repeatFlag=!1,e.$util.showToast({title:t.message})}))))}}};t.default=s},"4a2d":function(e,t,r){var a=r("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-1b913ef1]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-1b913ef1]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-1b913ef1]{position:fixed;left:0;right:0;z-index:998}.form-title[data-v-1b913ef1]{margin:%?20?% %?30?%;color:#909399}.item-wrap[data-v-1b913ef1]{background:#fff;margin-top:%?20?%}.item-wrap .form-wrap[data-v-1b913ef1]{display:flex;align-items:center;margin:0 %?30?%;border-bottom:1px solid #eee;height:%?100?%;line-height:%?100?%}.item-wrap .form-wrap[data-v-1b913ef1]:last-child{border-bottom:none}.item-wrap .form-wrap .label[data-v-1b913ef1]{vertical-align:middle;margin-right:%?30?%}.item-wrap .form-wrap.delivery-way .label[data-v-1b913ef1]{flex:1}.item-wrap .form-wrap.delivery-way uni-button[data-v-1b913ef1]:last-child{margin-left:%?20?%!important}.item-wrap .form-wrap .value[data-v-1b913ef1]{vertical-align:middle;display:inline-block;flex:1;text-align:right;overflow:hidden;text-overflow:ellipsis;white-space:pre}.item-wrap .form-wrap uni-input[data-v-1b913ef1]{vertical-align:middle;display:inline-block;flex:1;text-align:right}.item-wrap .form-wrap.more-wrap .selected[data-v-1b913ef1]{vertical-align:middle;display:inline-block;flex:1;text-align:right;color:#909399;overflow:hidden;white-space:pre;text-overflow:ellipsis}.item-wrap .form-wrap.more-wrap .selected.have[data-v-1b913ef1]{color:#303133}.item-wrap .form-wrap.more-wrap .iconfont[data-v-1b913ef1]{color:#909399;margin-left:%?20?%}.footer-wrap[data-v-1b913ef1]{width:100%;padding:%?40?% 0}',""]),e.exports=t},"50a6":function(e,t,r){"use strict";r.r(t);var a=r("abd3"),i=r("f530");for(var n in i)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(n);r("84e7");var o=r("828b"),d=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"1b913ef1",null,!1,a["a"],void 0);t["default"]=d.exports},6638:function(e,t,r){"use strict";r("6a54");var a=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.adjustOrderPrice=function(e){return i.default.post("/shopapi/order/adjustPrice",{data:e})},t.closeOrder=function(e){return i.default.post("/shopapi/order/close",{data:{order_id:e}})},t.deliveryOrder=function(e){return i.default.post("/shopapi/order/delivery",{data:e})},t.editOrderDelivery=function(e){return i.default.post("/shopapi/order/editOrderDelivery",{data:e})},t.editOrderInvoicelist=function(e){return i.default.post("/shopapi/order/invoiceEdit",{data:e})},t.getOrderCondition=function(){return i.default.get("/shopapi/order/condition")},t.getOrderDetailById=function(e){return i.default.post("/shopapi/order/getOrderDetail",{data:{order_id:e}})},t.getOrderDetailInfoById=function(e){return i.default.post("/shopapi/order/detail",{data:{order_id:e}})},t.getOrderGoodsList=function(e){return i.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:e}})},t.getOrderInfoById=function(e){return i.default.post("/shopapi/order/getOrderInfo",{data:{order_id:e}})},t.getOrderInvoicelist=function(e){return i.default.post("/shopapi/order/invoicelist",{data:e})},t.getOrderList=function(e){return i.default.post("/shopapi/order/lists",{data:e})},t.getOrderLog=function(e){return i.default.post("/shopapi/order/log",{data:{order_id:e}})},t.getOrderPackageList=function(e){return i.default.post("/shopapi/order/package",{data:{order_id:e}})},t.ordErtakeDelivery=function(e){return i.default.post("/shopapi/order/takeDelivery",{data:{order_id:e}})},t.orderExtendTakeDelivery=function(e){return i.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:e}})},t.orderLocalorderDelivery=function(e){return i.default.post("/shopapi/localorder/delivery",{data:e})},t.orderOfflinePay=function(e){return i.default.post("/shopapi/order/offlinePay",{data:{order_id:e}})},t.orderVirtualDelivery=function(e){return i.default.post("/shopapi/virtualorder/delivery",{data:{order_id:e}})},t.storeOrderTakeDelivery=function(e){return i.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:e}})};var i=a(r("9027"))},"84e7":function(e,t,r){"use strict";var a=r("b6c5"),i=r.n(a);i.a},abd3:function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return a}));var a={loadingCover:r("59c1").default},i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",[r("v-uni-view",{staticClass:"form-title"},[e._v("填写物流信息")]),r("v-uni-view",{staticClass:"item-wrap"},[r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("收货地址")]),r("v-uni-text",{staticClass:"value"},[e._v(e._s(e.order.full_address)+" "+e._s(e.order.address))])],1),r("v-uni-view",{staticClass:"form-wrap delivery-way"},[r("v-uni-text",{staticClass:"label"},[e._v("发货方式")]),r("v-uni-button",{attrs:{type:1==e.data.delivery_type?"primary":"default",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.data.delivery_type=1}}},[e._v("物流发货")]),r("v-uni-button",{attrs:{type:0==e.data.delivery_type?"primary":"default",size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.data.delivery_type=0}}},[e._v("无需物流")])],1),1==e.data.delivery_type?[r("v-uni-view",{staticClass:"form-wrap more-wrap"},[r("v-uni-text",{staticClass:"label"},[e._v("物流公司")]),r("v-uni-picker",{staticClass:"selected",attrs:{value:e.picker.index,range:e.picker.arr},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindPickerChange.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"uni-input",class:{"color-tip":!e.company_name}},[e._v(e._s(e.company_name?e.company_name:"请选择物流公司"))])],1),r("v-uni-text",{staticClass:"iconfont iconright"})],1),r("v-uni-view",{staticClass:"form-wrap"},[r("v-uni-view",{staticClass:"label"},[e._v("快递单号")]),r("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入快递单号"},model:{value:e.data.delivery_no,callback:function(t){e.$set(e.data,"delivery_no",t)},expression:"data.delivery_no"}})],1)]:e._e()],2),r("v-uni-view",{staticClass:"footer-wrap"},[r("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save()}}},[e._v("确定")])],1),r("loading-cover",{ref:"loadingCover"})],1)},n=[]},b6c5:function(e,t,r){var a=r("4a2d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=r("967d").default;i("3878202c",a,!0,{sourceMap:!1,shadowMode:!1})},f530:function(e,t,r){"use strict";r.r(t);var a=r("283d"),i=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=i.a}}]);