(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-verify-records"],{1186:function(e,t,a){"use strict";var i=a("7c27"),r=a.n(i);r.a},"204f":function(e,t,a){var i=a("b6b6");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("3895596c",i,!0,{sourceMap:!1,shadowMode:!1})},"3fe9":function(e,t,a){"use strict";a.r(t);var i=a("7281"),r=a("d191d");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("1186");var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"c9e45eec",null,!1,i["a"],void 0);t["default"]=o.exports},"5ba2":function(e,t,a){"use strict";a.r(t);var i=a("b303"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"62d3f":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".uni-tag[data-v-c9e45eec]{box-sizing:border-box;padding:0 %?32?%;height:%?60?%;line-height:calc(%?60?% - 2px);font-size:%?28?%;display:inline-flex;align-items:center;color:#333;border-radius:%?6?%;background-color:#f8f8f8;border:1px solid #f8f8f8}.uni-tag--circle[data-v-c9e45eec]{border-radius:%?30?%}.uni-tag--mark[data-v-c9e45eec]{border-radius:0 %?30?% %?30?% 0}.uni-tag--disabled[data-v-c9e45eec]{opacity:.5}.uni-tag--small[data-v-c9e45eec]{height:%?40?%;padding:0 %?16?%;line-height:calc(%?40?% - 2px);font-size:%?24?%}.uni-tag--primary[data-v-c9e45eec]{color:#fff;background-color:#007aff;border:1px solid #007aff}.uni-tag--primary.uni-tag--inverted[data-v-c9e45eec]{color:#007aff;background-color:#fff;border:1px solid #007aff}.uni-tag--success[data-v-c9e45eec]{color:#fff;background-color:#4cd964;border:1px solid #4cd964}.uni-tag--success.uni-tag--inverted[data-v-c9e45eec]{color:#4cd964;background-color:#fff;border:1px solid #4cd964}.uni-tag--warning[data-v-c9e45eec]{color:#fff;background-color:#f0ad4e;border:1px solid #f0ad4e}.uni-tag--warning.uni-tag--inverted[data-v-c9e45eec]{color:#f0ad4e;background-color:#fff;border:1px solid #f0ad4e}.uni-tag--error[data-v-c9e45eec]{color:#fff;background-color:#dd524d;border:1px solid #dd524d}.uni-tag--error.uni-tag--inverted[data-v-c9e45eec]{color:#dd524d;background-color:#fff;border:1px solid #dd524d}.uni-tag--inverted[data-v-c9e45eec]{color:#333;background-color:#fff;border:1px solid #f8f8f8}",""]),e.exports=t},"65d9":function(e,t,a){"use strict";a.r(t);var i=a("ddd33"),r=a("da74");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("732b");var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"dcc4de3c",null,!1,i["a"],void 0);t["default"]=o.exports},"67e7":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-7da4a131]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-7da4a131]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-7da4a131]{position:fixed;left:0;right:0;z-index:998}uni-page-body[data-v-7da4a131]{overflow:hidden}.search-wrap[data-v-7da4a131]{display:flex;justify-content:space-between;background-color:#fff;padding:%?20?% %?30?%}.search-wrap .search-input-inner[data-v-7da4a131]{flex:1;display:flex;align-items:center;height:%?70?%;padding:0 %?30?%;border-radius:%?100?%;box-sizing:border-box;border:1px solid #eee}.search-wrap .search-input-inner .search-input-icon[data-v-7da4a131]{margin-right:%?10?%;color:#909399}.search-wrap .search-input-inner .search-input-text[data-v-7da4a131]{flex:1}.search-wrap .screen[data-v-7da4a131]{line-height:%?70?%}.search-wrap .select[data-v-7da4a131]{height:%?66?%;line-height:%?66?%;padding:0 %?30?%;margin-left:%?30?%;border:1px solid #eee;border-radius:%?50?%;font-size:%?24?%}.search-wrap .select .iconfont[data-v-7da4a131]{margin-bottom:%?20?%}.list[data-v-7da4a131]{background-color:#fff;margin:0 %?30?% %?30?%;padding:0 %?30?% %?20?%;border-radius:%?10?%}.list .title[data-v-7da4a131]{display:flex;align-items:center;padding:%?20?% 0;font-size:%?24?%;border-bottom:1px solid #eee}.list .title .time[data-v-7da4a131]{flex:1;color:#909399}.list .title .status[data-v-7da4a131]{margin-left:%?20?%}.list .goods[data-v-7da4a131]{display:flex;padding:%?30?% 0}.list .goods .img[data-v-7da4a131]{height:%?140?%;width:%?140?%;min-width:%?140?%}.list .goods .info[data-v-7da4a131]{flex:1;margin-left:%?30?%;display:flex;flex-direction:column;justify-content:space-between}.list .goods .info .goods_name[data-v-7da4a131]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;line-height:1.5}.list .goods .info .flex[data-v-7da4a131]{margin-top:%?10?%;display:flex;justify-content:space-between;align-items:baseline}.list .goods .info .flex .flex_left[data-v-7da4a131]{font-size:%?24?%;color:#909399}.list .other_info[data-v-7da4a131]{color:#909399;font-size:%?24?%}.screen-wrap .title[data-v-7da4a131]{font-size:%?24?%;padding:%?20?%;background:#f8f8f8}.screen-wrap uni-scroll-view[data-v-7da4a131]{height:85%}.screen-wrap uni-scroll-view .item-wrap[data-v-7da4a131]{border-bottom:1px solid #eee}.screen-wrap uni-scroll-view .item-wrap[data-v-7da4a131]:last-child{border-bottom:none}.screen-wrap uni-scroll-view .item-wrap .label[data-v-7da4a131]{font-size:%?24?%;padding:%?20?% %?30?% 0 %?20?%;display:flex;justify-content:space-between;align-items:center}.screen-wrap uni-scroll-view .item-wrap .label .more[data-v-7da4a131]{font-size:%?24?%}.screen-wrap uni-scroll-view .item-wrap .label .more uni-picker[data-v-7da4a131]{display:inline-block;vertical-align:middle}.screen-wrap uni-scroll-view .item-wrap .label .more uni-picker uni-view[data-v-7da4a131]{font-size:%?24?%}.screen-wrap uni-scroll-view .item-wrap .label .more .iconfont[data-v-7da4a131]{display:inline-block;vertical-align:middle;color:#909399;font-size:%?28?%}.screen-wrap uni-scroll-view .item-wrap .label .uni-tag[data-v-7da4a131]{padding:0 %?20?%;font-size:%?22?%;background:#f8f8f8;height:%?40?%;line-height:%?40?%;border:0;margin-left:%?20?%}.screen-wrap uni-scroll-view .item-wrap .list[data-v-7da4a131]{margin:%?20?% %?30?%;overflow:hidden;padding:0}.screen-wrap uni-scroll-view .item-wrap .list .uni-tag[data-v-7da4a131]{padding:0 %?20?%;font-size:%?22?%;background:#f8f8f8;height:%?52?%;line-height:%?52?%;border:0;margin-right:%?20?%;margin-bottom:%?20?%}.screen-wrap uni-scroll-view .item-wrap .list .uni-tag[data-v-7da4a131]:nth-child(3n){margin-right:0}.screen-wrap uni-scroll-view .item-wrap .value-wrap[data-v-7da4a131]{display:flex;justify-content:center;align-items:center;padding:%?20?%}.screen-wrap uni-scroll-view .item-wrap .value-wrap .h-line[data-v-7da4a131]{width:%?40?%;height:%?2?%;background-color:#909399}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-input[data-v-7da4a131]{flex:1;background:#eee;height:%?60?%;line-height:%?60?%;font-size:%?22?%;border-radius:%?50?%;text-align:center}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-input[data-v-7da4a131]:first-child{margin-right:%?10?%}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-input[data-v-7da4a131]:last-child{margin-left:%?10?%}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-picker[data-v-7da4a131]{display:inline-block;vertical-align:middle}.screen-wrap uni-scroll-view .item-wrap .value-wrap uni-picker uni-view[data-v-7da4a131]{font-size:%?24?%}.screen-wrap .footer[data-v-7da4a131]{height:%?90?%;display:flex;justify-content:center;align-items:flex-start;bottom:0;width:100%}.screen-wrap .footer uni-button[data-v-7da4a131]{margin:0;width:40%}.screen-wrap .footer uni-button[data-v-7da4a131]:first-child{border-top-right-radius:0;border-bottom-right-radius:0}.screen-wrap .footer uni-button[data-v-7da4a131]:last-child{border-top-left-radius:0;border-bottom-left-radius:0}',""]),e.exports=t},7281:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.text?a("v-uni-view",{staticClass:"uni-tag",class:[!0===e.disabled||"true"===e.disabled?"uni-tag--disabled":"",!0===e.inverted||"true"===e.inverted?"uni-tag--inverted":"",!0===e.circle||"true"===e.circle?"uni-tag--circle":"",!0===e.mark||"true"===e.mark?"uni-tag--mark":"","uni-tag--"+e.size,"uni-tag--"+e.type],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick()}}},[e._v(e._s(e.text))]):e._e()},r=[]},7319:function(e,t,a){"use strict";a.r(t);var i=a("e3bc"),r=a("5ba2");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("8dab");var s=a("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"7da4a131",null,!1,i["a"],void 0);t["default"]=o.exports},"732b":function(e,t,a){"use strict";var i=a("204f"),r=a.n(i);r.a},"7c27":function(e,t,a){var i=a("62d3f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("92f7967a",i,!0,{sourceMap:!1,shadowMode:!1})},"7c4b":function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"UniTag",props:{type:{type:String,default:"default"},size:{type:String,default:"normal"},text:{type:String,default:""},disabled:{type:[String,Boolean],default:!1},inverted:{type:[String,Boolean],default:!1},circle:{type:[String,Boolean],default:!1},mark:{type:[String,Boolean],default:!1}},methods:{onClick:function(){!0!==this.disabled&&"true"!==this.disabled&&this.$emit("click")}}};t.default=i},"8dab":function(e,t,a){"use strict";var i=a("8dc5"),r=a.n(i);r.a},"8dc5":function(e,t,a){var i=a("67e7");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=a("967d").default;r("4826350b",i,!0,{sourceMap:!1,shadowMode:!1})},b303:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("dfe4")),n={mixins:[r.default]};t.default=n},b6b6:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".uni-drawer[data-v-dcc4de3c]{display:block;position:fixed;top:0;left:0;right:0;bottom:0;overflow:hidden;visibility:hidden;z-index:999;height:100%}.uni-drawer.uni-drawer--right .uni-drawer__content[data-v-dcc4de3c]{left:auto;right:0;-webkit-transform:translatex(100%);transform:translatex(100%)}.uni-drawer.uni-drawer--visible[data-v-dcc4de3c]{visibility:visible}.uni-drawer.uni-drawer--visible .uni-drawer__content[data-v-dcc4de3c]{-webkit-transform:translatex(0);transform:translatex(0)}.uni-drawer.uni-drawer--visible .uni-drawer__mask[data-v-dcc4de3c]{display:block;opacity:1}.uni-drawer__mask[data-v-dcc4de3c]{display:block;opacity:0;position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.4);transition:opacity .3s}.uni-drawer__content[data-v-dcc4de3c]{display:block;position:absolute;top:0;left:0;width:61.8%;height:100%;background:#fff;transition:all .3s ease-out;-webkit-transform:translatex(-100%);transform:translatex(-100%)}.safe-area[data-v-dcc4de3c]{padding-bottom:%?68?%;padding-top:%?44?%;box-sizing:border-box}",""]),e.exports=t},ca53:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"UniDrawer",props:{visible:{type:Boolean,default:!1},mode:{type:String,default:""},mask:{type:Boolean,default:!0}},data:function(){return{visibleSync:!1,showDrawer:!1,rightMode:!1,closeTimer:null,watchTimer:null,isIphoneX:!1}},watch:{visible:function(e){var t=this;clearTimeout(this.watchTimer),setTimeout((function(){t.showDrawer=e}),100),this.visibleSync&&clearTimeout(this.closeTimer),e?this.visibleSync=e:this.watchTimer=setTimeout((function(){t.visibleSync=e}),300)}},created:function(){var e=this;this.isIphoneX=this.$util.uniappIsIPhoneX(),this.visibleSync=this.visible,setTimeout((function(){e.showDrawer=e.visible}),100),this.rightMode="right"===this.mode},methods:{close:function(){var e=this;this.showDrawer=!1,this.closeTimer=setTimeout((function(){e.visibleSync=!1,e.$emit("close")}),200)},moveHandle:function(){}}};t.default=i},d191d:function(e,t,a){"use strict";a.r(t);var i=a("7c4b"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},da74:function(e,t,a){"use strict";a.r(t);var i=a("ca53"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},ddd33:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.visibleSync?a("v-uni-view",{staticClass:"uni-drawer",class:{"uni-drawer--visible":e.showDrawer,"uni-drawer--right":e.rightMode},on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),e.moveHandle.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-drawer__mask",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"uni-drawer__content",class:{"safe-area":e.isIphoneX}},[e._t("default")],2)],1):e._e()},r=[]},dfe4:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("c223"),a("aa9c");var i={data:function(){return{showScreen:!1,isShow:!1,formData:{verifier_name:"",start_time:"",end_time:"",verify_code:"",verify_type:""},recordsList:[],pickerCurr:0,picker:[{date_text:"全部",date_value:""},{date_text:"订单自提",date_value:"pickup"},{date_text:"虚拟商品",date_value:"virtualgoods"}],verifyType:[],verify_type:0}},onLoad:function(){this.getverifyType()},onShow:function(){this.$util.checkToken("/pages/verify/records")&&this.$refs.mescroll&&this.$refs.mescroll.refresh()},methods:{search:function(){this.$refs.mescroll.refresh()},pickerChange:function(e){this.pickerCurr=e.detail.value,this.formData.verify_type=e.detail.value,this.$refs.mescroll.refresh()},bindTimeStartChange:function(e){if(e.detail.value>=this.formData.end_time&&this.formData.end_time)return this.$util.showToast({title:"开始时间不能大于结束时间"}),!1;this.formData.start_time=e.detail.value},bindTimeEndChange:function(e){if(e.detail.value<=this.formData.start_time)return this.$util.showToast({title:"结束时间不能小于开始时间"}),!1;this.formData.end_time=e.detail.value},getListData:function(e){var t=this,a={page_size:e.size,page:e.num};Object.assign(a,this.formData),this.$api.sendRequest({url:"/shopapi/verify/records",data:a,success:function(a){var i=[],r=a.message;0==a.code&&a.data?i=a.data.list:t.$util.showToast({title:r}),e.endSuccess(i.length),1==e.num&&(t.recordsList=[]),t.recordsList=t.recordsList.concat(i),t.$refs.loadingCover&&t.$refs.loadingCover.hide(),t.isShow=!0}})},getverifyType:function(){var e=this;this.$api.sendRequest({url:"/shopapi/verify/verifyType",success:function(t){var a=[{date_value:"",date_name:"全部"}];for(var i in t.data)a.push({date_value:i,date_name:t.data[i].name});e.verifyType=a}})},uTag:function(e){this.verify_type=e,this.formData.verify_type=this.verifyType[this.verify_type].date_value},screenData:function(){this.formData;this.showScreen=!1,this.$refs.mescroll.refresh()},resetData:function(){this.formData.verifier_name="",this.formData.start_time="",this.formData.end_time="",this.formData.verify_code="",this.formData.verify_type="",this.verify_type=0},imgError:function(e,t){this.recordsList[e].item_array[t].img=this.$util.getDefaultImage().default_goods_img,this.$forceUpdate()}}};t.default=i},e3bc:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i}));var i={uniDrawer:a("65d9").default,uniTag:a("3fe9").default,nsEmpty:a("63ed").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"records"},[a("v-uni-view",{staticClass:"search-wrap"},[a("v-uni-view",{staticClass:"search-input-inner"},[a("v-uni-text",{staticClass:"search-input-icon iconfont iconsousuo",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search()}}}),a("v-uni-input",{staticClass:"search-input-text font-size-tag",attrs:{maxlength:"50",placeholder:"请输入核销人员名称"},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.search()}},model:{value:e.formData.verifier_name,callback:function(t){e.$set(e.formData,"verifier_name",t)},expression:"formData.verifier_name"}})],1),a("v-uni-view",{staticClass:"screen margin-left",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showScreen=!0}}},[e._v("筛选"),a("v-uni-text",{staticClass:"iconfont iconshaixuan color-tip"})],1)],1),a("uni-drawer",{staticClass:"screen-wrap",attrs:{visible:e.showScreen,mode:"right"},on:{close:function(t){arguments[0]=t=e.$handleEvent(t),e.showScreen=!1}}},[a("v-uni-view",{staticClass:"title color-tip"},[e._v("筛选")]),a("v-uni-scroll-view",{attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"label"},[e._v("核销人员名称")]),a("v-uni-view",{staticClass:"value-wrap"},[a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入核销人员名称"},model:{value:e.formData.verifier_name,callback:function(t){e.$set(e.formData,"verifier_name",t)},expression:"formData.verifier_name"}})],1)],1),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"label"},[e._v("核销类型")]),a("v-uni-view",{staticClass:"list"},[e._l(e.verifyType,(function(t,i){return[a("uni-tag",{key:i+"_0",attrs:{inverted:!0,text:t.date_name,type:"primary",type:i==e.verify_type?"primary":"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.uTag(i)}}})]}))],2)],1),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"label"},[e._v("核销时间")]),a("v-uni-view",{staticClass:"value-wrap"},[a("v-uni-picker",{staticClass:"picker margin-right",attrs:{mode:"date"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindTimeStartChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.formData.start_time?e.formData.start_time:"开始时间"))])],1),a("v-uni-view",{staticClass:"h-line"}),a("v-uni-picker",{staticClass:"picker margin-left",attrs:{mode:"date"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindTimeEndChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.formData.end_time?e.formData.end_time:"结束时间"))])],1)],1)],1),a("v-uni-view",{staticClass:"item-wrap"},[a("v-uni-view",{staticClass:"label"},[e._v("核销码")]),a("v-uni-view",{staticClass:"value-wrap"},[a("v-uni-input",{staticClass:"uni-input",attrs:{placeholder:"请输入核销码"},model:{value:e.formData.verify_code,callback:function(t){e.$set(e.formData,"verify_code",t)},expression:"formData.verify_code"}})],1)],1)],1),a("v-uni-view",{staticClass:"footer"},[a("v-uni-button",{attrs:{type:"default"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.resetData.apply(void 0,arguments)}}},[e._v("重置")]),a("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.screenData.apply(void 0,arguments)}}},[e._v("确定")])],1)],1),a("mescroll-uni",{ref:"mescroll",staticClass:"list-wrap",attrs:{top:"140",size:8},on:{getData:function(t){arguments[0]=t=e.$handleEvent(t),e.getListData.apply(void 0,arguments)}}},[a("template",{attrs:{slot:"list"},slot:"list"},[e.recordsList.length>0?e._l(e.recordsList,(function(t,i){return a("v-uni-view",{key:i,staticClass:"list"},[a("v-uni-view",{staticClass:"title"},[a("v-uni-text",{staticClass:"time"},[e._v("核销码："+e._s(t.verify_code)),a("v-uni-text",{staticClass:"color-base-text",staticStyle:{"margin-left":"10rpx"},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.$util.copy(t.verify_code)}}},[e._v("复制")])],1),a("v-uni-text",[e._v(e._s(t.verify_type_name))]),a("v-uni-text",{staticClass:"status"},[e._v(e._s(1==t.is_verify?"已核销":"尚未核销"))])],1),e._l(t.item_array,(function(t,r){return a("v-uni-view",{key:r,staticClass:"goods"},[a("v-uni-image",{staticClass:"img",attrs:{src:e.$util.img(t.img),mode:"aspectFit"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.imgError(i,r)}}}),a("v-uni-view",{staticClass:"info"},[a("v-uni-view",{staticClass:"goods_name"},[e._v(e._s(t.name))]),a("v-uni-view",{staticClass:"flex"},[a("v-uni-view",{staticClass:"flex_left"},[e._v("x"+e._s(t.num))]),a("v-uni-view",{staticClass:"flex_right"},[a("v-uni-text",{staticClass:"font-size-tag"},[e._v("￥")]),e._v(e._s(t.price))],1)],1)],1)],1)})),a("v-uni-view",{staticClass:"other_info"},[a("v-uni-text",{staticClass:"margin-right"},[e._v("核销员："+e._s(t.verifier_name))])],1),a("v-uni-view",{staticClass:"other_info"},[e._v("创建时间："+e._s(e.$util.timeStampTurnTime(t.create_time)))]),a("v-uni-view",{staticClass:"other_info"},[e._v("核销时间："+e._s(e.$util.timeStampTurnTime(t.verify_time)))])],2)})):e.isShow&&0==e.recordsList.length?a("ns-empty",{attrs:{text:"暂无核销数据"}}):e._e()],2)],2)],1)},n=[]}}]);