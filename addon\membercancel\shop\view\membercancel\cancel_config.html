<style>
	.layui-form-item .layui-form-checkbox[lay-skin=primary] {margin-top: 0;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label ">是否允许注销：</label>
		<div class="layui-input-block" id="isReg">
			<input type="checkbox" name="is_enable" value="1" lay-filter="is_enable" lay-skin="switch" {if condition="$value.is_enable == 1"} checked {/if} >
		</div>
		<div class="word-aux">设置为关闭，则无法注销会员账号</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label ">注销审核：</label>
		<div class="layui-input-block" >
			<input type="checkbox" name="is_audit" value="1"  lay-filter="is_enable" lay-skin="switch" {if condition="$value.is_audit== 1"} checked {/if} >
		</div>
		<div class="word-aux">设置为关闭，会员将直接注销成功。</div>
	</div>

	<div class="form-row">
		<button type="button" class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>
</div>

<script>
	layui.use('form', function() {
		var form = layui.form,
			repeat_flag = false; //防重复
		form.render();

		form.on('submit(save)', function(data) {
		    field = data.field;
			if (field.is_enable == undefined) {
				field.is_enable = 0;
			}

            if (field.is_audit == undefined) {
                field.is_audit = 0;
            }

			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("membercancel://shop/membercancel/cancelConfig"),
				data: field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					layer.msg(res.message);
					repeat_flag = false;
				}
			});
		});
		
	});
</script>
