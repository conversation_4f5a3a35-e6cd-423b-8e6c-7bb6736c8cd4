(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-invoices-invoices"],{"04d4":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={nsEmpty:i("63ed").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"search-wrap"},[i("v-uni-view",{staticClass:"search-input-inner"},[i("v-uni-text",{staticClass:"search-input-icon iconfont iconsousuo",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.searchGoods()}}}),i("v-uni-input",{staticClass:"uni-input font-size-tag",attrs:{maxlength:"50",placeholder:"请输入订单号"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.searchGoods()}},model:{value:t.search_text,callback:function(e){t.search_text=e},expression:"search_text"}})],1),i("v-uni-picker",{attrs:{value:t.indexse,range:t.array},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"select color-tip"},[t._v(t._s(t.array[t.indexse])),i("v-uni-text",{staticClass:"iconfont iconiconangledown",staticStyle:{transform:"scale(1.8)"}})],1)],1)],1),i("v-uni-view",{staticClass:"tab-block"},[i("v-uni-view",{staticClass:"tab-wrap"},[t._l(t.statusList,(function(e,a){return[i("v-uni-view",{key:a+"_0",staticClass:"tab-item",class:e.id==t.status?"active color-base-text color-base-bg-before":"",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.tabChange(e.id)}}},[t._v(t._s(e.name))])]}))],2)],1),i("mescroll-uni",{ref:"mescroll",attrs:{top:"200"},on:{getData:function(e){arguments[0]=e=t.$handleEvent(e),t.getList.apply(void 0,arguments)}}},[i("template",{attrs:{slot:"list"},slot:"list"},[t.dashboard_list.length>0?t._l(t.dashboard_list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"goods-class"},[i("v-uni-view",{staticClass:"goods-item"},[i("v-uni-view",{staticClass:"goods-item-title"},[i("v-uni-view",{staticClass:"title-ordernum"},[t._v("订单编号："+t._s(e.order_no))]),i("v-uni-view",{class:0==e.invoice_status?"title-orderactive":"title-ordertext"},[t._v(t._s(0==e.invoice_status?"未开票":"已开票"))])],1),i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("订单总额")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(e.order_money)+"元")])],1),i("v-uni-view",{staticClass:"goods-item-content",staticStyle:{"align-items":"center"}},[i("v-uni-view",{staticClass:"content-left"},[t._v("发票金额")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(e.invoice_money)+"元")]),e.invoice_delivery_money>0?i("v-uni-view",{staticClass:"content-last"},[t._v("发票邮寄费用："+t._s(e.invoice_delivery_money))]):t._e()],1),i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("发票类型")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(e.order_type_name))])],1),i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("发票抬头")]),i("v-uni-view",{staticClass:"content-right"},[t._v("发票抬头："+t._s(e.invoice_title)),i("br"),t._v("抬头类型："+t._s(1==e.invoice_title_type?"个人":"企业")),i("br"),t._v("发票内容："+t._s(e.invoice_content))])],1),i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("发票税率(%)")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(e.invoice_rate))])],1),i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("订单状态")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(e.order_status_name))])],1),i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("下单时间")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(t.$util.timeStampTurnTime(e.create_time)))])],1),e.invoice_time?i("v-uni-view",{staticClass:"goods-item-content"},[i("v-uni-view",{staticClass:"content-left"},[t._v("开票时间")]),i("v-uni-view",{staticClass:"content-right"},[t._v(t._s(t.$util.timeStampTurnTime(e.invoice_time)))])],1):t._e(),i("v-uni-view",{staticClass:"goods-btn"},[i("v-uni-button",{staticClass:"goods-btn-search",attrs:{type:"default",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onDetail(e.order_id)}}},[t._v("查看订单")]),0==e.invoice_status?i("v-uni-button",{staticClass:"goods-btn-item",attrs:{type:"default",size:"mini"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onEdit(e.order_id)}}},[t._v("开票")]):t._e()],1)],1)],1)})):t._e(),t.dashboard_list.length?t._e():i("ns-empty",{attrs:{text:"暂无商品数据"}})],2)],2)],1)},o=[]},6638:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.adjustOrderPrice=function(t){return n.default.post("/shopapi/order/adjustPrice",{data:t})},e.closeOrder=function(t){return n.default.post("/shopapi/order/close",{data:{order_id:t}})},e.deliveryOrder=function(t){return n.default.post("/shopapi/order/delivery",{data:t})},e.editOrderDelivery=function(t){return n.default.post("/shopapi/order/editOrderDelivery",{data:t})},e.editOrderInvoicelist=function(t){return n.default.post("/shopapi/order/invoiceEdit",{data:t})},e.getOrderCondition=function(){return n.default.get("/shopapi/order/condition")},e.getOrderDetailById=function(t){return n.default.post("/shopapi/order/getOrderDetail",{data:{order_id:t}})},e.getOrderDetailInfoById=function(t){return n.default.post("/shopapi/order/detail",{data:{order_id:t}})},e.getOrderGoodsList=function(t){return n.default.post("/shopapi/order/getOrderGoodsList",{data:{order_id:t}})},e.getOrderInfoById=function(t){return n.default.post("/shopapi/order/getOrderInfo",{data:{order_id:t}})},e.getOrderInvoicelist=function(t){return n.default.post("/shopapi/order/invoicelist",{data:t})},e.getOrderList=function(t){return n.default.post("/shopapi/order/lists",{data:t})},e.getOrderLog=function(t){return n.default.post("/shopapi/order/log",{data:{order_id:t}})},e.getOrderPackageList=function(t){return n.default.post("/shopapi/order/package",{data:{order_id:t}})},e.ordErtakeDelivery=function(t){return n.default.post("/shopapi/order/takeDelivery",{data:{order_id:t}})},e.orderExtendTakeDelivery=function(t){return n.default.post("/shopapi/order/extendtakedelivery",{data:{order_id:t}})},e.orderLocalorderDelivery=function(t){return n.default.post("/shopapi/localorder/delivery",{data:t})},e.orderOfflinePay=function(t){return n.default.post("/shopapi/order/offlinePay",{data:{order_id:t}})},e.orderVirtualDelivery=function(t){return n.default.post("/shopapi/virtualorder/delivery",{data:{order_id:t}})},e.storeOrderTakeDelivery=function(t){return n.default.post("/shopapi/Storeorder/storeOrderTakeDelivery",{data:{order_id:t}})};var n=a(i("9027"))},a8da:function(t,e,i){"use strict";i.r(e);var a=i("04d4"),n=i("b316");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("d6e4");var r=i("828b"),s=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"f941886e",null,!1,a["a"],void 0);e["default"]=s.exports},b316:function(t,e,i){"use strict";i.r(e);var a=i("cae0"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},cae0:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c223");var a=i("6638"),n={data:function(){return{search_text:"",array:["全部","普通订单","自提订单","外卖订单","虚拟订单"],statusList:[{id:0,name:"全部",invoice_status:""},{id:1,name:"未开票",invoice_status:0},{id:2,name:"已开票",invoice_status:1}],status:0,indexse:0,orderType:"",dashboard_list:[]}},onShow:function(){this.mescroll&&this.$refs.mescroll.refresh()},methods:{searchGoods:function(){this.$refs.mescroll.refresh()},tabChange:function(t){this.status=t,this.$refs.mescroll.refresh()},onDetail:function(t){this.$util.redirectTo("/pages/order/detail/basis",{order_id:t})},onEdit:function(t){this.$util.redirectTo("/pages/invoices/edit/edit",{order_id:t})},bindPickerChange:function(t){this.indexse=t.detail.value,0==t.detail.value?this.orderType="":this.orderType=t.detail.value,this.$refs.mescroll.refresh()},getList:function(t){var e=this,i={page:t.num,page_size:t.size,invoice_status:this.statusList[this.status].invoice_status,search_text:this.search_text,order_type:this.orderType};(0,a.getOrderInvoicelist)(i).then((function(i){var a=[],n=i.message;0==i.code&&i.data?(0==i.data.page_count&&(e.emptyShow=!0),a=i.data.list):e.$util.showToast({title:n}),t.endSuccess(a.length),1==t.num&&(e.dashboard_list=[]),e.dashboard_list=e.dashboard_list.concat(a),e.$refs.loadingCover&&e.$refs.loadingCover.hide()}))}}};e.default=n},d6e4:function(t,e,i){"use strict";var a=i("f2aa"),n=i.n(a);n.a},f2aa:function(t,e,i){var a=i("f860");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("0617c7fe",a,!0,{sourceMap:!1,shadowMode:!1})},f860:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-f941886e]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-f941886e]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-f941886e]{position:fixed;left:0;right:0;z-index:998}.search-wrap[data-v-f941886e]{display:flex;justify-content:space-between;padding:%?30?%;background-color:#fff}.search-wrap .search-input-inner[data-v-f941886e]{display:flex;align-items:center;width:%?460?%;height:%?70?%;padding:0 %?30?%;background-color:#f8f8f8;border-radius:%?100?%;box-sizing:border-box}.search-wrap .search-input-inner .search-input-icon[data-v-f941886e]{margin-right:%?10?%;color:#909399}.search-wrap .search-btn[data-v-f941886e]{display:flex;justify-content:center;align-items:center;width:%?200?%;height:%?70?%;color:#fff;border-radius:%?100?%}.search-wrap .search-btn uni-text[data-v-f941886e]{margin-right:%?10?%}.search[data-v-f941886e]{display:flex;padding:0 %?30?% %?30?%;justify-content:center;text-align:center}.search .search_input[data-v-f941886e]{padding:0 %?20?%;background-color:#fff;flex:1;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;display:flex;align-items:center}.search .search_input uni-input[data-v-f941886e]{height:%?70?%;line-height:%?70?%;border-radius:%?70?%;padding:0 %?20?%}.search .search_input .date[data-v-f941886e]{display:flex;align-items:center;flex:1;color:#909399}.search .search_input .date uni-picker[data-v-f941886e]{flex:1}.search .search_input .date uni-picker.start[data-v-f941886e]{margin-right:%?20?%!important}.search .search_input .date uni-picker.end[data-v-f941886e]{margin-left:%?20?%!important}.search .search_input .date .clear[data-v-f941886e]{min-width:%?60?%}.search .search_input .search_btn[data-v-f941886e]{min-width:%?60?%}.search .search_input .placeholder[data-v-f941886e]{font-size:%?28?%;color:#909399}.search .search_select[data-v-f941886e]{background-color:#fff;height:%?70?%;line-height:%?70?%;border-radius:%?70?%;width:%?160?%;color:#909399;font-size:%?24?%;display:flex;align-items:center;justify-content:center}.search .search_select > uni-text[data-v-f941886e]{margin-left:%?10?%}.select[data-v-f941886e]{height:%?68?%;line-height:%?68?%;border-radius:%?35?%;min-width:%?200?%;margin-left:%?30?%;padding:0 %?20?%;text-align:center;background:#fff;border:1px solid #ccc;display:flex;justify-content:space-between;align-items:center;font-size:%?28?%}.select uni-text[data-v-f941886e]{vertical-align:middle;font-size:%?28?%}.tab-block[data-v-f941886e]{display:flex;flex-direction:row;justify-content:space-between;background:#fff}.tab-block .choose[data-v-f941886e]{min-width:50px;background-color:#fff;padding:%?20?% %?0?% 0 %?20?%;height:%?66?%}.tab-block .tab-wrap[data-v-f941886e]{width:100%;height:%?66?%;background-color:#fff;display:flex;flex-direction:row;justify-content:space-around}.tab-block .active[data-v-f941886e]{position:relative}.tab-block .active[data-v-f941886e]::after{content:"";position:absolute;bottom:0;left:0;height:%?4?%;width:100%}.goods-class[data-v-f941886e]{margin:0 %?30?%}.goods-item[data-v-f941886e]{background:#fff;border-radius:%?10?%;margin-top:%?20?%;padding:%?30?% %?30?% %?40?%}.goods-item .goods-item-title[data-v-f941886e]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;height:%?70?%;border-bottom:1px solid #eee}.goods-item .goods-item-title .title-ordernum[data-v-f941886e]{font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#909399}.goods-item .goods-item-title .title-ordertext[data-v-f941886e]{font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#4456ff}.goods-item .goods-item-title .title-orderactive[data-v-f941886e]{font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#ff4544}.goods-item .goods-item-content[data-v-f941886e]{display:flex;flex-direction:row;padding-top:%?10?%}.goods-item .goods-item-content .content-left[data-v-f941886e]{font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#303133;min-width:%?160?%}.goods-item .goods-item-content .content-right[data-v-f941886e]{font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#303133;margin-left:%?80?%}.goods-item .goods-item-content .content-last[data-v-f941886e]{font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#ff6a00;margin-left:%?30?%}.goods-item .goods-btn[data-v-f941886e]{display:flex;flex-direction:row-reverse;margin-top:%?25?%}.goods-item .goods-btn .goods-btn-search[data-v-f941886e]{color:#303133;border-color:#909399;margin-left:%?20?%!important}.goods-item .goods-btn .goods-btn-item[data-v-f941886e]{color:#ff6a00;border-color:#ff6a00}',""]),t.exports=e}}]);