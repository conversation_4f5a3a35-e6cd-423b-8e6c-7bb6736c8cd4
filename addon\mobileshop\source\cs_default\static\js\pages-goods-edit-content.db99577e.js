(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods-edit-content"],{"0954":function(t,e,i){"use strict";i.r(e);var n=i("b097"),o=i("20c5");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("565c");var s=i("828b"),r=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"7f9d87e9",null,!1,n["a"],void 0);e["default"]=r.exports},"140a":function(t,e,i){var n=i("a42d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("967d").default;o("05b0f002",n,!0,{sourceMap:!1,shadowMode:!1})},"20c5":function(t,e,i){"use strict";i.r(e);var n=i("9f51"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"565c":function(t,e,i){"use strict";var n=i("140a"),o=i.n(n);o.a},"6b7d":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={nsEditor:i("0954").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("ns-editor",{ref:"edit",attrs:{placeholder:"请输入商品详情...",verify:"请输入商品详情",html:t.goodsContent},on:{editOk:function(e){arguments[0]=e=t.$handleEvent(e),t.editOk.apply(void 0,arguments)}}})],1)},a=[]},"9f51":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(i("2634")),a=n(i("2fdc"));i("5c47");var s={props:{showImgSize:{type:Boolean,default:!1},showImgToolbar:{type:Boolean,default:!1},showImgResize:{type:Boolean,default:!1},placeholder:{type:String,default:"请输入内容..."},verify:{type:String,default:"请输入内容"},html:{type:String,default:""}},computed:{},data:function(){return{isIphoneX:!1,imgCount:9999,showMoreTool:!1,showBold:!1,showItalic:!1,showIns:!1,showHeader:!1,showCenter:!1,showRight:!1,disabledDraftBox:!0,draftboxHtml:""}},created:function(){this.isIphoneX=this.$util.uniappIsIPhoneX()},methods:{onEditorReady:function(t){var e=this;uni.createSelectorQuery().in(this).select(".ql-container").fields({size:!0,context:!0},(function(t){e.editorCtx=t.context,e.html?(e.disabledDraftBox=!1,e.editorCtx.setContents({html:e.html})):e.draftboxHtml&&(e.disabledDraftBox=!1,e.editorCtx.setContents({html:e.draftboxHtml}))})).exec()},undo:function(){this.editorCtx.undo()},insertImage:function(){var t=this;this.$util.upload({number:this.imgCount,path:"image"},(function(e){for(var i=0;i<e.length;i++)t.editorCtx.insertImage({src:t.$util.img(e[i]),alt:"图片",success:function(t){}})}))},insertDivider:function(){this.editorCtx.insertDivider()},redo:function(){this.editorCtx.redo()},draftBox:function(){},showMore:function(){this.showMoreTool=!this.showMoreTool,this.editorCtx.setContents()},setBold:function(){this.showBold=!this.showBold,this.editorCtx.format("bold")},setItalic:function(){this.showItalic=!this.showItalic,this.editorCtx.format("italic")},checkStatus:function(t,e,i){e.hasOwnProperty(t)?this[i]=!0:this[i]=!1},statuschange:function(t){var e=t.detail;this.checkStatus("bold",e,"showBold"),this.checkStatus("italic",e,"showItalic"),this.checkStatus("ins",e,"showIns"),this.checkStatus("header",e,"showHeader"),e.hasOwnProperty("align")?"center"==e.align?(this.showCenter=!0,this.showRight=!1):"right"==e.align?(this.showCenter=!1,this.showRight=!0):(this.showCenter=!1,this.showRight=!1):(this.showCenter=!1,this.showRight=!1)},setIns:function(){this.showIns=!this.showIns,this.editorCtx.format("ins")},setHeader:function(){this.showHeader=!this.showHeader,this.editorCtx.format("header",!!this.showHeader&&"H2")},setCenter:function(){this.showCenter=!this.showCenter,this.editorCtx.format("align",!!this.showCenter&&"center")},setRight:function(){this.showRight=!this.showRight,this.editorCtx.format("align",!!this.showRight&&"right")},editInput:function(t){return(0,a.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},editFocus:function(){return(0,a.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},editBlur:function(){},save:function(){var t=this;this.editorCtx.getContents({success:function(e){var i=e.text.length;1!=i||"<p><br></p>"!=e.html?i<1||i>5e3?t.$util.showToast({title:"内容描述字符数应在1～5000之间"}):t.$emit("editOk",e):t.$util.showToast({title:t.verify})}})}}};e.default=s},a42d:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n * 建议使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n */.uni-line-hide[data-v-7f9d87e9]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.uni-using-hide[data-v-7f9d87e9]{overflow:hidden;width:100%;text-overflow:ellipsis;white-space:nowrap}.prevent-head-roll[data-v-7f9d87e9]{position:fixed;left:0;right:0;z-index:998}.container[data-v-7f9d87e9]{padding-right:0;padding-left:0;box-sizing:border-box;padding-top:%?100?%;padding-bottom:%?40?%}.ql-container[data-v-7f9d87e9]{line-height:160%;font-size:%?28?%;height:auto;background-color:#fff;padding:%?30?%;margin-top:%?20?%;margin-bottom:%?160?%}.ql-container.safe-area[data-v-7f9d87e9]{margin-bottom:%?200?%}.tool-view[data-v-7f9d87e9]{width:100vw;position:fixed;top:0;left:0;z-index:1}.tool[data-v-7f9d87e9]{height:%?100?%;display:flex;align-items:center;justify-content:space-around;width:100%;background:#fff}.iconfont[data-v-7f9d87e9]{font-size:%?44?%;color:#606266}.disabled[data-v-7f9d87e9]{color:#ccc}[data-v-7f9d87e9] .ql-editor.ql-blank:before{font-style:normal}.font-more[data-v-7f9d87e9]{position:absolute;left:0;top:%?100?%;display:flex;align-items:center;justify-content:space-around;width:100%;background:#fff;overflow:hidden;transition:all .15s}.footer-wrap[data-v-7f9d87e9]{position:fixed;background-color:#fff;width:100%;bottom:0;padding:%?40?% 0;z-index:1}.footer-wrap.safe-area[data-v-7f9d87e9]{padding-bottom:calc(constant(safe-area-inset-bottom) + %?100?%);padding-bottom:calc(env(safe-area-inset-bottom) + %?100?%)}',""]),t.exports=e},b097:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"container",style:{paddingTop:t.showMoreTool?"200rpx":"100rpx"}},[i("v-uni-view",{staticClass:"tool-view"},[i("v-uni-view",{staticClass:"tool"},[i("v-uni-view",{staticClass:"iconfont iconshangchuantupian",attrs:{title:"插入图片"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.insertImage.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconT",class:{"color-base-text":t.showMoreTool},attrs:{title:"修改文字样式"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showMore.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconfengexian",attrs:{title:"分割线"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.insertDivider.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconchexiao",attrs:{title:"撤销"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.undo.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconzhongzuo",attrs:{title:"重做"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.redo.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconqueding_queren",class:{disabled:t.disabledDraftBox},attrs:{title:"草稿箱"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.draftBox.apply(void 0,arguments)}}})],1),i("v-uni-view",{staticClass:"font-more",style:{height:t.showMoreTool?"100rpx":0}},[i("v-uni-view",{staticClass:"iconfont iconjiacu1",class:{"color-base-text":t.showBold},attrs:{title:"加粗"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setBold.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconzitixieti",class:{"color-base-text":t.showItalic},attrs:{title:"斜体"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setItalic.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconxiahuaxian1",class:{"color-base-text":t.showIns},attrs:{title:"下划线"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setIns.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconbiaotizhengwenqiehuan",class:{"color-base-text":t.showHeader},attrs:{title:"标题"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setHeader.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconjuzhongduiqi",class:{"color-base-text":t.showCenter},attrs:{title:"居中"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setCenter.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"iconfont iconjuyouduiqi",class:{"color-base-text":t.showRight},attrs:{title:"居右"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setRight.apply(void 0,arguments)}}})],1)],1),i("v-uni-editor",{ref:"editor",staticClass:"ql-container",class:{"safe-area":t.isIphoneX},attrs:{id:"editor",placeholder:t.placeholder,"show-img-size":!0,"show-img-toolbar":!0,"show-img-resize":!0},on:{ready:function(e){arguments[0]=e=t.$handleEvent(e),t.onEditorReady.apply(void 0,arguments)},statuschange:function(e){arguments[0]=e=t.$handleEvent(e),t.statuschange.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.editInput.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.editFocus.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.editBlur.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"footer-wrap",class:{"safe-area":t.isIphoneX}},[i("v-uni-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.save()}}},[t._v("保存")])],1)],1)},o=[]},ba42:function(t,e,i){"use strict";i.r(e);var n=i("6b7d"),o=i("f9c0");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);var s=i("828b"),r=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,"c8299a54",null,!1,n["a"],void 0);e["default"]=r.exports},d319:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(i("0954")),a={data:function(){return{goodsContent:""}},components:{nsEditor:o.default},onLoad:function(t){this.goodsContent=uni.getStorageSync("editGoodsContent")||""},onShow:function(){},methods:{editOk:function(t){uni.setStorageSync("editGoodsContent",t.html),uni.navigateBack({delta:1})}}};e.default=a},f9c0:function(t,e,i){"use strict";i.r(e);var n=i("d319"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a}}]);